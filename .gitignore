HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**
!**/src/test/**

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

### VS Code ###
.vscode/

### macOS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

### Logs ###
*.log
logs/

### Temporary files ###
*.tmp
*.temp
*~
*.bak
*.swp
*.swo

### Maven ###
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

### Application specific ###
application-local*.yml
application-dev*.yml
application-test*.yml

### Security sensitive files ###
.env
.env.*
*.key
*.pem
*.p12
*.jks
secrets/
certificates/
backup/

### Database dumps ###
*.sql
*.dump

### IDE and editor files ###
.vscode/settings.json
.idea/workspace.xml
