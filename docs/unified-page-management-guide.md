# 🎯 统一页面管理系统使用指南

## 📋 系统概述

统一页面管理系统提供了一个集中的入口，用于访问各种管理功能页面，包括：
- 访问日志管理
- 监控日志管理  
- Redis数据管理
- 集群监控管理
- 定时任务管理

## 🔧 技术实现

### 核心特性
- **动态路径配置**：使用`platform.oss.viewUrl`配置项，避免硬编码路径
- **安全验证**：基于AccessToken的安全验证机制
- **统一入口**：所有管理功能通过统一Controller访问
- **自动重定向**：验证失败自动重定向到登录页面

### 配置说明
系统使用`platform.oss.viewUrl`配置项作为基础URL：
```yaml
platform:
  oss:
    viewUrl: http://localhost:8000/api/
```

## 🚀 API接口说明

### 基础路径
```
/api/unified-page-management
```

### 接口列表

#### 1. 访问日志管理
- **登录页面**: `GET /access-log-login`
  - 重定向到: `{viewUrl}/access-log-management/login.html`
  
- **管理页面**: `GET /access-log-management?accessToken={token}`
  - 验证AccessToken后重定向到: `{viewUrl}/access-log-management/management.html?accessToken={token}`

#### 2. 监控日志管理
- **登录页面**: `GET /log-management-login`
  - 重定向到: `{viewUrl}/log-management/login`
  
- **管理页面**: `GET /log-management?accessToken={token}`
  - 验证AccessToken后重定向到: `{viewUrl}/log-management/log-login.html?accessToken={token}`

#### 3. Redis数据管理
- **登录页面**: `GET /redis-management-login`
  - 重定向到: `{viewUrl}/redis-management/login.html`
  
- **管理页面**: `GET /redis-management?accessToken={token}`
  - 验证AccessToken后重定向到: `{viewUrl}/redis-management/management.html?accessToken={token}`

#### 4. 集群监控管理
- **登录页面**: `GET /cluster-monitor-login`
  - 重定向到: `{viewUrl}/cluster-monitor/login.html`
  
- **管理页面**: `GET /cluster-monitor?accessToken={token}`
  - 验证AccessToken后重定向到: `{viewUrl}/cluster-monitor/index.html?accessToken={token}`

#### 5. 定时任务管理
- **登录页面**: `GET /quartz-management-login`
  - 重定向到: `{viewUrl}/quartz-management/login.html`
  
- **管理页面**: `GET /quartz-management?accessToken={token}`
  - 验证AccessToken后重定向到: `{viewUrl}/quartz-management/management.html?accessToken={token}`

## 🔐 安全机制

### AccessToken验证
- 所有管理页面都需要有效的AccessToken
- Token验证失败会自动重定向到对应的登录页面
- 使用`SecureTokenUtil`进行Token验证

### 验证流程
1. 用户访问管理页面URL
2. 系统验证AccessToken
3. 验证成功：重定向到管理页面
4. 验证失败：重定向到登录页面

## 📝 使用示例

### 访问访问日志管理
```bash
# 1. 访问登录页面
curl -L "http://localhost:8000/api/unified-page-management/access-log-login"

# 2. 获取AccessToken后访问管理页面
curl -L "http://localhost:8000/api/unified-page-management/access-log-management?accessToken=your_token_here"
```

### 访问Redis管理
```bash
# 1. 访问登录页面
curl -L "http://localhost:8000/api/unified-page-management/redis-management-login"

# 2. 获取AccessToken后访问管理页面
curl -L "http://localhost:8000/api/unified-page-management/redis-management?accessToken=your_token_here"
```

## 🛠️ 配置管理

### 动态路径配置
系统会根据`platform.oss.viewUrl`配置动态构建URL：

```java
// 配置示例
platform.oss.viewUrl = "http://localhost:8000/api/"

// 构建的URL示例
// 访问日志管理登录: http://localhost:8000/api/access-log-management/login.html
// Redis管理页面: http://localhost:8000/api/redis-management/management.html
```

### 备用方案
如果`platform.oss.viewUrl`未配置，系统会使用相对路径作为备用方案：
```
/api/{module}/{page}
```

## 🔍 日志监控

系统提供详细的日志记录：
- 页面访问请求日志
- AccessToken验证日志
- URL构建过程日志
- 重定向操作日志

### 日志示例
```
2025-08-04 10:08:00 INFO  - 访问日志管理登录页面请求
2025-08-04 10:08:01 INFO  - 使用配置的platform.oss.viewUrl构建管理URL: http://localhost:8000/api/
2025-08-04 10:08:02 INFO  - 构建的管理URL: http://localhost:8000/api/access-log-management/login.html
```

## 🚨 错误处理

### 常见错误及解决方案

1. **AccessToken验证失败**
   - 错误：Token无效或已过期
   - 解决：重新获取有效的AccessToken

2. **配置URL无效**
   - 错误：`platform.oss.viewUrl`配置错误
   - 解决：检查配置文件中的URL格式

3. **重定向失败**
   - 错误：目标页面不存在
   - 解决：确认目标页面路径正确

## 📈 扩展说明

### 添加新的管理功能
1. 在Controller中添加新的接口方法
2. 实现登录页面和管理页面的重定向逻辑
3. 使用相同的AccessToken验证机制
4. 更新文档说明

### 自定义配置
可以通过配置文件自定义：
- 密钥配置：`unified.page.management.secret-key`
- 基础URL：`platform.oss.viewUrl`

## 🎯 最佳实践

1. **安全性**：始终验证AccessToken
2. **日志记录**：记录关键操作和错误
3. **配置管理**：使用配置文件管理URL
4. **错误处理**：提供友好的错误重定向
5. **文档维护**：及时更新API文档

## 📞 技术支持

如有问题，请联系开发团队或查看相关日志文件进行排查。
