package com.haoys.user.service;

import com.haoys.user.model.SecureAppRequestLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全应用请求日志服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface SecureAppRequestLogService {
    
    /**
     * 记录请求日志
     */
    void logRequest(SecureAppRequestLog log);
    
    /**
     * 记录成功的请求
     * 
     * @param appId 应用ID
     * @param environment 环境
     * @param requestType 请求类型
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param requestData 请求数据
     * @param responseData 响应数据
     */
    void logSuccessRequest(String appId, String environment, String requestType, 
                          String clientIp, String userAgent, String requestData, String responseData);
    
    /**
     * 记录失败的请求
     * 
     * @param appId 应用ID
     * @param environment 环境
     * @param requestType 请求类型
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param requestData 请求数据
     * @param errorMessage 错误信息
     */
    void logFailedRequest(String appId, String environment, String requestType, 
                         String clientIp, String userAgent, String requestData, String errorMessage);
    
    /**
     * 根据条件查询日志列表
     */
    List<SecureAppRequestLog> getLogsByCondition(String appId, String environment, 
                                               String requestType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计今日请求次数
     */
    int countTodayRequests(String appId, String environment);
}
