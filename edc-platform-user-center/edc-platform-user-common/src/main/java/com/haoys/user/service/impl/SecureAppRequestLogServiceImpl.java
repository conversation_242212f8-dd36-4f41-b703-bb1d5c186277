package com.haoys.user.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.haoys.user.mapper.SecureAppRequestLogMapper;
import com.haoys.user.model.SecureAppRequestLog;
import com.haoys.user.service.SecureAppRequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全应用请求日志服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class SecureAppRequestLogServiceImpl implements SecureAppRequestLogService {
    
    @Autowired
    private SecureAppRequestLogMapper secureAppRequestLogMapper;
    
    @Override
    public void logRequest(SecureAppRequestLog logRecord) {
        if (logRecord == null) {
            log.warn("请求日志记录为空，跳过记录");
            return;
        }
        
        try {
            // 设置请求时间
            if (logRecord.getRequestTime() == null) {
                logRecord.setRequestTime(LocalDateTime.now());
            }
            
            secureAppRequestLogMapper.insertSelective(logRecord);
            log.debug("记录请求日志成功: appId={}, requestType={}, responseStatus={}", 
                     logRecord.getAppId(), logRecord.getRequestType(), logRecord.getResponseStatus());
        } catch (Exception e) {
            log.error("记录请求日志失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void logSuccessRequest(String appId, String environment, String requestType, 
                                 String clientIp, String userAgent, String requestData, String responseData) {
        SecureAppRequestLog logRecord = new SecureAppRequestLog()
                .setAppId(appId)
                .setEnvironment(environment)
                .setRequestType(requestType)
                .setClientIp(clientIp)
                .setUserAgent(userAgent)
                .setRequestTime(LocalDateTime.now())
                .setResponseStatus("success")
                .setRequestData(requestData)
                .setResponseData(responseData);
        
        logRequest(logRecord);
    }
    
    @Override
    public void logFailedRequest(String appId, String environment, String requestType, 
                                String clientIp, String userAgent, String requestData, String errorMessage) {
        SecureAppRequestLog logRecord = new SecureAppRequestLog()
                .setAppId(appId)
                .setEnvironment(environment)
                .setRequestType(requestType)
                .setClientIp(clientIp)
                .setUserAgent(userAgent)
                .setRequestTime(LocalDateTime.now())
                .setResponseStatus("failed")
                .setErrorMessage(errorMessage)
                .setRequestData(requestData);
        
        logRequest(logRecord);
    }
    
    @Override
    public List<SecureAppRequestLog> getLogsByCondition(String appId, String environment, 
                                                       String requestType, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return secureAppRequestLogMapper.selectByCondition(appId, environment, requestType, startTime, endTime);
        } catch (Exception e) {
            log.error("查询请求日志失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public int countTodayRequests(String appId, String environment) {
        try {
            return secureAppRequestLogMapper.countTodayRequests(appId, environment);
        } catch (Exception e) {
            log.error("统计今日请求次数失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
