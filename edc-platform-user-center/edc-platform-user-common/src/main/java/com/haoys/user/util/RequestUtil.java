package com.haoys.user.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求工具类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
public class RequestUtil {
    
    /**
     * 获取客户端IP地址
     * 
     * @return 客户端IP
     */
    public static String getClientIp() {
        try {
            HttpServletRequest request = getHttpServletRequest();
            if (request == null) {
                return "unknown";
            }
            
            String ip = request.getHeader("X-Forwarded-For");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 多次反向代理后会有多个IP值，第一个为真实IP
                int index = ip.indexOf(',');
                if (index != -1) {
                    return ip.substring(0, index);
                } else {
                    return ip;
                }
            }
            
            ip = request.getHeader("X-Real-IP");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
            
            ip = request.getHeader("Proxy-Client-IP");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
            
            ip = request.getHeader("WL-Proxy-Client-IP");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
            
            ip = request.getHeader("HTTP_CLIENT_IP");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
            
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip;
            }
            
            return request.getRemoteAddr();
        } catch (Exception e) {
            log.warn("获取客户端IP失败: {}", e.getMessage());
            return "unknown";
        }
    }
    
    /**
     * 获取用户代理
     * 
     * @return 用户代理字符串
     */
    public static String getUserAgent() {
        try {
            HttpServletRequest request = getHttpServletRequest();
            if (request == null) {
                return "unknown";
            }
            
            String userAgent = request.getHeader("User-Agent");
            return StrUtil.isNotBlank(userAgent) ? userAgent : "unknown";
        } catch (Exception e) {
            log.warn("获取用户代理失败: {}", e.getMessage());
            return "unknown";
        }
    }
    
    /**
     * 获取当前HTTP请求
     * 
     * @return HttpServletRequest对象
     */
    private static HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.warn("获取HttpServletRequest失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取请求URI
     * 
     * @return 请求URI
     */
    public static String getRequestUri() {
        try {
            HttpServletRequest request = getHttpServletRequest();
            return request != null ? request.getRequestURI() : "unknown";
        } catch (Exception e) {
            log.warn("获取请求URI失败: {}", e.getMessage());
            return "unknown";
        }
    }
    
    /**
     * 获取请求方法
     * 
     * @return 请求方法
     */
    public static String getRequestMethod() {
        try {
            HttpServletRequest request = getHttpServletRequest();
            return request != null ? request.getMethod() : "unknown";
        } catch (Exception e) {
            log.warn("获取请求方法失败: {}", e.getMessage());
            return "unknown";
        }
    }
}
