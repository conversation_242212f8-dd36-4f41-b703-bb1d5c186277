package com.haoys.user.security.interceptor;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.util.SecureTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * AccessToken验证拦截器
 * 
 * <p>此拦截器用于验证AccessToken相关接口的存在性，防止开发者删除或注释掉关键的安全验证流程。
 * 一旦没有AccessToken验证，数据管理和访问日志等模块数据存在泄露的风险。</p>
 * 
 * <p>主要功能：</p>
 * <ul>
 *   <li>检查SecureTokenService及其关键方法是否存在</li>
 *   <li>检查SecureTokenUtil及其验证方法是否存在</li>
 *   <li>验证AccessToken验证流程的完整性</li>
 *   <li>记录安全检查结果</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Component
public class AccessTokenValidationInterceptor implements HandlerInterceptor {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 是否启用AccessToken验证检查
     */
    @Value("${security.access-token.validation.enabled:true}")
    private boolean validationEnabled;
    
    /**
     * 需要检查的关键接口路径
     */
    private static final List<String> CRITICAL_PATHS = Arrays.asList(
        "/api/secure/token/generateCode",
        "/api/secure/token/getAccessToken",
        "/api/secure/token/validateToken",
        "/api/secure/token/refreshToken"
    );
    
    /**
     * 需要验证的关键服务方法
     */
    private static final List<String> CRITICAL_SERVICE_METHODS = Arrays.asList(
        "generateCode",
        "getAccessToken", 
        "validateAccessToken",
        "refreshAccessToken"
    );
    
    /**
     * 需要验证的工具类方法
     */
    private static final List<String> CRITICAL_UTIL_METHODS = Arrays.asList(
        "isValidAccessToken",
        "getUserIdFromToken",
        "getRemainingTime"
    );
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) 
            throws Exception {
        
        if (!validationEnabled) {
            log.debug("AccessToken验证检查已禁用");
            return true;
        }
        
        String requestURI = request.getRequestURI();
        
        // 只对关键路径进行检查
        if (!isCriticalPath(requestURI)) {
            return true;
        }
        
        log.debug("开始AccessToken安全验证检查: {}", requestURI);
        
        try {
            // 检查SecureTokenService是否存在及其方法完整性
            validateSecureTokenService();
            
            // 检查SecureTokenUtil是否存在及其方法完整性
            validateSecureTokenUtil();
            
            // 检查处理器方法是否存在
            validateHandlerMethod(handler);
            
            log.debug("AccessToken安全验证检查通过: {}", requestURI);
            return true;
            
        } catch (Exception e) {
            log.error("AccessToken安全验证检查失败: {} - {}", requestURI, e.getMessage());
            
            // 安全检查失败，拒绝请求
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":403,\"message\":\"安全验证失败：AccessToken验证组件不完整\"}");
            return false;
        }
    }
    
    /**
     * 判断是否为关键路径
     */
    private boolean isCriticalPath(String requestURI) {
        return CRITICAL_PATHS.stream().anyMatch(requestURI::contains);
    }
    
    /**
     * 验证SecureTokenService及其关键方法
     */
    private void validateSecureTokenService() {
        try {
            // 检查服务是否存在
            SecureTokenService secureTokenService = applicationContext.getBean(SecureTokenService.class);
            if (secureTokenService == null) {
                throw new SecurityException("SecureTokenService服务不存在");
            }
            
            // 检查关键方法是否存在
            Class<?> serviceClass = secureTokenService.getClass();
            for (String methodName : CRITICAL_SERVICE_METHODS) {
                boolean methodExists = Arrays.stream(serviceClass.getMethods())
                    .anyMatch(method -> method.getName().equals(methodName));
                
                if (!methodExists) {
                    throw new SecurityException("SecureTokenService关键方法不存在: " + methodName);
                }
            }
            
            log.debug("SecureTokenService验证通过");
            
        } catch (Exception e) {
            throw new SecurityException("SecureTokenService验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证SecureTokenUtil及其关键方法
     */
    private void validateSecureTokenUtil() {
        try {
            // 检查工具类是否存在
            SecureTokenUtil secureTokenUtil = applicationContext.getBean(SecureTokenUtil.class);
            if (secureTokenUtil == null) {
                throw new SecurityException("SecureTokenUtil工具类不存在");
            }
            
            // 检查关键方法是否存在
            Class<?> utilClass = secureTokenUtil.getClass();
            for (String methodName : CRITICAL_UTIL_METHODS) {
                boolean methodExists = Arrays.stream(utilClass.getMethods())
                    .anyMatch(method -> method.getName().equals(methodName));
                
                if (!methodExists) {
                    throw new SecurityException("SecureTokenUtil关键方法不存在: " + methodName);
                }
            }
            
            log.debug("SecureTokenUtil验证通过");
            
        } catch (Exception e) {
            throw new SecurityException("SecureTokenUtil验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证处理器方法
     */
    private void validateHandlerMethod(Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        
        // 检查方法是否被注释掉或删除
        if (method == null) {
            throw new SecurityException("处理器方法不存在");
        }
        
        // 检查方法是否为空实现（简单检查）
        String methodName = method.getName();
        if (StrUtil.isBlank(methodName)) {
            throw new SecurityException("处理器方法名为空");
        }
        
        log.debug("处理器方法验证通过: {}", methodName);
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) throws Exception {
        // 记录安全检查完成
        if (validationEnabled && isCriticalPath(request.getRequestURI())) {
            log.debug("AccessToken安全验证检查完成: {}", request.getRequestURI());
        }
    }
}
