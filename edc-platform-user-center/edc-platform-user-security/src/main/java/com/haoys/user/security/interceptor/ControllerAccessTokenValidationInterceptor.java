package com.haoys.user.security.interceptor;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 控制器AccessToken验证拦截器
 * 
 * <p>此拦截器专门用于检查指定控制器类中的AccessToken验证方法是否存在，
 * 防止开发者删除或注释掉关键的安全验证流程。</p>
 * 
 * <p>检查的控制器包括：</p>
 * <ul>
 *   <li>ClusterMonitorController - 集群监控控制器</li>
 *   <li>AccessLogManagementController - 访问日志管理控制器</li>
 *   <li>LogViewerManagementController - 日志查看管理控制器</li>
 *   <li>QuartzManagementViewController - 定时任务管理视图控制器</li>
 *   <li>RedisManagementController - Redis管理控制器</li>
 *   <li>SystemMonitorController - 系统监控控制器</li>
 *   <li>SystemMonitorViewController - 系统监控视图控制器</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Component
public class ControllerAccessTokenValidationInterceptor implements HandlerInterceptor {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 是否启用控制器AccessToken验证检查
     */
    @Value("${security.controller.access-token.validation.enabled:true}")
    private boolean validationEnabled;
    
    /**
     * 需要检查的控制器类名及其对应的路径模式
     */
    private static final Map<String, List<String>> CRITICAL_CONTROLLERS = new HashMap<String, List<String>>() {{
        put("ClusterMonitorController", Arrays.asList("/api/monitor/cluster", "/api/cluster"));
        put("AccessLogManagementController", Arrays.asList("/api/monitor/access-log", "/api/access-log"));
        put("LogViewerManagementController", Arrays.asList("/api/monitor/log-viewer", "/api/log-viewer"));
        put("QuartzManagementViewController", Arrays.asList("/api/monitor/quartz", "/api/quartz"));
        put("RedisManagementController", Arrays.asList("/api/monitor/redis", "/api/redis"));
        put("SystemMonitorController", Arrays.asList("/api/monitor/system", "/api/system-monitor"));
        put("SystemMonitorViewController", Arrays.asList("/api/monitor/system-view", "/api/system-monitor-view"));
    }};
    
    /**
     * 需要验证的AccessToken相关方法名
     */
    private static final List<String> ACCESS_TOKEN_METHODS = Arrays.asList(
        "validateAccessToken",
        "checkAccessToken", 
        "verifyAccessToken",
        "isValidAccessToken",
        "validateToken",
        "checkToken",
        "verifyToken"
    );
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) 
            throws Exception {
        
        if (!validationEnabled) {
            log.debug("控制器AccessToken验证检查已禁用");
            return true;
        }
        
        String requestURI = request.getRequestURI();
        
        // 检查是否为需要验证的控制器路径
        String controllerName = getMatchingController(requestURI);
        if (StrUtil.isBlank(controllerName)) {
            return true; // 不是关键控制器，直接通过
        }
        
        log.debug("开始控制器AccessToken验证检查: {} -> {}", requestURI, controllerName);
        
        try {
            // 验证控制器是否存在
            validateControllerExists(controllerName);
            
            // 验证AccessToken验证方法是否存在
            validateAccessTokenMethods(controllerName);
            
            // 验证处理器方法
            validateHandlerMethod(handler, controllerName);
            
            log.debug("控制器AccessToken验证检查通过: {}", controllerName);
            return true;
            
        } catch (Exception e) {
            log.error("控制器AccessToken验证检查失败: {} - {}", controllerName, e.getMessage());
            
            // 安全检查失败，拒绝请求
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"code\":403,\"message\":\"安全验证失败：控制器 %s 的AccessToken验证组件不完整\"}", 
                controllerName));
            return false;
        }
    }
    
    /**
     * 根据请求URI匹配对应的控制器
     */
    private String getMatchingController(String requestURI) {
        for (Map.Entry<String, List<String>> entry : CRITICAL_CONTROLLERS.entrySet()) {
            String controllerName = entry.getKey();
            List<String> pathPatterns = entry.getValue();
            
            for (String pattern : pathPatterns) {
                if (requestURI.contains(pattern)) {
                    return controllerName;
                }
            }
        }
        return null;
    }
    
    /**
     * 验证控制器是否存在
     */
    private void validateControllerExists(String controllerName) {
        try {
            // 尝试通过不同的方式获取控制器Bean
            Object controller = null;
            
            // 方式1：直接通过类名获取
            try {
                controller = applicationContext.getBean(controllerName);
            } catch (Exception e) {
                // 忽略，尝试其他方式
            }
            
            // 方式2：通过首字母小写的类名获取
            if (controller == null) {
                try {
                    String beanName = Character.toLowerCase(controllerName.charAt(0)) + controllerName.substring(1);
                    controller = applicationContext.getBean(beanName);
                } catch (Exception e) {
                    // 忽略，尝试其他方式
                }
            }
            
            // 方式3：通过类型获取
            if (controller == null) {
                try {
                    String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
                    for (String beanName : beanNames) {
                        Object bean = applicationContext.getBean(beanName);
                        if (bean.getClass().getSimpleName().equals(controllerName)) {
                            controller = bean;
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 忽略
                }
            }
            
            if (controller == null) {
                throw new SecurityException("控制器不存在: " + controllerName);
            }
            
            log.debug("控制器存在验证通过: {} -> {}", controllerName, controller.getClass().getName());
            
        } catch (Exception e) {
            throw new SecurityException("控制器验证失败: " + controllerName + " - " + e.getMessage());
        }
    }
    
    /**
     * 验证AccessToken验证方法是否存在
     */
    private void validateAccessTokenMethods(String controllerName) {
        try {
            // 获取控制器实例
            Object controller = getControllerInstance(controllerName);
            if (controller == null) {
                throw new SecurityException("无法获取控制器实例: " + controllerName);
            }
            
            Class<?> controllerClass = controller.getClass();
            Method[] methods = controllerClass.getMethods();
            
            // 检查是否存在AccessToken验证方法
            boolean hasAccessTokenMethod = false;
            for (Method method : methods) {
                String methodName = method.getName();
                for (String tokenMethod : ACCESS_TOKEN_METHODS) {
                    if (methodName.contains(tokenMethod) || methodName.toLowerCase().contains("token")) {
                        hasAccessTokenMethod = true;
                        log.debug("找到AccessToken验证方法: {}.{}", controllerName, methodName);
                        break;
                    }
                }
                if (hasAccessTokenMethod) break;
            }
            
            if (!hasAccessTokenMethod) {
                // 检查是否有注解或其他验证机制
                boolean hasSecurityAnnotation = checkSecurityAnnotations(controllerClass);
                if (!hasSecurityAnnotation) {
                    throw new SecurityException("控制器缺少AccessToken验证方法: " + controllerName);
                }
            }
            
            log.debug("AccessToken验证方法检查通过: {}", controllerName);
            
        } catch (Exception e) {
            throw new SecurityException("AccessToken验证方法检查失败: " + controllerName + " - " + e.getMessage());
        }
    }
    
    /**
     * 获取控制器实例
     */
    private Object getControllerInstance(String controllerName) {
        try {
            // 尝试多种方式获取控制器实例
            Object controller = null;
            
            try {
                controller = applicationContext.getBean(controllerName);
            } catch (Exception e) {
                String beanName = Character.toLowerCase(controllerName.charAt(0)) + controllerName.substring(1);
                controller = applicationContext.getBean(beanName);
            }
            
            return controller;
        } catch (Exception e) {
            log.warn("无法获取控制器实例: {} - {}", controllerName, e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查安全注解
     */
    private boolean checkSecurityAnnotations(Class<?> controllerClass) {
        try {
            // 检查类级别的安全注解
            if (controllerClass.isAnnotationPresent(org.springframework.security.access.prepost.PreAuthorize.class) ||
                controllerClass.isAnnotationPresent(org.springframework.web.bind.annotation.RestController.class)) {
                return true;
            }
            
            // 检查方法级别的安全注解
            Method[] methods = controllerClass.getMethods();
            for (Method method : methods) {
                if (method.isAnnotationPresent(org.springframework.security.access.prepost.PreAuthorize.class)) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            log.warn("检查安全注解失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证处理器方法
     */
    private void validateHandlerMethod(Object handler, String controllerName) {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        
        if (method == null) {
            throw new SecurityException("处理器方法不存在: " + controllerName);
        }
        
        // 检查方法是否属于预期的控制器
        String actualControllerName = handlerMethod.getBeanType().getSimpleName();
        if (!actualControllerName.equals(controllerName)) {
            log.warn("控制器名称不匹配: 预期={}, 实际={}", controllerName, actualControllerName);
        }
        
        log.debug("处理器方法验证通过: {}.{}", controllerName, method.getName());
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) throws Exception {
        // 记录验证完成
        if (validationEnabled) {
            String controllerName = getMatchingController(request.getRequestURI());
            if (StrUtil.isNotBlank(controllerName)) {
                log.debug("控制器AccessToken验证检查完成: {} -> {}", request.getRequestURI(), controllerName);
            }
        }
    }
}
