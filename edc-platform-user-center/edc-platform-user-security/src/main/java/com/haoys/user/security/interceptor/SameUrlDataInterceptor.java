package com.haoys.user.security.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.filter.RepeatedlyRequestWrapper;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.HttpHelper;
import com.haoys.user.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 判断请求url和数据是否和上一次相同，
 * 如果和上次相同，则是重复提交表单。
 *
 * 重构优化：
 * 1. 增加日志记录和监控
 * 2. 优化缓存key生成策略，使用MD5避免key过长
 * 3. 增加异常处理和降级策略
 * 4. 优化性能，减少不必要的序列化
 * 5. 增加安全性检查
 *
 * <AUTHOR>
 */
@Component
public class SameUrlDataInterceptor extends RepeatSubmitInterceptor {

    private static final Logger log = LoggerFactory.getLogger(SameUrlDataInterceptor.class);

    // 缓存key常量
    private static final String REPEAT_PARAMS = "repeatParams";
    private static final String REPEAT_TIME = "repeatTime";
    private static final String REPEAT_USER = "repeatUser";

    // 最大参数长度限制，防止恶意请求
    private static final int MAX_PARAMS_LENGTH = 10240; // 10KB

    // 最大缓存时间限制
    private static final long MAX_CACHE_TIME = TimeUnit.HOURS.toMillis(1); // 1小时

    // 令牌自定义标识
    @Value("${jwt.tokenHeader:Authorization}")
    private String header;

    @Autowired
    private RedisTemplateService redisTemplateService;

    @SuppressWarnings("unchecked")
    @Override
    public boolean isRepeatSubmit(HttpServletRequest request, NoRepeatSubmit annotation) {
        try {
            // 获取请求参数
            String nowParams = extractRequestParams(request);

            // 安全检查：参数长度限制
            if (nowParams.length() > MAX_PARAMS_LENGTH) {
                log.warn("请求参数过长，可能存在恶意攻击，URL: {}, 参数长度: {}",
                    request.getRequestURI(), nowParams.length());
                return true; // 拒绝处理
            }

            // 构建当前请求数据
            Map<String, Object> nowDataMap = buildRequestDataMap(nowParams, request);

            // 生成缓存key
            String cacheRepeatKey = generateCacheKey(request, nowParams);

            // 检查是否重复提交
            if (isRepeatRequest(cacheRepeatKey, nowDataMap, annotation)) {
                log.info("检测到重复提交，URL: {}, 用户: {}",
                    request.getRequestURI(), getCurrentUser(request));
                return true;
            }

            // 存储当前请求信息
            storeCacheData(cacheRepeatKey, nowDataMap, annotation);
            return false;

        } catch (Exception e) {
            log.error("防重复提交检查异常，URL: {}, 错误: {}",
                request.getRequestURI(), e.getMessage(), e);
            // 异常情况下采用保守策略，允许请求通过但记录日志
            return false;
        }
    }

    /**
     * 提取请求参数
     */
    private String extractRequestParams(HttpServletRequest request) {
        String params = "";

        // 优先获取body参数
        if (request instanceof RepeatedlyRequestWrapper) {
            RepeatedlyRequestWrapper repeatedlyRequest = (RepeatedlyRequestWrapper) request;
            params = HttpHelper.getBodyString(repeatedlyRequest);
        }

        // body参数为空时，获取Parameter数据
        if (StringUtils.isEmpty(params)) {
            params = JSONObject.toJSONString(request.getParameterMap());
        }

        return StringUtils.trimToEmpty(params);
    }

    /**
     * 构建请求数据映射
     */
    private Map<String, Object> buildRequestDataMap(String params, HttpServletRequest request) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(REPEAT_PARAMS, params);
        dataMap.put(REPEAT_TIME, System.currentTimeMillis());
        dataMap.put(REPEAT_USER, getCurrentUser(request));
        return dataMap;
    }

    /**
     * 生成缓存key，使用MD5避免key过长
     */
    private String generateCacheKey(HttpServletRequest request, String params) {
        String url = request.getRequestURI();
        String submitKey = StringUtils.trimToEmpty(request.getHeader(header));
        String userInfo = getCurrentUser(request);

        // 构建原始key
        String rawKey = url + "|" + submitKey + "|" + userInfo + "|" + params;

        // 使用MD5生成固定长度的key，避免Redis key过长
        String hashedKey = DigestUtils.md5DigestAsHex(rawKey.getBytes(StandardCharsets.UTF_8));

        return Constants.REPEAT_SUBMIT_KEY + hashedKey;
    }

    /**
     * 获取当前用户标识
     */
    private String getCurrentUser(HttpServletRequest request) {
        // 尝试从多个地方获取用户标识
        String user = request.getHeader("X-User-Id");
        if (StringUtils.isEmpty(user)) {
            user = request.getHeader("X-User-Name");
        }
        if (StringUtils.isEmpty(user)) {
            user = request.getRemoteAddr(); // 使用IP作为备选
        }
        return StringUtils.trimToEmpty(user);
    }

    /**
     * 检查是否重复请求
     */
    private boolean isRepeatRequest(String cacheKey, Map<String, Object> nowDataMap, NoRepeatSubmit annotation) {
        try {
            Object cacheObj = redisTemplateService.get(cacheKey);
            if (cacheObj == null) {
                return false;
            }

            Map<String, Object> preDataMap = (Map<String, Object>) cacheObj;

            // 检查参数是否相同
            if (!compareParams(nowDataMap, preDataMap)) {
                return false;
            }

            // 检查时间间隔
            return compareTime(nowDataMap, preDataMap, annotation.interval());

        } catch (Exception e) {
            log.error("检查重复请求异常，缓存key: {}, 错误: {}", cacheKey, e.getMessage());
            return false; // 异常时允许请求通过
        }
    }

    /**
     * 存储缓存数据
     */
    private void storeCacheData(String cacheKey, Map<String, Object> dataMap, NoRepeatSubmit annotation) {
        try {
            // 限制缓存时间，防止缓存过期时间过长
            long cacheTime = Math.min(annotation.interval(), MAX_CACHE_TIME);
            redisTemplateService.set(cacheKey, dataMap, cacheTime);

        } catch (Exception e) {
            log.error("存储防重复提交缓存失败，缓存key: {}, 错误: {}", cacheKey, e.getMessage());
            // 缓存失败不影响业务流程
        }
    }

    /**
     * 判断参数是否相同（优化版本）
     */
    private boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap) {
        try {
            String nowParams = (String) nowMap.get(REPEAT_PARAMS);
            String preParams = (String) preMap.get(REPEAT_PARAMS);

            // 空值检查
            if (nowParams == null || preParams == null) {
                return false;
            }

            return nowParams.equals(preParams);
        } catch (Exception e) {
            log.error("比较请求参数异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断两次间隔时间（优化版本）
     */
    private boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap, int interval) {
        try {
            Object nowTimeObj = nowMap.get(REPEAT_TIME);
            Object preTimeObj = preMap.get(REPEAT_TIME);

            if (nowTimeObj == null || preTimeObj == null) {
                return false;
            }

            long nowTime = Long.parseLong(nowTimeObj.toString());
            long preTime = Long.parseLong(preTimeObj.toString());

            // 时间间隔检查
            long timeDiff = nowTime - preTime;

            // 防止时间倒流攻击
            if (timeDiff < 0) {
                log.warn("检测到时间倒流，可能存在攻击行为，时间差: {}ms", timeDiff);
                return true;
            }

            return timeDiff < interval;

        } catch (NumberFormatException e) {
            log.error("时间格式转换异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("比较时间间隔异常: {}", e.getMessage());
            return false;
        }
    }
}
