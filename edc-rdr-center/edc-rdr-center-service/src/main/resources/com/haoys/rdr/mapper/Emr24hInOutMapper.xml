<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.Emr24hInOutMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.Emr24hInOut">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="in_days" jdbcType="INTEGER" property="inDays" />
    <result column="admission_status" jdbcType="VARCHAR" property="admissionStatus" />
    <result column="admission_diagnosis" jdbcType="VARCHAR" property="admissionDiagnosis" />
    <result column="treat_process" jdbcType="VARCHAR" property="treatProcess" />
    <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint" />
    <result column="discharge_status" jdbcType="VARCHAR" property="dischargeStatus" />
    <result column="discharge_diagnosis" jdbcType="VARCHAR" property="dischargeDiagnosis" />
    <result column="discharge_order" jdbcType="VARCHAR" property="dischargeOrder" />
    <result column="discharge_date_time" jdbcType="TIMESTAMP" property="dischargeDateTime" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="doctor_sign" jdbcType="VARCHAR" property="doctorSign" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "name", "admission_date_time",
    "in_days", "admission_status", "admission_diagnosis", "treat_process", "chief_complaint",
    "discharge_status", "discharge_diagnosis", "discharge_order", "discharge_date_time",
    "record_time", "doctor_sign", "source_path", "pk_id", "data_state", "full_text",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.Emr24hInOutExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_24h_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.Emr24hInOutExample">
    delete from "public"."emr_24h_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.Emr24hInOut">
    insert into "public"."emr_24h_in_out" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "name", "admission_date_time",
      "in_days", "admission_status", "admission_diagnosis",
      "treat_process", "chief_complaint", "discharge_status",
      "discharge_diagnosis", "discharge_order", "discharge_date_time",
      "record_time", "doctor_sign", "source_path",
      "pk_id", "data_state", "full_text",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP},
      #{inDays,jdbcType=INTEGER}, #{admissionStatus,jdbcType=VARCHAR}, #{admissionDiagnosis,jdbcType=VARCHAR},
      #{treatProcess,jdbcType=VARCHAR}, #{chiefComplaint,jdbcType=VARCHAR}, #{dischargeStatus,jdbcType=VARCHAR},
      #{dischargeDiagnosis,jdbcType=VARCHAR}, #{dischargeOrder,jdbcType=VARCHAR}, #{dischargeDateTime,jdbcType=TIMESTAMP},
      #{recordTime,jdbcType=TIMESTAMP}, #{doctorSign,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.Emr24hInOut">
    insert into "public"."emr_24h_in_out"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="inDays != null">
        "in_days",
      </if>
      <if test="admissionStatus != null">
        "admission_status",
      </if>
      <if test="admissionDiagnosis != null">
        "admission_diagnosis",
      </if>
      <if test="treatProcess != null">
        "treat_process",
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint",
      </if>
      <if test="dischargeStatus != null">
        "discharge_status",
      </if>
      <if test="dischargeDiagnosis != null">
        "discharge_diagnosis",
      </if>
      <if test="dischargeOrder != null">
        "discharge_order",
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="doctorSign != null">
        "doctor_sign",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inDays != null">
        #{inDays,jdbcType=INTEGER},
      </if>
      <if test="admissionStatus != null">
        #{admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="admissionDiagnosis != null">
        #{admissionDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="treatProcess != null">
        #{treatProcess,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="dischargeStatus != null">
        #{dischargeStatus,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDiagnosis != null">
        #{dischargeDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="dischargeOrder != null">
        #{dischargeOrder,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDateTime != null">
        #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.Emr24hInOutExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_24h_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_24h_in_out"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inDays != null">
        "in_days" = #{record.inDays,jdbcType=INTEGER},
      </if>
      <if test="record.admissionStatus != null">
        "admission_status" = #{record.admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDiagnosis != null">
        "admission_diagnosis" = #{record.admissionDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="record.treatProcess != null">
        "treat_process" = #{record.treatProcess,jdbcType=VARCHAR},
      </if>
      <if test="record.chiefComplaint != null">
        "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeStatus != null">
        "discharge_status" = #{record.dischargeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDiagnosis != null">
        "discharge_diagnosis" = #{record.dischargeDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeOrder != null">
        "discharge_order" = #{record.dischargeOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDateTime != null">
        "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorSign != null">
        "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_24h_in_out"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "in_days" = #{record.inDays,jdbcType=INTEGER},
      "admission_status" = #{record.admissionStatus,jdbcType=VARCHAR},
      "admission_diagnosis" = #{record.admissionDiagnosis,jdbcType=VARCHAR},
      "treat_process" = #{record.treatProcess,jdbcType=VARCHAR},
      "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      "discharge_status" = #{record.dischargeStatus,jdbcType=VARCHAR},
      "discharge_diagnosis" = #{record.dischargeDiagnosis,jdbcType=VARCHAR},
      "discharge_order" = #{record.dischargeOrder,jdbcType=VARCHAR},
      "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.Emr24hInOut">
    update "public"."emr_24h_in_out"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inDays != null">
        "in_days" = #{inDays,jdbcType=INTEGER},
      </if>
      <if test="admissionStatus != null">
        "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="admissionDiagnosis != null">
        "admission_diagnosis" = #{admissionDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="treatProcess != null">
        "treat_process" = #{treatProcess,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="dischargeStatus != null">
        "discharge_status" = #{dischargeStatus,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDiagnosis != null">
        "discharge_diagnosis" = #{dischargeDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="dischargeOrder != null">
        "discharge_order" = #{dischargeOrder,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.Emr24hInOut">
    update "public"."emr_24h_in_out"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "in_days" = #{inDays,jdbcType=INTEGER},
      "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      "admission_diagnosis" = #{admissionDiagnosis,jdbcType=VARCHAR},
      "treat_process" = #{treatProcess,jdbcType=VARCHAR},
      "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      "discharge_status" = #{dischargeStatus,jdbcType=VARCHAR},
      "discharge_diagnosis" = #{dischargeDiagnosis,jdbcType=VARCHAR},
      "discharge_order" = #{dischargeOrder,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
