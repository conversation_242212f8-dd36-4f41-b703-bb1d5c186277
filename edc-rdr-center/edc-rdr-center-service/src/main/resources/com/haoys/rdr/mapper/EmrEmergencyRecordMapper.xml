<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrEmergencyRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrEmergencyRecord">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="emergency_note" jdbcType="VARCHAR" property="emergencyNote" />
    <result column="emergency_personnel" jdbcType="VARCHAR" property="emergencyPersonnel" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="doctor_sign" jdbcType="VARCHAR" property="doctorSign" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "emergency_note", "emergency_personnel",
    "record_time", "doctor_sign", "source_path", "pk_id", "data_state", "full_text",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrEmergencyRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_emergency_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_emergency_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_emergency_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrEmergencyRecordExample">
    delete from "public"."emr_emergency_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrEmergencyRecord">
    insert into "public"."emr_emergency_record" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "emergency_note", "emergency_personnel",
      "record_time", "doctor_sign", "source_path",
      "pk_id", "data_state", "full_text",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{emergencyNote,jdbcType=VARCHAR}, #{emergencyPersonnel,jdbcType=VARCHAR},
      #{recordTime,jdbcType=TIMESTAMP}, #{doctorSign,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrEmergencyRecord">
    insert into "public"."emr_emergency_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="emergencyNote != null">
        "emergency_note",
      </if>
      <if test="emergencyPersonnel != null">
        "emergency_personnel",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="doctorSign != null">
        "doctor_sign",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="emergencyNote != null">
        #{emergencyNote,jdbcType=VARCHAR},
      </if>
      <if test="emergencyPersonnel != null">
        #{emergencyPersonnel,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrEmergencyRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_emergency_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_emergency_record"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.emergencyNote != null">
        "emergency_note" = #{record.emergencyNote,jdbcType=VARCHAR},
      </if>
      <if test="record.emergencyPersonnel != null">
        "emergency_personnel" = #{record.emergencyPersonnel,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorSign != null">
        "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_emergency_record"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "emergency_note" = #{record.emergencyNote,jdbcType=VARCHAR},
      "emergency_personnel" = #{record.emergencyPersonnel,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrEmergencyRecord">
    update "public"."emr_emergency_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="emergencyNote != null">
        "emergency_note" = #{emergencyNote,jdbcType=VARCHAR},
      </if>
      <if test="emergencyPersonnel != null">
        "emergency_personnel" = #{emergencyPersonnel,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrEmergencyRecord">
    update "public"."emr_emergency_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "emergency_note" = #{emergencyNote,jdbcType=VARCHAR},
      "emergency_personnel" = #{emergencyPersonnel,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
