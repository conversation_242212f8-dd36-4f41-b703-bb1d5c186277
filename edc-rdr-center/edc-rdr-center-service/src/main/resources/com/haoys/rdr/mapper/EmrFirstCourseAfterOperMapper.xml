<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrFirstCourseAfterOperMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrFirstCourseAfterOper">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operation_duration" jdbcType="VARCHAR" property="operationDuration" />
    <result column="diag_perioperation" jdbcType="VARCHAR" property="diagPerioperation" />
    <result column="anesthesia_method" jdbcType="VARCHAR" property="anesthesiaMethod" />
    <result column="operation_approach" jdbcType="VARCHAR" property="operationApproach" />
    <result column="operation_process" jdbcType="VARCHAR" property="operationProcess" />
    <result column="post_operation" jdbcType="VARCHAR" property="postOperation" />
    <result column="post_op_consideration" jdbcType="VARCHAR" property="postOpConsideration" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="doctor_sign" jdbcType="VARCHAR" property="doctorSign" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "operation_time", "operation_duration",
    "diag_perioperation", "anesthesia_method", "operation_approach", "operation_process",
    "post_operation", "post_op_consideration", "record_time", "doctor_sign", "source_path",
    "pk_id", "data_state", "full_text", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOperExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_first_course_after_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_first_course_after_oper"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_first_course_after_oper"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOperExample">
    delete from "public"."emr_first_course_after_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOper">
    insert into "public"."emr_first_course_after_oper" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "operation_time", "operation_duration",
      "diag_perioperation", "anesthesia_method", "operation_approach",
      "operation_process", "post_operation", "post_op_consideration",
      "record_time", "doctor_sign", "source_path",
      "pk_id", "data_state", "full_text",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, #{operationDuration,jdbcType=VARCHAR},
      #{diagPerioperation,jdbcType=VARCHAR}, #{anesthesiaMethod,jdbcType=VARCHAR}, #{operationApproach,jdbcType=VARCHAR},
      #{operationProcess,jdbcType=VARCHAR}, #{postOperation,jdbcType=VARCHAR}, #{postOpConsideration,jdbcType=VARCHAR},
      #{recordTime,jdbcType=TIMESTAMP}, #{doctorSign,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOper">
    insert into "public"."emr_first_course_after_oper"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="operationTime != null">
        "operation_time",
      </if>
      <if test="operationDuration != null">
        "operation_duration",
      </if>
      <if test="diagPerioperation != null">
        "diag_perioperation",
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method",
      </if>
      <if test="operationApproach != null">
        "operation_approach",
      </if>
      <if test="operationProcess != null">
        "operation_process",
      </if>
      <if test="postOperation != null">
        "post_operation",
      </if>
      <if test="postOpConsideration != null">
        "post_op_consideration",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="doctorSign != null">
        "doctor_sign",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationDuration != null">
        #{operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="diagPerioperation != null">
        #{diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="operationApproach != null">
        #{operationApproach,jdbcType=VARCHAR},
      </if>
      <if test="operationProcess != null">
        #{operationProcess,jdbcType=VARCHAR},
      </if>
      <if test="postOperation != null">
        #{postOperation,jdbcType=VARCHAR},
      </if>
      <if test="postOpConsideration != null">
        #{postOpConsideration,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOperExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_first_course_after_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_first_course_after_oper"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.operationTime != null">
        "operation_time" = #{record.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationDuration != null">
        "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPerioperation != null">
        "diag_perioperation" = #{record.diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaMethod != null">
        "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.operationApproach != null">
        "operation_approach" = #{record.operationApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.operationProcess != null">
        "operation_process" = #{record.operationProcess,jdbcType=VARCHAR},
      </if>
      <if test="record.postOperation != null">
        "post_operation" = #{record.postOperation,jdbcType=VARCHAR},
      </if>
      <if test="record.postOpConsideration != null">
        "post_op_consideration" = #{record.postOpConsideration,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorSign != null">
        "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_first_course_after_oper"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "operation_time" = #{record.operationTime,jdbcType=TIMESTAMP},
      "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      "diag_perioperation" = #{record.diagPerioperation,jdbcType=VARCHAR},
      "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      "operation_approach" = #{record.operationApproach,jdbcType=VARCHAR},
      "operation_process" = #{record.operationProcess,jdbcType=VARCHAR},
      "post_operation" = #{record.postOperation,jdbcType=VARCHAR},
      "post_op_consideration" = #{record.postOpConsideration,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOper">
    update "public"."emr_first_course_after_oper"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        "operation_time" = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diagPerioperation != null">
        "diag_perioperation" = #{diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="operationApproach != null">
        "operation_approach" = #{operationApproach,jdbcType=VARCHAR},
      </if>
      <if test="operationProcess != null">
        "operation_process" = #{operationProcess,jdbcType=VARCHAR},
      </if>
      <if test="postOperation != null">
        "post_operation" = #{postOperation,jdbcType=VARCHAR},
      </if>
      <if test="postOpConsideration != null">
        "post_op_consideration" = #{postOpConsideration,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrFirstCourseAfterOper">
    update "public"."emr_first_course_after_oper"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "operation_time" = #{operationTime,jdbcType=TIMESTAMP},
      "diag_perioperation" = #{diagPerioperation,jdbcType=VARCHAR},
      "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      "operation_approach" = #{operationApproach,jdbcType=VARCHAR},
      "operation_process" = #{operationProcess,jdbcType=VARCHAR},
      "post_operation" = #{postOperation,jdbcType=VARCHAR},
      "post_op_consideration" = #{postOpConsideration,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
