<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrFirstCourseMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrFirstCourse">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="case_features" jdbcType="VARCHAR" property="caseFeatures" />
    <result column="pre_diag" jdbcType="VARCHAR" property="preDiag" />
    <result column="diag_basis" jdbcType="VARCHAR" property="diagBasis" />
    <result column="diff_diag" jdbcType="VARCHAR" property="diffDiag" />
    <result column="assess_plan" jdbcType="VARCHAR" property="assessPlan" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="resident_doctor" jdbcType="VARCHAR" property="residentDoctor" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "name", "admission_date_time",
    "case_features", "pre_diag", "diag_basis", "diff_diag", "assess_plan", "record_time",
    "resident_doctor", "source_path", "pk_id", "data_state", "full_text", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_first_course"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_first_course"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_first_course"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseExample">
    delete from "public"."emr_first_course"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrFirstCourse">
    insert into "public"."emr_first_course" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "name", "admission_date_time",
      "case_features", "pre_diag", "diag_basis",
      "diff_diag", "assess_plan", "record_time",
      "resident_doctor", "source_path", "pk_id",
      "data_state", "full_text", "patient_sn_org"
      )
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP},
      #{caseFeatures,jdbcType=VARCHAR}, #{preDiag,jdbcType=VARCHAR}, #{diagBasis,jdbcType=VARCHAR},
      #{diffDiag,jdbcType=VARCHAR}, #{assessPlan,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP},
      #{residentDoctor,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrFirstCourse">
    insert into "public"."emr_first_course"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="caseFeatures != null">
        "case_features",
      </if>
      <if test="preDiag != null">
        "pre_diag",
      </if>
      <if test="diagBasis != null">
        "diag_basis",
      </if>
      <if test="diffDiag != null">
        "diff_diag",
      </if>
      <if test="assessPlan != null">
        "assess_plan",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="residentDoctor != null">
        "resident_doctor",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseFeatures != null">
        #{caseFeatures,jdbcType=VARCHAR},
      </if>
      <if test="preDiag != null">
        #{preDiag,jdbcType=VARCHAR},
      </if>
      <if test="diagBasis != null">
        #{diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="diffDiag != null">
        #{diffDiag,jdbcType=VARCHAR},
      </if>
      <if test="assessPlan != null">
        #{assessPlan,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="residentDoctor != null">
        #{residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrFirstCourseExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_first_course"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_first_course"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.caseFeatures != null">
        "case_features" = #{record.caseFeatures,jdbcType=VARCHAR},
      </if>
      <if test="record.preDiag != null">
        "pre_diag" = #{record.preDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.diagBasis != null">
        "diag_basis" = #{record.diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="record.diffDiag != null">
        "diff_diag" = #{record.diffDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.assessPlan != null">
        "assess_plan" = #{record.assessPlan,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.residentDoctor != null">
        "resident_doctor" = #{record.residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_first_course"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "case_features" = #{record.caseFeatures,jdbcType=VARCHAR},
      "pre_diag" = #{record.preDiag,jdbcType=VARCHAR},
      "diag_basis" = #{record.diagBasis,jdbcType=VARCHAR},
      "diff_diag" = #{record.diffDiag,jdbcType=VARCHAR},
      "assess_plan" = #{record.assessPlan,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "resident_doctor" = #{record.residentDoctor,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrFirstCourse">
    update "public"."emr_first_course"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseFeatures != null">
        "case_features" = #{caseFeatures,jdbcType=VARCHAR},
      </if>
      <if test="preDiag != null">
        "pre_diag" = #{preDiag,jdbcType=VARCHAR},
      </if>
      <if test="diagBasis != null">
        "diag_basis" = #{diagBasis,jdbcType=VARCHAR},
      </if>
      <if test="diffDiag != null">
        "diff_diag" = #{diffDiag,jdbcType=VARCHAR},
      </if>
      <if test="assessPlan != null">
        "assess_plan" = #{assessPlan,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="residentDoctor != null">
        "resident_doctor" = #{residentDoctor,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrFirstCourse">
    update "public"."emr_first_course"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "case_features" = #{caseFeatures,jdbcType=VARCHAR},
      "pre_diag" = #{preDiag,jdbcType=VARCHAR},
      "diag_basis" = #{diagBasis,jdbcType=VARCHAR},
      "diff_diag" = #{diffDiag,jdbcType=VARCHAR},
      "assess_plan" = #{assessPlan,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "resident_doctor" = #{residentDoctor,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getFirstCourse" resultMap="BaseResultMap">
    select * from emr_first_course where patient_sn = #{patientId} order by admission_date_time
  </select>
</mapper>
