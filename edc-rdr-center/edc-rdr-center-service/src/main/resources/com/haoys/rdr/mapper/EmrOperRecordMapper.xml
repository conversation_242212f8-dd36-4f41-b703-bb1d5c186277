<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrOperRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrOperRecord">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="operation_no" jdbcType="VARCHAR" property="operationNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="operation_code" jdbcType="VARCHAR" property="operationCode" />
    <result column="operation_datetime" jdbcType="TIMESTAMP" property="operationDatetime" />
    <result column="operation_duration" jdbcType="VARCHAR" property="operationDuration" />
    <result column="operation_begin_time" jdbcType="TIMESTAMP" property="operationBeginTime" />
    <result column="operation_end_time" jdbcType="TIMESTAMP" property="operationEndTime" />
    <result column="operation_level_code" jdbcType="VARCHAR" property="operationLevelCode" />
    <result column="operation_site_name" jdbcType="VARCHAR" property="operationSiteName" />
    <result column="operation_position" jdbcType="VARCHAR" property="operationPosition" />
    <result column="operation_history" jdbcType="VARCHAR" property="operationHistory" />
    <result column="operation_incision_description" jdbcType="VARCHAR" property="operationIncisionDescription" />
    <result column="drainage_sign" jdbcType="VARCHAR" property="drainageSign" />
    <result column="bleeding_volume" jdbcType="VARCHAR" property="bleedingVolume" />
    <result column="transfusion_volume" jdbcType="VARCHAR" property="transfusionVolume" />
    <result column="blood_transfusion_volume" jdbcType="VARCHAR" property="bloodTransfusionVolume" />
    <result column="drug_before_oper" jdbcType="VARCHAR" property="drugBeforeOper" />
    <result column="drug_during_oper" jdbcType="VARCHAR" property="drugDuringOper" />
    <result column="transfusion_reaction_signs" jdbcType="VARCHAR" property="transfusionReactionSigns" />
    <result column="surgeon_doctor" jdbcType="VARCHAR" property="surgeonDoctor" />
    <result column="first_assistant" jdbcType="VARCHAR" property="firstAssistant" />
    <result column="second_assistant" jdbcType="VARCHAR" property="secondAssistant" />
    <result column="anesthesia_code" jdbcType="VARCHAR" property="anesthesiaCode" />
    <result column="anesthesia_method" jdbcType="VARCHAR" property="anesthesiaMethod" />
    <result column="anaesthesia_doctor" jdbcType="VARCHAR" property="anaesthesiaDoctor" />
    <result column="diag_preoperation" jdbcType="VARCHAR" property="diagPreoperation" />
    <result column="diag_perioperation" jdbcType="VARCHAR" property="diagPerioperation" />
    <result column="diag_postoperation" jdbcType="VARCHAR" property="diagPostoperation" />
    <result column="surgical_process" jdbcType="VARCHAR" property="surgicalProcess" />
    <result column="postoperation_complite" jdbcType="VARCHAR" property="postoperationComplite" />
    <result column="focus_description" jdbcType="VARCHAR" property="focusDescription" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="doctor_sign" jdbcType="VARCHAR" property="doctorSign" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "operation_no", "name", "operation_name",
    "operation_code", "operation_datetime", "operation_duration", "operation_begin_time",
    "operation_end_time", "operation_level_code", "operation_site_name", "operation_position",
    "operation_history", "operation_incision_description", "drainage_sign", "bleeding_volume",
    "transfusion_volume", "blood_transfusion_volume", "drug_before_oper", "drug_during_oper",
    "transfusion_reaction_signs", "surgeon_doctor", "first_assistant", "second_assistant",
    "anesthesia_code", "anesthesia_method", "anaesthesia_doctor", "diag_preoperation",
    "diag_perioperation", "diag_postoperation", "surgical_process", "postoperation_complite",
    "focus_description", "record_time", "doctor_sign", "source_path", "pk_id", "data_state",
    "full_text", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrOperRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_oper_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_oper_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_oper_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrOperRecordExample">
    delete from "public"."emr_oper_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrOperRecord">
    insert into "public"."emr_oper_record" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "operation_no", "name",
      "operation_name", "operation_code", "operation_datetime",
      "operation_duration", "operation_begin_time",
      "operation_end_time", "operation_level_code",
      "operation_site_name", "operation_position", "operation_history",
      "operation_incision_description", "drainage_sign",
      "bleeding_volume", "transfusion_volume", "blood_transfusion_volume",
      "drug_before_oper", "drug_during_oper", "transfusion_reaction_signs",
      "surgeon_doctor", "first_assistant", "second_assistant",
      "anesthesia_code", "anesthesia_method", "anaesthesia_doctor",
      "diag_preoperation", "diag_perioperation", "diag_postoperation",
      "surgical_process", "postoperation_complite",
      "focus_description", "record_time", "doctor_sign",
      "source_path", "pk_id", "data_state",
      "full_text", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{operationNo,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{operationName,jdbcType=VARCHAR}, #{operationCode,jdbcType=VARCHAR}, #{operationDatetime,jdbcType=TIMESTAMP},
      #{operationDuration,jdbcType=VARCHAR}, #{operationBeginTime,jdbcType=TIMESTAMP},
      #{operationEndTime,jdbcType=TIMESTAMP}, #{operationLevelCode,jdbcType=VARCHAR},
      #{operationSiteName,jdbcType=VARCHAR}, #{operationPosition,jdbcType=VARCHAR}, #{operationHistory,jdbcType=VARCHAR},
      #{operationIncisionDescription,jdbcType=VARCHAR}, #{drainageSign,jdbcType=VARCHAR},
      #{bleedingVolume,jdbcType=VARCHAR}, #{transfusionVolume,jdbcType=VARCHAR}, #{bloodTransfusionVolume,jdbcType=VARCHAR},
      #{drugBeforeOper,jdbcType=VARCHAR}, #{drugDuringOper,jdbcType=VARCHAR}, #{transfusionReactionSigns,jdbcType=VARCHAR},
      #{surgeonDoctor,jdbcType=VARCHAR}, #{firstAssistant,jdbcType=VARCHAR}, #{secondAssistant,jdbcType=VARCHAR},
      #{anesthesiaCode,jdbcType=VARCHAR}, #{anesthesiaMethod,jdbcType=VARCHAR}, #{anaesthesiaDoctor,jdbcType=VARCHAR},
      #{diagPreoperation,jdbcType=VARCHAR}, #{diagPerioperation,jdbcType=VARCHAR}, #{diagPostoperation,jdbcType=VARCHAR},
      #{surgicalProcess,jdbcType=VARCHAR}, #{postoperationComplite,jdbcType=VARCHAR},
      #{focusDescription,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP}, #{doctorSign,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{fullText,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrOperRecord">
    insert into "public"."emr_oper_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="operationNo != null">
        "operation_no",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="operationName != null">
        "operation_name",
      </if>
      <if test="operationCode != null">
        "operation_code",
      </if>
      <if test="operationDatetime != null">
        "operation_datetime",
      </if>
      <if test="operationDuration != null">
        "operation_duration",
      </if>
      <if test="operationBeginTime != null">
        "operation_begin_time",
      </if>
      <if test="operationEndTime != null">
        "operation_end_time",
      </if>
      <if test="operationLevelCode != null">
        "operation_level_code",
      </if>
      <if test="operationSiteName != null">
        "operation_site_name",
      </if>
      <if test="operationPosition != null">
        "operation_position",
      </if>
      <if test="operationHistory != null">
        "operation_history",
      </if>
      <if test="operationIncisionDescription != null">
        "operation_incision_description",
      </if>
      <if test="drainageSign != null">
        "drainage_sign",
      </if>
      <if test="bleedingVolume != null">
        "bleeding_volume",
      </if>
      <if test="transfusionVolume != null">
        "transfusion_volume",
      </if>
      <if test="bloodTransfusionVolume != null">
        "blood_transfusion_volume",
      </if>
      <if test="drugBeforeOper != null">
        "drug_before_oper",
      </if>
      <if test="drugDuringOper != null">
        "drug_during_oper",
      </if>
      <if test="transfusionReactionSigns != null">
        "transfusion_reaction_signs",
      </if>
      <if test="surgeonDoctor != null">
        "surgeon_doctor",
      </if>
      <if test="firstAssistant != null">
        "first_assistant",
      </if>
      <if test="secondAssistant != null">
        "second_assistant",
      </if>
      <if test="anesthesiaCode != null">
        "anesthesia_code",
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method",
      </if>
      <if test="anaesthesiaDoctor != null">
        "anaesthesia_doctor",
      </if>
      <if test="diagPreoperation != null">
        "diag_preoperation",
      </if>
      <if test="diagPerioperation != null">
        "diag_perioperation",
      </if>
      <if test="diagPostoperation != null">
        "diag_postoperation",
      </if>
      <if test="surgicalProcess != null">
        "surgical_process",
      </if>
      <if test="postoperationComplite != null">
        "postoperation_complite",
      </if>
      <if test="focusDescription != null">
        "focus_description",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="doctorSign != null">
        "doctor_sign",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationNo != null">
        #{operationNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="operationCode != null">
        #{operationCode,jdbcType=VARCHAR},
      </if>
      <if test="operationDatetime != null">
        #{operationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationDuration != null">
        #{operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="operationBeginTime != null">
        #{operationBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationEndTime != null">
        #{operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationLevelCode != null">
        #{operationLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="operationSiteName != null">
        #{operationSiteName,jdbcType=VARCHAR},
      </if>
      <if test="operationPosition != null">
        #{operationPosition,jdbcType=VARCHAR},
      </if>
      <if test="operationHistory != null">
        #{operationHistory,jdbcType=VARCHAR},
      </if>
      <if test="operationIncisionDescription != null">
        #{operationIncisionDescription,jdbcType=VARCHAR},
      </if>
      <if test="drainageSign != null">
        #{drainageSign,jdbcType=VARCHAR},
      </if>
      <if test="bleedingVolume != null">
        #{bleedingVolume,jdbcType=VARCHAR},
      </if>
      <if test="transfusionVolume != null">
        #{transfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="bloodTransfusionVolume != null">
        #{bloodTransfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="drugBeforeOper != null">
        #{drugBeforeOper,jdbcType=VARCHAR},
      </if>
      <if test="drugDuringOper != null">
        #{drugDuringOper,jdbcType=VARCHAR},
      </if>
      <if test="transfusionReactionSigns != null">
        #{transfusionReactionSigns,jdbcType=VARCHAR},
      </if>
      <if test="surgeonDoctor != null">
        #{surgeonDoctor,jdbcType=VARCHAR},
      </if>
      <if test="firstAssistant != null">
        #{firstAssistant,jdbcType=VARCHAR},
      </if>
      <if test="secondAssistant != null">
        #{secondAssistant,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaCode != null">
        #{anesthesiaCode,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctor != null">
        #{anaesthesiaDoctor,jdbcType=VARCHAR},
      </if>
      <if test="diagPreoperation != null">
        #{diagPreoperation,jdbcType=VARCHAR},
      </if>
      <if test="diagPerioperation != null">
        #{diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="diagPostoperation != null">
        #{diagPostoperation,jdbcType=VARCHAR},
      </if>
      <if test="surgicalProcess != null">
        #{surgicalProcess,jdbcType=VARCHAR},
      </if>
      <if test="postoperationComplite != null">
        #{postoperationComplite,jdbcType=VARCHAR},
      </if>
      <if test="focusDescription != null">
        #{focusDescription,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrOperRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_oper_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_oper_record"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.operationNo != null">
        "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.operationName != null">
        "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationCode != null">
        "operation_code" = #{record.operationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.operationDatetime != null">
        "operation_datetime" = #{record.operationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationDuration != null">
        "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="record.operationBeginTime != null">
        "operation_begin_time" = #{record.operationBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationEndTime != null">
        "operation_end_time" = #{record.operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationLevelCode != null">
        "operation_level_code" = #{record.operationLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.operationSiteName != null">
        "operation_site_name" = #{record.operationSiteName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationPosition != null">
        "operation_position" = #{record.operationPosition,jdbcType=VARCHAR},
      </if>
      <if test="record.operationHistory != null">
        "operation_history" = #{record.operationHistory,jdbcType=VARCHAR},
      </if>
      <if test="record.operationIncisionDescription != null">
        "operation_incision_description" = #{record.operationIncisionDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.drainageSign != null">
        "drainage_sign" = #{record.drainageSign,jdbcType=VARCHAR},
      </if>
      <if test="record.bleedingVolume != null">
        "bleeding_volume" = #{record.bleedingVolume,jdbcType=VARCHAR},
      </if>
      <if test="record.transfusionVolume != null">
        "transfusion_volume" = #{record.transfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="record.bloodTransfusionVolume != null">
        "blood_transfusion_volume" = #{record.bloodTransfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="record.drugBeforeOper != null">
        "drug_before_oper" = #{record.drugBeforeOper,jdbcType=VARCHAR},
      </if>
      <if test="record.drugDuringOper != null">
        "drug_during_oper" = #{record.drugDuringOper,jdbcType=VARCHAR},
      </if>
      <if test="record.transfusionReactionSigns != null">
        "transfusion_reaction_signs" = #{record.transfusionReactionSigns,jdbcType=VARCHAR},
      </if>
      <if test="record.surgeonDoctor != null">
        "surgeon_doctor" = #{record.surgeonDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.firstAssistant != null">
        "first_assistant" = #{record.firstAssistant,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAssistant != null">
        "second_assistant" = #{record.secondAssistant,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaCode != null">
        "anesthesia_code" = #{record.anesthesiaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaMethod != null">
        "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.anaesthesiaDoctor != null">
        "anaesthesia_doctor" = #{record.anaesthesiaDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPreoperation != null">
        "diag_preoperation" = #{record.diagPreoperation,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPerioperation != null">
        "diag_perioperation" = #{record.diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="record.diagPostoperation != null">
        "diag_postoperation" = #{record.diagPostoperation,jdbcType=VARCHAR},
      </if>
      <if test="record.surgicalProcess != null">
        "surgical_process" = #{record.surgicalProcess,jdbcType=VARCHAR},
      </if>
      <if test="record.postoperationComplite != null">
        "postoperation_complite" = #{record.postoperationComplite,jdbcType=VARCHAR},
      </if>
      <if test="record.focusDescription != null">
        "focus_description" = #{record.focusDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorSign != null">
        "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_oper_record"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      "operation_code" = #{record.operationCode,jdbcType=VARCHAR},
      "operation_datetime" = #{record.operationDatetime,jdbcType=TIMESTAMP},
      "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      "operation_begin_time" = #{record.operationBeginTime,jdbcType=TIMESTAMP},
      "operation_end_time" = #{record.operationEndTime,jdbcType=TIMESTAMP},
      "operation_level_code" = #{record.operationLevelCode,jdbcType=VARCHAR},
      "operation_site_name" = #{record.operationSiteName,jdbcType=VARCHAR},
      "operation_position" = #{record.operationPosition,jdbcType=VARCHAR},
      "operation_history" = #{record.operationHistory,jdbcType=VARCHAR},
      "operation_incision_description" = #{record.operationIncisionDescription,jdbcType=VARCHAR},
      "drainage_sign" = #{record.drainageSign,jdbcType=VARCHAR},
      "bleeding_volume" = #{record.bleedingVolume,jdbcType=VARCHAR},
      "transfusion_volume" = #{record.transfusionVolume,jdbcType=VARCHAR},
      "blood_transfusion_volume" = #{record.bloodTransfusionVolume,jdbcType=VARCHAR},
      "drug_before_oper" = #{record.drugBeforeOper,jdbcType=VARCHAR},
      "drug_during_oper" = #{record.drugDuringOper,jdbcType=VARCHAR},
      "transfusion_reaction_signs" = #{record.transfusionReactionSigns,jdbcType=VARCHAR},
      "surgeon_doctor" = #{record.surgeonDoctor,jdbcType=VARCHAR},
      "first_assistant" = #{record.firstAssistant,jdbcType=VARCHAR},
      "second_assistant" = #{record.secondAssistant,jdbcType=VARCHAR},
      "anesthesia_code" = #{record.anesthesiaCode,jdbcType=VARCHAR},
      "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      "anaesthesia_doctor" = #{record.anaesthesiaDoctor,jdbcType=VARCHAR},
      "diag_preoperation" = #{record.diagPreoperation,jdbcType=VARCHAR},
      "diag_perioperation" = #{record.diagPerioperation,jdbcType=VARCHAR},
      "diag_postoperation" = #{record.diagPostoperation,jdbcType=VARCHAR},
      "surgical_process" = #{record.surgicalProcess,jdbcType=VARCHAR},
      "postoperation_complite" = #{record.postoperationComplite,jdbcType=VARCHAR},
      "focus_description" = #{record.focusDescription,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrOperRecord">
    update "public"."emr_oper_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationNo != null">
        "operation_no" = #{operationNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        "operation_name" = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="operationCode != null">
        "operation_code" = #{operationCode,jdbcType=VARCHAR},
      </if>
      <if test="operationBeginTime != null">
        "operation_begin_time" = #{operationBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationEndTime != null">
        "operation_end_time" = #{operationEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationLevelCode != null">
        "operation_level_code" = #{operationLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="operationSiteName != null">
        "operation_site_name" = #{operationSiteName,jdbcType=VARCHAR},
      </if>
      <if test="operationPosition != null">
        "operation_position" = #{operationPosition,jdbcType=VARCHAR},
      </if>
      <if test="operationHistory != null">
        "operation_history" = #{operationHistory,jdbcType=VARCHAR},
      </if>
      <if test="operationIncisionDescription != null">
        "operation_incision_description" = #{operationIncisionDescription,jdbcType=VARCHAR},
      </if>
      <if test="drainageSign != null">
        "drainage_sign" = #{drainageSign,jdbcType=VARCHAR},
      </if>
      <if test="bleedingVolume != null">
        "bleeding_volume" = #{bleedingVolume,jdbcType=VARCHAR},
      </if>
      <if test="transfusionVolume != null">
        "transfusion_volume" = #{transfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="bloodTransfusionVolume != null">
        "blood_transfusion_volume" = #{bloodTransfusionVolume,jdbcType=VARCHAR},
      </if>
      <if test="drugBeforeOper != null">
        "drug_before_oper" = #{drugBeforeOper,jdbcType=VARCHAR},
      </if>
      <if test="drugDuringOper != null">
        "drug_during_oper" = #{drugDuringOper,jdbcType=VARCHAR},
      </if>
      <if test="transfusionReactionSigns != null">
        "transfusion_reaction_signs" = #{transfusionReactionSigns,jdbcType=VARCHAR},
      </if>
      <if test="surgeonDoctor != null">
        "surgeon_doctor" = #{surgeonDoctor,jdbcType=VARCHAR},
      </if>
      <if test="firstAssistant != null">
        "first_assistant" = #{firstAssistant,jdbcType=VARCHAR},
      </if>
      <if test="secondAssistant != null">
        "second_assistant" = #{secondAssistant,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaCode != null">
        "anesthesia_code" = #{anesthesiaCode,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctor != null">
        "anaesthesia_doctor" = #{anaesthesiaDoctor,jdbcType=VARCHAR},
      </if>
      <if test="diagPreoperation != null">
        "diag_preoperation" = #{diagPreoperation,jdbcType=VARCHAR},
      </if>
      <if test="diagPerioperation != null">
        "diag_perioperation" = #{diagPerioperation,jdbcType=VARCHAR},
      </if>
      <if test="diagPostoperation != null">
        "diag_postoperation" = #{diagPostoperation,jdbcType=VARCHAR},
      </if>
      <if test="surgicalProcess != null">
        "surgical_process" = #{surgicalProcess,jdbcType=VARCHAR},
      </if>
      <if test="postoperationComplite != null">
        "postoperation_complite" = #{postoperationComplite,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="focusDescription != null">
        "focus_description" = #{focusDescription,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrOperRecord">
    update "public"."emr_oper_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "operation_no" = #{operationNo,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "operation_name" = #{operationName,jdbcType=VARCHAR},
      "operation_code" = #{operationCode,jdbcType=VARCHAR},
      "operation_begin_time" = #{operationBeginTime,jdbcType=TIMESTAMP},
      "operation_end_time" = #{operationEndTime,jdbcType=TIMESTAMP},
      "operation_level_code" = #{operationLevelCode,jdbcType=VARCHAR},
      "operation_site_name" = #{operationSiteName,jdbcType=VARCHAR},
      "operation_position" = #{operationPosition,jdbcType=VARCHAR},
      "operation_history" = #{operationHistory,jdbcType=VARCHAR},
      "operation_incision_description" = #{operationIncisionDescription,jdbcType=VARCHAR},
      "drainage_sign" = #{drainageSign,jdbcType=VARCHAR},
      "bleeding_volume" = #{bleedingVolume,jdbcType=VARCHAR},
      "transfusion_volume" = #{transfusionVolume,jdbcType=VARCHAR},
      "blood_transfusion_volume" = #{bloodTransfusionVolume,jdbcType=VARCHAR},
      "drug_before_oper" = #{drugBeforeOper,jdbcType=VARCHAR},
      "drug_during_oper" = #{drugDuringOper,jdbcType=VARCHAR},
      "transfusion_reaction_signs" = #{transfusionReactionSigns,jdbcType=VARCHAR},
      "surgeon_doctor" = #{surgeonDoctor,jdbcType=VARCHAR},
      "first_assistant" = #{firstAssistant,jdbcType=VARCHAR},
      "second_assistant" = #{secondAssistant,jdbcType=VARCHAR},
      "anesthesia_code" = #{anesthesiaCode,jdbcType=VARCHAR},
      "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      "anaesthesia_doctor" = #{anaesthesiaDoctor,jdbcType=VARCHAR},
      "diag_preoperation" = #{diagPreoperation,jdbcType=VARCHAR},
      "diag_perioperation" = #{diagPerioperation,jdbcType=VARCHAR},
      "diag_postoperation" = #{diagPostoperation,jdbcType=VARCHAR},
      "surgical_process" = #{surgicalProcess,jdbcType=VARCHAR},
      "postoperation_complite" = #{postoperationComplite,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "focus_description" = #{focusDescription,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getOperRecord" resultMap="BaseResultMap">
    select * from emr_oper_record where patient_sn = #{patientId} order by operation_begin_time
  </select>
</mapper>
