<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrOutpRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrOutpRecord">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="emr_content" jdbcType="VARCHAR" property="emrContent" />
    <result column="outpatient_diagnosis" jdbcType="VARCHAR" property="outpatientDiagnosis" />
    <result column="hy_present" jdbcType="VARCHAR" property="hyPresent" />
    <result column="hy_past" jdbcType="VARCHAR" property="hyPast" />
    <result column="physical_exam" jdbcType="VARCHAR" property="physicalExam" />
    <result column="chief_complaint" jdbcType="VARCHAR" property="chiefComplaint" />
    <result column="supplementary_exam" jdbcType="VARCHAR" property="supplementaryExam" />
    <result column="hy_menstrual_marriage" jdbcType="VARCHAR" property="hyMenstrualMarriage" />
    <result column="treatment" jdbcType="VARCHAR" property="treatment" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="doctor_sign" jdbcType="VARCHAR" property="doctorSign" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_date", "name", "emr_content", "outpatient_diagnosis",
    "hy_present", "hy_past", "physical_exam", "chief_complaint", "supplementary_exam",
    "hy_menstrual_marriage", "treatment", "record_time", "doctor_sign", "source_path",
    "pk_id", "data_state", "full_text", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrOutpRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_outp_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."emr_outp_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_outp_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrOutpRecordExample">
    delete from "public"."emr_outp_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrOutpRecord">
    insert into "public"."emr_outp_record" ("hospital_code", "patient_sn", "visit_sn",
      "visit_date", "name", "emr_content",
      "outpatient_diagnosis", "hy_present", "hy_past",
      "physical_exam", "chief_complaint", "supplementary_exam",
      "hy_menstrual_marriage", "treatment", "record_time",
      "doctor_sign", "source_path", "pk_id",
      "data_state", "full_text", "patient_sn_org"
      )
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitDate,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, #{emrContent,jdbcType=VARCHAR},
      #{outpatientDiagnosis,jdbcType=VARCHAR}, #{hyPresent,jdbcType=VARCHAR}, #{hyPast,jdbcType=VARCHAR},
      #{physicalExam,jdbcType=VARCHAR}, #{chiefComplaint,jdbcType=VARCHAR}, #{supplementaryExam,jdbcType=VARCHAR},
      #{hyMenstrualMarriage,jdbcType=VARCHAR}, #{treatment,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP},
      #{doctorSign,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrOutpRecord">
    insert into "public"."emr_outp_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="emrContent != null">
        "emr_content",
      </if>
      <if test="outpatientDiagnosis != null">
        "outpatient_diagnosis",
      </if>
      <if test="hyPresent != null">
        "hy_present",
      </if>
      <if test="hyPast != null">
        "hy_past",
      </if>
      <if test="physicalExam != null">
        "physical_exam",
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint",
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam",
      </if>
      <if test="hyMenstrualMarriage != null">
        "hy_menstrual_marriage",
      </if>
      <if test="treatment != null">
        "treatment",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="doctorSign != null">
        "doctor_sign",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="emrContent != null">
        #{emrContent,jdbcType=VARCHAR},
      </if>
      <if test="outpatientDiagnosis != null">
        #{outpatientDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="hyMenstrualMarriage != null">
        #{hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="treatment != null">
        #{treatment,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrOutpRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_outp_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_outp_record"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.emrContent != null">
        "emr_content" = #{record.emrContent,jdbcType=VARCHAR},
      </if>
      <if test="record.outpatientDiagnosis != null">
        "outpatient_diagnosis" = #{record.outpatientDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPresent != null">
        "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="record.hyPast != null">
        "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      </if>
      <if test="record.physicalExam != null">
        "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="record.chiefComplaint != null">
        "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="record.supplementaryExam != null">
        "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="record.hyMenstrualMarriage != null">
        "hy_menstrual_marriage" = #{record.hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="record.treatment != null">
        "treatment" = #{record.treatment,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctorSign != null">
        "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_outp_record"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "name" = #{record.name,jdbcType=VARCHAR},
      "emr_content" = #{record.emrContent,jdbcType=VARCHAR},
      "outpatient_diagnosis" = #{record.outpatientDiagnosis,jdbcType=VARCHAR},
      "hy_present" = #{record.hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{record.hyPast,jdbcType=VARCHAR},
      "physical_exam" = #{record.physicalExam,jdbcType=VARCHAR},
      "chief_complaint" = #{record.chiefComplaint,jdbcType=VARCHAR},
      "supplementary_exam" = #{record.supplementaryExam,jdbcType=VARCHAR},
      "hy_menstrual_marriage" = #{record.hyMenstrualMarriage,jdbcType=VARCHAR},
      "treatment" = #{record.treatment,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{record.doctorSign,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrOutpRecord">
    update "public"."emr_outp_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="emrContent != null">
        "emr_content" = #{emrContent,jdbcType=VARCHAR},
      </if>
      <if test="outpatientDiagnosis != null">
        "outpatient_diagnosis" = #{outpatientDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="hyPresent != null">
        "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      </if>
      <if test="hyPast != null">
        "hy_past" = #{hyPast,jdbcType=VARCHAR},
      </if>
      <if test="physicalExam != null">
        "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      </if>
      <if test="chiefComplaint != null">
        "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      </if>
      <if test="supplementaryExam != null">
        "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      </if>
      <if test="hyMenstrualMarriage != null">
        "hy_menstrual_marriage" = #{hyMenstrualMarriage,jdbcType=VARCHAR},
      </if>
      <if test="treatment != null">
        "treatment" = #{treatment,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorSign != null">
        "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrOutpRecord">
    update "public"."emr_outp_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "name" = #{name,jdbcType=VARCHAR},
      "emr_content" = #{emrContent,jdbcType=VARCHAR},
      "outpatient_diagnosis" = #{outpatientDiagnosis,jdbcType=VARCHAR},
      "hy_present" = #{hyPresent,jdbcType=VARCHAR},
      "hy_past" = #{hyPast,jdbcType=VARCHAR},
      "physical_exam" = #{physicalExam,jdbcType=VARCHAR},
      "chief_complaint" = #{chiefComplaint,jdbcType=VARCHAR},
      "supplementary_exam" = #{supplementaryExam,jdbcType=VARCHAR},
      "hy_menstrual_marriage" = #{hyMenstrualMarriage,jdbcType=VARCHAR},
      "treatment" = #{treatment,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "doctor_sign" = #{doctorSign,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getEmrOutpRecord" resultMap="BaseResultMap">
    select * from "public"."emr_outp_record" where patient_sn = #{patientSn} and visit_sn = #{visitSn}
  </select>
</mapper>
