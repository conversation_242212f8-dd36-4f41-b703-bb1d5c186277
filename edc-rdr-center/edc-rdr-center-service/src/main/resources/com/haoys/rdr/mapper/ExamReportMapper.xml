<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.ExamReportMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.ExamReport">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_sn_org" jdbcType="VARCHAR" property="visitSnOrg" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="date_of_birth" jdbcType="TIMESTAMP" property="dateOfBirth" />
    <result column="patient_source" jdbcType="VARCHAR" property="patientSource" />
    <result column="clin_symp" jdbcType="VARCHAR" property="clinSymp" />
    <result column="phys_sign" jdbcType="VARCHAR" property="physSign" />
    <result column="relevant_lab_test" jdbcType="VARCHAR" property="relevantLabTest" />
    <result column="clin_diag" jdbcType="VARCHAR" property="clinDiag" />
    <result column="relevant_diag" jdbcType="VARCHAR" property="relevantDiag" />
    <result column="exam_class" jdbcType="VARCHAR" property="examClass" />
    <result column="exam_position" jdbcType="VARCHAR" property="examPosition" />
    <result column="exam_mode" jdbcType="VARCHAR" property="examMode" />
    <result column="device" jdbcType="VARCHAR" property="device" />
    <result column="exam_para" jdbcType="VARCHAR" property="examPara" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="conclusion" jdbcType="VARCHAR" property="conclusion" />
    <result column="is_abnormal" jdbcType="VARCHAR" property="isAbnormal" />
    <result column="req_date_time" jdbcType="TIMESTAMP" property="reqDateTime" />
    <result column="req_dept" jdbcType="VARCHAR" property="reqDept" />
    <result column="req_doctor" jdbcType="VARCHAR" property="reqDoctor" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="exam_date_time" jdbcType="TIMESTAMP" property="examDateTime" />
    <result column="perform_dept" jdbcType="VARCHAR" property="performDept" />
    <result column="report_date_time" jdbcType="TIMESTAMP" property="reportDateTime" />
    <result column="report_doctor" jdbcType="VARCHAR" property="reportDoctor" />
    <result column="review_date_time" jdbcType="TIMESTAMP" property="reviewDateTime" />
    <result column="review_doctor" jdbcType="VARCHAR" property="reviewDoctor" />
    <result column="image_no" jdbcType="VARCHAR" property="imageNo" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="exam_name" jdbcType="VARCHAR" property="examName" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_sn_org", "report_no", "order_no",
    "name", "gender", "date_of_birth", "patient_source", "clin_symp", "phys_sign", "relevant_lab_test",
    "clin_diag", "relevant_diag", "exam_class", "exam_position", "exam_mode", "device",
    "exam_para", "description", "conclusion", "is_abnormal", "req_date_time", "req_dept",
    "req_doctor", "notice", "exam_date_time", "perform_dept", "report_date_time", "report_doctor",
    "review_date_time", "review_doctor", "image_no", "image_path", "comment", "source_path",
    "pk_id", "data_state", "exam_name", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.ExamReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."exam_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."exam_report"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."exam_report"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.ExamReportExample">
    delete from "public"."exam_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.ExamReport">
    insert into "public"."exam_report" ("hospital_code", "patient_sn", "visit_sn",
      "visit_sn_org", "report_no", "order_no",
      "name", "gender", "date_of_birth",
      "patient_source", "clin_symp", "phys_sign",
      "relevant_lab_test", "clin_diag", "relevant_diag",
      "exam_class", "exam_position", "exam_mode",
      "device", "exam_para", "description",
      "conclusion", "is_abnormal", "req_date_time",
      "req_dept", "req_doctor", "notice",
      "exam_date_time", "perform_dept", "report_date_time",
      "report_doctor", "review_date_time", "review_doctor",
      "image_no", "image_path", "comment",
      "source_path", "pk_id", "data_state",
      "exam_name", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitSnOrg,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, #{dateOfBirth,jdbcType=TIMESTAMP},
      #{patientSource,jdbcType=VARCHAR}, #{clinSymp,jdbcType=VARCHAR}, #{physSign,jdbcType=VARCHAR},
      #{relevantLabTest,jdbcType=VARCHAR}, #{clinDiag,jdbcType=VARCHAR}, #{relevantDiag,jdbcType=VARCHAR},
      #{examClass,jdbcType=VARCHAR}, #{examPosition,jdbcType=VARCHAR}, #{examMode,jdbcType=VARCHAR},
      #{device,jdbcType=VARCHAR}, #{examPara,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
      #{conclusion,jdbcType=VARCHAR}, #{isAbnormal,jdbcType=VARCHAR}, #{reqDateTime,jdbcType=TIMESTAMP},
      #{reqDept,jdbcType=VARCHAR}, #{reqDoctor,jdbcType=VARCHAR}, #{notice,jdbcType=VARCHAR},
      #{examDateTime,jdbcType=TIMESTAMP}, #{performDept,jdbcType=VARCHAR}, #{reportDateTime,jdbcType=TIMESTAMP},
      #{reportDoctor,jdbcType=VARCHAR}, #{reviewDateTime,jdbcType=TIMESTAMP}, #{reviewDoctor,jdbcType=VARCHAR},
      #{imageNo,jdbcType=VARCHAR}, #{imagePath,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{examName,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.ExamReport">
    insert into "public"."exam_report"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitSnOrg != null">
        "visit_sn_org",
      </if>
      <if test="reportNo != null">
        "report_no",
      </if>
      <if test="orderNo != null">
        "order_no",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="gender != null">
        "gender",
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth",
      </if>
      <if test="patientSource != null">
        "patient_source",
      </if>
      <if test="clinSymp != null">
        "clin_symp",
      </if>
      <if test="physSign != null">
        "phys_sign",
      </if>
      <if test="relevantLabTest != null">
        "relevant_lab_test",
      </if>
      <if test="clinDiag != null">
        "clin_diag",
      </if>
      <if test="relevantDiag != null">
        "relevant_diag",
      </if>
      <if test="examClass != null">
        "exam_class",
      </if>
      <if test="examPosition != null">
        "exam_position",
      </if>
      <if test="examMode != null">
        "exam_mode",
      </if>
      <if test="device != null">
        "device",
      </if>
      <if test="examPara != null">
        "exam_para",
      </if>
      <if test="description != null">
        "description",
      </if>
      <if test="conclusion != null">
        "conclusion",
      </if>
      <if test="isAbnormal != null">
        "is_abnormal",
      </if>
      <if test="reqDateTime != null">
        "req_date_time",
      </if>
      <if test="reqDept != null">
        "req_dept",
      </if>
      <if test="reqDoctor != null">
        "req_doctor",
      </if>
      <if test="notice != null">
        "notice",
      </if>
      <if test="examDateTime != null">
        "exam_date_time",
      </if>
      <if test="performDept != null">
        "perform_dept",
      </if>
      <if test="reportDateTime != null">
        "report_date_time",
      </if>
      <if test="reportDoctor != null">
        "report_doctor",
      </if>
      <if test="reviewDateTime != null">
        "review_date_time",
      </if>
      <if test="reviewDoctor != null">
        "review_doctor",
      </if>
      <if test="imageNo != null">
        "image_no",
      </if>
      <if test="imagePath != null">
        "image_path",
      </if>
      <if test="comment != null">
        "comment",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="examName != null">
        "exam_name",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSnOrg != null">
        #{visitSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="patientSource != null">
        #{patientSource,jdbcType=VARCHAR},
      </if>
      <if test="clinSymp != null">
        #{clinSymp,jdbcType=VARCHAR},
      </if>
      <if test="physSign != null">
        #{physSign,jdbcType=VARCHAR},
      </if>
      <if test="relevantLabTest != null">
        #{relevantLabTest,jdbcType=VARCHAR},
      </if>
      <if test="clinDiag != null">
        #{clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="relevantDiag != null">
        #{relevantDiag,jdbcType=VARCHAR},
      </if>
      <if test="examClass != null">
        #{examClass,jdbcType=VARCHAR},
      </if>
      <if test="examPosition != null">
        #{examPosition,jdbcType=VARCHAR},
      </if>
      <if test="examMode != null">
        #{examMode,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        #{device,jdbcType=VARCHAR},
      </if>
      <if test="examPara != null">
        #{examPara,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="conclusion != null">
        #{conclusion,jdbcType=VARCHAR},
      </if>
      <if test="isAbnormal != null">
        #{isAbnormal,jdbcType=VARCHAR},
      </if>
      <if test="reqDateTime != null">
        #{reqDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reqDept != null">
        #{reqDept,jdbcType=VARCHAR},
      </if>
      <if test="reqDoctor != null">
        #{reqDoctor,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        #{notice,jdbcType=VARCHAR},
      </if>
      <if test="examDateTime != null">
        #{examDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="performDept != null">
        #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="reportDateTime != null">
        #{reportDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDoctor != null">
        #{reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reviewDateTime != null">
        #{reviewDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewDoctor != null">
        #{reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="imageNo != null">
        #{imageNo,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null">
        #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="examName != null">
        #{examName,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.ExamReportExample" resultType="java.lang.Long">
    select count(*) from "public"."exam_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."exam_report"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSnOrg != null">
        "visit_sn_org" = #{record.visitSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        "report_no" = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        "gender" = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOfBirth != null">
        "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="record.patientSource != null">
        "patient_source" = #{record.patientSource,jdbcType=VARCHAR},
      </if>
      <if test="record.clinSymp != null">
        "clin_symp" = #{record.clinSymp,jdbcType=VARCHAR},
      </if>
      <if test="record.physSign != null">
        "phys_sign" = #{record.physSign,jdbcType=VARCHAR},
      </if>
      <if test="record.relevantLabTest != null">
        "relevant_lab_test" = #{record.relevantLabTest,jdbcType=VARCHAR},
      </if>
      <if test="record.clinDiag != null">
        "clin_diag" = #{record.clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.relevantDiag != null">
        "relevant_diag" = #{record.relevantDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.examClass != null">
        "exam_class" = #{record.examClass,jdbcType=VARCHAR},
      </if>
      <if test="record.examPosition != null">
        "exam_position" = #{record.examPosition,jdbcType=VARCHAR},
      </if>
      <if test="record.examMode != null">
        "exam_mode" = #{record.examMode,jdbcType=VARCHAR},
      </if>
      <if test="record.device != null">
        "device" = #{record.device,jdbcType=VARCHAR},
      </if>
      <if test="record.examPara != null">
        "exam_para" = #{record.examPara,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        "description" = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusion != null">
        "conclusion" = #{record.conclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.isAbnormal != null">
        "is_abnormal" = #{record.isAbnormal,jdbcType=VARCHAR},
      </if>
      <if test="record.reqDateTime != null">
        "req_date_time" = #{record.reqDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reqDept != null">
        "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      </if>
      <if test="record.reqDoctor != null">
        "req_doctor" = #{record.reqDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.notice != null">
        "notice" = #{record.notice,jdbcType=VARCHAR},
      </if>
      <if test="record.examDateTime != null">
        "exam_date_time" = #{record.examDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.performDept != null">
        "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDateTime != null">
        "report_date_time" = #{record.reportDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportDoctor != null">
        "report_doctor" = #{record.reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewDateTime != null">
        "review_date_time" = #{record.reviewDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewDoctor != null">
        "review_doctor" = #{record.reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.imageNo != null">
        "image_no" = #{record.imageNo,jdbcType=VARCHAR},
      </if>
      <if test="record.imagePath != null">
        "image_path" = #{record.imagePath,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        "comment" = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.examName != null">
        "exam_name" = #{record.examName,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."exam_report"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_sn_org" = #{record.visitSnOrg,jdbcType=VARCHAR},
      "report_no" = #{record.reportNo,jdbcType=VARCHAR},
      "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "gender" = #{record.gender,jdbcType=VARCHAR},
      "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      "patient_source" = #{record.patientSource,jdbcType=VARCHAR},
      "clin_symp" = #{record.clinSymp,jdbcType=VARCHAR},
      "phys_sign" = #{record.physSign,jdbcType=VARCHAR},
      "relevant_lab_test" = #{record.relevantLabTest,jdbcType=VARCHAR},
      "clin_diag" = #{record.clinDiag,jdbcType=VARCHAR},
      "relevant_diag" = #{record.relevantDiag,jdbcType=VARCHAR},
      "exam_class" = #{record.examClass,jdbcType=VARCHAR},
      "exam_position" = #{record.examPosition,jdbcType=VARCHAR},
      "exam_mode" = #{record.examMode,jdbcType=VARCHAR},
      "device" = #{record.device,jdbcType=VARCHAR},
      "exam_para" = #{record.examPara,jdbcType=VARCHAR},
      "description" = #{record.description,jdbcType=VARCHAR},
      "conclusion" = #{record.conclusion,jdbcType=VARCHAR},
      "is_abnormal" = #{record.isAbnormal,jdbcType=VARCHAR},
      "req_date_time" = #{record.reqDateTime,jdbcType=TIMESTAMP},
      "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      "req_doctor" = #{record.reqDoctor,jdbcType=VARCHAR},
      "notice" = #{record.notice,jdbcType=VARCHAR},
      "exam_date_time" = #{record.examDateTime,jdbcType=TIMESTAMP},
      "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      "report_date_time" = #{record.reportDateTime,jdbcType=TIMESTAMP},
      "report_doctor" = #{record.reportDoctor,jdbcType=VARCHAR},
      "review_date_time" = #{record.reviewDateTime,jdbcType=TIMESTAMP},
      "review_doctor" = #{record.reviewDoctor,jdbcType=VARCHAR},
      "image_no" = #{record.imageNo,jdbcType=VARCHAR},
      "image_path" = #{record.imagePath,jdbcType=VARCHAR},
      "comment" = #{record.comment,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "exam_name" = #{record.examName,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.ExamReport">
    update "public"."exam_report"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        "report_no" = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        "order_no" = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        "gender" = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="patientSource != null">
        "patient_source" = #{patientSource,jdbcType=VARCHAR},
      </if>
      <if test="clinSymp != null">
        "clin_symp" = #{clinSymp,jdbcType=VARCHAR},
      </if>
      <if test="physSign != null">
        "phys_sign" = #{physSign,jdbcType=VARCHAR},
      </if>
      <if test="relevantLabTest != null">
        "relevant_lab_test" = #{relevantLabTest,jdbcType=VARCHAR},
      </if>
      <if test="clinDiag != null">
        "clin_diag" = #{clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="relevantDiag != null">
        "relevant_diag" = #{relevantDiag,jdbcType=VARCHAR},
      </if>
      <if test="examClass != null">
        "exam_class" = #{examClass,jdbcType=VARCHAR},
      </if>
      <if test="examPosition != null">
        "exam_position" = #{examPosition,jdbcType=VARCHAR},
      </if>
      <if test="examMode != null">
        "exam_mode" = #{examMode,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        "device" = #{device,jdbcType=VARCHAR},
      </if>
      <if test="examPara != null">
        "exam_para" = #{examPara,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        "description" = #{description,jdbcType=VARCHAR},
      </if>
      <if test="conclusion != null">
        "conclusion" = #{conclusion,jdbcType=VARCHAR},
      </if>
      <if test="isAbnormal != null">
        "is_abnormal" = #{isAbnormal,jdbcType=VARCHAR},
      </if>
      <if test="reqDateTime != null">
        "req_date_time" = #{reqDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reqDept != null">
        "req_dept" = #{reqDept,jdbcType=VARCHAR},
      </if>
      <if test="reqDoctor != null">
        "req_doctor" = #{reqDoctor,jdbcType=VARCHAR},
      </if>
      <if test="notice != null">
        "notice" = #{notice,jdbcType=VARCHAR},
      </if>
      <if test="examDateTime != null">
        "exam_date_time" = #{examDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="performDept != null">
        "perform_dept" = #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="reportDateTime != null">
        "report_date_time" = #{reportDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDoctor != null">
        "report_doctor" = #{reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reviewDoctor != null">
        "review_doctor" = #{reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="imageNo != null">
        "image_no" = #{imageNo,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null">
        "image_path" = #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        "comment" = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="examSmallClass != null">
        "exam_small_class" = #{examSmallClass,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.ExamReport">
    update "public"."exam_report"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "report_no" = #{reportNo,jdbcType=VARCHAR},
      "order_no" = #{orderNo,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "gender" = #{gender,jdbcType=VARCHAR},
      "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      "patient_source" = #{patientSource,jdbcType=VARCHAR},
      "clin_symp" = #{clinSymp,jdbcType=VARCHAR},
      "phys_sign" = #{physSign,jdbcType=VARCHAR},
      "relevant_lab_test" = #{relevantLabTest,jdbcType=VARCHAR},
      "clin_diag" = #{clinDiag,jdbcType=VARCHAR},
      "relevant_diag" = #{relevantDiag,jdbcType=VARCHAR},
      "exam_class" = #{examClass,jdbcType=VARCHAR},
      "exam_position" = #{examPosition,jdbcType=VARCHAR},
      "exam_mode" = #{examMode,jdbcType=VARCHAR},
      "device" = #{device,jdbcType=VARCHAR},
      "exam_para" = #{examPara,jdbcType=VARCHAR},
      "description" = #{description,jdbcType=VARCHAR},
      "conclusion" = #{conclusion,jdbcType=VARCHAR},
      "is_abnormal" = #{isAbnormal,jdbcType=VARCHAR},
      "req_date_time" = #{reqDateTime,jdbcType=TIMESTAMP},
      "req_dept" = #{reqDept,jdbcType=VARCHAR},
      "req_doctor" = #{reqDoctor,jdbcType=VARCHAR},
      "notice" = #{notice,jdbcType=VARCHAR},
      "exam_date_time" = #{examDateTime,jdbcType=TIMESTAMP},
      "perform_dept" = #{performDept,jdbcType=VARCHAR},
      "report_date_time" = #{reportDateTime,jdbcType=TIMESTAMP},
      "report_doctor" = #{reportDoctor,jdbcType=VARCHAR},
      "review_doctor" = #{reviewDoctor,jdbcType=VARCHAR},
      "image_no" = #{imageNo,jdbcType=VARCHAR},
      "image_path" = #{imagePath,jdbcType=VARCHAR},
      "comment" = #{comment,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "exam_small_class" = #{examSmallClass,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getExamReport" resultMap="BaseResultMap">
    select * from exam_report where patient_sn = #{patientId} order by report_date_time
  </select>
</mapper>
