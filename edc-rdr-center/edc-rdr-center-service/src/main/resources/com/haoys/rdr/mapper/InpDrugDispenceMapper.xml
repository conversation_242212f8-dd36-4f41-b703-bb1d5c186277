<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.InpDrugDispenceMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.InpDrugDispence">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_group_id" jdbcType="VARCHAR" property="orderGroupId" />
    <result column="drug_name" jdbcType="VARCHAR" property="drugName" />
    <result column="drug_code" jdbcType="VARCHAR" property="drugCode" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="generic_name" jdbcType="VARCHAR" property="genericName" />
    <result column="drug_type" jdbcType="VARCHAR" property="drugType" />
    <result column="drug_form" jdbcType="VARCHAR" property="drugForm" />
    <result column="drug_spec" jdbcType="VARCHAR" property="drugSpec" />
    <result column="firm" jdbcType="VARCHAR" property="firm" />
    <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
    <result column="administration_route_code" jdbcType="VARCHAR" property="administrationRouteCode" />
    <result column="quantity" jdbcType="VARCHAR" property="quantity" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="days" jdbcType="VARCHAR" property="days" />
    <result column="times" jdbcType="VARCHAR" property="times" />
    <result column="costs" jdbcType="VARCHAR" property="costs" />
    <result column="drug_approval_number" jdbcType="VARCHAR" property="drugApprovalNumber" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="min_pack_unit" jdbcType="VARCHAR" property="minPackUnit" />
    <result column="min_pack_unit_code" jdbcType="VARCHAR" property="minPackUnitCode" />
    <result column="min_pack_dose" jdbcType="VARCHAR" property="minPackDose" />
    <result column="min_pack_dose_unit" jdbcType="VARCHAR" property="minPackDoseUnit" />
    <result column="min_pack_dose_unit_code" jdbcType="VARCHAR" property="minPackDoseUnitCode" />
    <result column="pack_unit" jdbcType="VARCHAR" property="packUnit" />
    <result column="pack_unit_code" jdbcType="VARCHAR" property="packUnitCode" />
    <result column="quantity_per_pack" jdbcType="VARCHAR" property="quantityPerPack" />
    <result column="order_dept" jdbcType="VARCHAR" property="orderDept" />
    <result column="order_dept_code" jdbcType="VARCHAR" property="orderDeptCode" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "record_id", "order_id", 
    "order_group_id", "drug_name", "drug_code", "brand_name", "generic_name", "drug_type", 
    "drug_form", "drug_spec", "firm", "administration_route", "administration_route_code", 
    "quantity", "unit", "unit_code", "days", "times", "costs", "drug_approval_number", 
    "batch_no", "expiration_date", "min_pack_unit", "min_pack_unit_code", "min_pack_dose", 
    "min_pack_dose_unit", "min_pack_dose_unit_code", "pack_unit", "pack_unit_code", "quantity_per_pack", 
    "order_dept", "order_dept_code", "status", "operation_time", "operator", "operator_id", 
    "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.InpDrugDispenceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."inp_drug_dispence"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."inp_drug_dispence"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."inp_drug_dispence"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.InpDrugDispenceExample">
    delete from "public"."inp_drug_dispence"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.InpDrugDispence">
    insert into "public"."inp_drug_dispence" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "record_id", 
      "order_id", "order_group_id", "drug_name", 
      "drug_code", "brand_name", "generic_name", 
      "drug_type", "drug_form", "drug_spec", 
      "firm", "administration_route", "administration_route_code", 
      "quantity", "unit", "unit_code", 
      "days", "times", "costs", 
      "drug_approval_number", "batch_no", "expiration_date", 
      "min_pack_unit", "min_pack_unit_code", "min_pack_dose", 
      "min_pack_dose_unit", "min_pack_dose_unit_code", "pack_unit", 
      "pack_unit_code", "quantity_per_pack", "order_dept", 
      "order_dept_code", "status", "operation_time", 
      "operator", "operator_id", "source_path", 
      "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{recordId,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{orderGroupId,jdbcType=VARCHAR}, #{drugName,jdbcType=VARCHAR}, 
      #{drugCode,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{genericName,jdbcType=VARCHAR}, 
      #{drugType,jdbcType=VARCHAR}, #{drugForm,jdbcType=VARCHAR}, #{drugSpec,jdbcType=VARCHAR}, 
      #{firm,jdbcType=VARCHAR}, #{administrationRoute,jdbcType=VARCHAR}, #{administrationRouteCode,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR}, 
      #{days,jdbcType=VARCHAR}, #{times,jdbcType=VARCHAR}, #{costs,jdbcType=VARCHAR}, 
      #{drugApprovalNumber,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{expirationDate,jdbcType=TIMESTAMP}, 
      #{minPackUnit,jdbcType=VARCHAR}, #{minPackUnitCode,jdbcType=VARCHAR}, #{minPackDose,jdbcType=VARCHAR}, 
      #{minPackDoseUnit,jdbcType=VARCHAR}, #{minPackDoseUnitCode,jdbcType=VARCHAR}, #{packUnit,jdbcType=VARCHAR}, 
      #{packUnitCode,jdbcType=VARCHAR}, #{quantityPerPack,jdbcType=VARCHAR}, #{orderDept,jdbcType=VARCHAR}, 
      #{orderDeptCode,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, 
      #{operator,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, 
      #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.InpDrugDispence">
    insert into "public"."inp_drug_dispence"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="recordId != null">
        "record_id",
      </if>
      <if test="orderId != null">
        "order_id",
      </if>
      <if test="orderGroupId != null">
        "order_group_id",
      </if>
      <if test="drugName != null">
        "drug_name",
      </if>
      <if test="drugCode != null">
        "drug_code",
      </if>
      <if test="brandName != null">
        "brand_name",
      </if>
      <if test="genericName != null">
        "generic_name",
      </if>
      <if test="drugType != null">
        "drug_type",
      </if>
      <if test="drugForm != null">
        "drug_form",
      </if>
      <if test="drugSpec != null">
        "drug_spec",
      </if>
      <if test="firm != null">
        "firm",
      </if>
      <if test="administrationRoute != null">
        "administration_route",
      </if>
      <if test="administrationRouteCode != null">
        "administration_route_code",
      </if>
      <if test="quantity != null">
        "quantity",
      </if>
      <if test="unit != null">
        "unit",
      </if>
      <if test="unitCode != null">
        "unit_code",
      </if>
      <if test="days != null">
        "days",
      </if>
      <if test="times != null">
        "times",
      </if>
      <if test="costs != null">
        "costs",
      </if>
      <if test="drugApprovalNumber != null">
        "drug_approval_number",
      </if>
      <if test="batchNo != null">
        "batch_no",
      </if>
      <if test="expirationDate != null">
        "expiration_date",
      </if>
      <if test="minPackUnit != null">
        "min_pack_unit",
      </if>
      <if test="minPackUnitCode != null">
        "min_pack_unit_code",
      </if>
      <if test="minPackDose != null">
        "min_pack_dose",
      </if>
      <if test="minPackDoseUnit != null">
        "min_pack_dose_unit",
      </if>
      <if test="minPackDoseUnitCode != null">
        "min_pack_dose_unit_code",
      </if>
      <if test="packUnit != null">
        "pack_unit",
      </if>
      <if test="packUnitCode != null">
        "pack_unit_code",
      </if>
      <if test="quantityPerPack != null">
        "quantity_per_pack",
      </if>
      <if test="orderDept != null">
        "order_dept",
      </if>
      <if test="orderDeptCode != null">
        "order_dept_code",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="operationTime != null">
        "operation_time",
      </if>
      <if test="operator != null">
        "operator",
      </if>
      <if test="operatorId != null">
        "operator_id",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderGroupId != null">
        #{orderGroupId,jdbcType=VARCHAR},
      </if>
      <if test="drugName != null">
        #{drugName,jdbcType=VARCHAR},
      </if>
      <if test="drugCode != null">
        #{drugCode,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="drugType != null">
        #{drugType,jdbcType=VARCHAR},
      </if>
      <if test="drugForm != null">
        #{drugForm,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="firm != null">
        #{firm,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="administrationRouteCode != null">
        #{administrationRouteCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="days != null">
        #{days,jdbcType=VARCHAR},
      </if>
      <if test="times != null">
        #{times,jdbcType=VARCHAR},
      </if>
      <if test="costs != null">
        #{costs,jdbcType=VARCHAR},
      </if>
      <if test="drugApprovalNumber != null">
        #{drugApprovalNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="minPackUnit != null">
        #{minPackUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackUnitCode != null">
        #{minPackUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="minPackDose != null">
        #{minPackDose,jdbcType=VARCHAR},
      </if>
      <if test="minPackDoseUnit != null">
        #{minPackDoseUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackDoseUnitCode != null">
        #{minPackDoseUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="packUnit != null">
        #{packUnit,jdbcType=VARCHAR},
      </if>
      <if test="packUnitCode != null">
        #{packUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="quantityPerPack != null">
        #{quantityPerPack,jdbcType=VARCHAR},
      </if>
      <if test="orderDept != null">
        #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDeptCode != null">
        #{orderDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.InpDrugDispenceExample" resultType="java.lang.Long">
    select count(*) from "public"."inp_drug_dispence"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."inp_drug_dispence"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.recordId != null">
        "record_id" = #{record.recordId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        "order_id" = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderGroupId != null">
        "order_group_id" = #{record.orderGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.drugName != null">
        "drug_name" = #{record.drugName,jdbcType=VARCHAR},
      </if>
      <if test="record.drugCode != null">
        "drug_code" = #{record.drugCode,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.genericName != null">
        "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      </if>
      <if test="record.drugType != null">
        "drug_type" = #{record.drugType,jdbcType=VARCHAR},
      </if>
      <if test="record.drugForm != null">
        "drug_form" = #{record.drugForm,jdbcType=VARCHAR},
      </if>
      <if test="record.drugSpec != null">
        "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.firm != null">
        "firm" = #{record.firm,jdbcType=VARCHAR},
      </if>
      <if test="record.administrationRoute != null">
        "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.administrationRouteCode != null">
        "administration_route_code" = #{record.administrationRouteCode,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        "quantity" = #{record.quantity,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        "unit" = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.unitCode != null">
        "unit_code" = #{record.unitCode,jdbcType=VARCHAR},
      </if>
      <if test="record.days != null">
        "days" = #{record.days,jdbcType=VARCHAR},
      </if>
      <if test="record.times != null">
        "times" = #{record.times,jdbcType=VARCHAR},
      </if>
      <if test="record.costs != null">
        "costs" = #{record.costs,jdbcType=VARCHAR},
      </if>
      <if test="record.drugApprovalNumber != null">
        "drug_approval_number" = #{record.drugApprovalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        "batch_no" = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expirationDate != null">
        "expiration_date" = #{record.expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.minPackUnit != null">
        "min_pack_unit" = #{record.minPackUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.minPackUnitCode != null">
        "min_pack_unit_code" = #{record.minPackUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="record.minPackDose != null">
        "min_pack_dose" = #{record.minPackDose,jdbcType=VARCHAR},
      </if>
      <if test="record.minPackDoseUnit != null">
        "min_pack_dose_unit" = #{record.minPackDoseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.minPackDoseUnitCode != null">
        "min_pack_dose_unit_code" = #{record.minPackDoseUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="record.packUnit != null">
        "pack_unit" = #{record.packUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.packUnitCode != null">
        "pack_unit_code" = #{record.packUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="record.quantityPerPack != null">
        "quantity_per_pack" = #{record.quantityPerPack,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDept != null">
        "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDeptCode != null">
        "order_dept_code" = #{record.orderDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.operationTime != null">
        "operation_time" = #{record.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        "operator" = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        "operator_id" = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."inp_drug_dispence"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "record_id" = #{record.recordId,jdbcType=VARCHAR},
      "order_id" = #{record.orderId,jdbcType=VARCHAR},
      "order_group_id" = #{record.orderGroupId,jdbcType=VARCHAR},
      "drug_name" = #{record.drugName,jdbcType=VARCHAR},
      "drug_code" = #{record.drugCode,jdbcType=VARCHAR},
      "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      "drug_type" = #{record.drugType,jdbcType=VARCHAR},
      "drug_form" = #{record.drugForm,jdbcType=VARCHAR},
      "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      "firm" = #{record.firm,jdbcType=VARCHAR},
      "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      "administration_route_code" = #{record.administrationRouteCode,jdbcType=VARCHAR},
      "quantity" = #{record.quantity,jdbcType=VARCHAR},
      "unit" = #{record.unit,jdbcType=VARCHAR},
      "unit_code" = #{record.unitCode,jdbcType=VARCHAR},
      "days" = #{record.days,jdbcType=VARCHAR},
      "times" = #{record.times,jdbcType=VARCHAR},
      "costs" = #{record.costs,jdbcType=VARCHAR},
      "drug_approval_number" = #{record.drugApprovalNumber,jdbcType=VARCHAR},
      "batch_no" = #{record.batchNo,jdbcType=VARCHAR},
      "expiration_date" = #{record.expirationDate,jdbcType=TIMESTAMP},
      "min_pack_unit" = #{record.minPackUnit,jdbcType=VARCHAR},
      "min_pack_unit_code" = #{record.minPackUnitCode,jdbcType=VARCHAR},
      "min_pack_dose" = #{record.minPackDose,jdbcType=VARCHAR},
      "min_pack_dose_unit" = #{record.minPackDoseUnit,jdbcType=VARCHAR},
      "min_pack_dose_unit_code" = #{record.minPackDoseUnitCode,jdbcType=VARCHAR},
      "pack_unit" = #{record.packUnit,jdbcType=VARCHAR},
      "pack_unit_code" = #{record.packUnitCode,jdbcType=VARCHAR},
      "quantity_per_pack" = #{record.quantityPerPack,jdbcType=VARCHAR},
      "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      "order_dept_code" = #{record.orderDeptCode,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "operation_time" = #{record.operationTime,jdbcType=TIMESTAMP},
      "operator" = #{record.operator,jdbcType=VARCHAR},
      "operator_id" = #{record.operatorId,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.InpDrugDispence">
    update "public"."inp_drug_dispence"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="recordId != null">
        "record_id" = #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        "order_id" = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderGroupId != null">
        "order_group_id" = #{orderGroupId,jdbcType=VARCHAR},
      </if>
      <if test="drugName != null">
        "drug_name" = #{drugName,jdbcType=VARCHAR},
      </if>
      <if test="drugCode != null">
        "drug_code" = #{drugCode,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        "brand_name" = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        "generic_name" = #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="drugType != null">
        "drug_type" = #{drugType,jdbcType=VARCHAR},
      </if>
      <if test="drugForm != null">
        "drug_form" = #{drugForm,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="firm != null">
        "firm" = #{firm,jdbcType=VARCHAR},
      </if>
      <if test="administrationRoute != null">
        "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="administrationRouteCode != null">
        "administration_route_code" = #{administrationRouteCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        "quantity" = #{quantity,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        "unit" = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        "unit_code" = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="days != null">
        "days" = #{days,jdbcType=VARCHAR},
      </if>
      <if test="times != null">
        "times" = #{times,jdbcType=VARCHAR},
      </if>
      <if test="costs != null">
        "costs" = #{costs,jdbcType=VARCHAR},
      </if>
      <if test="drugApprovalNumber != null">
        "drug_approval_number" = #{drugApprovalNumber,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        "batch_no" = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        "expiration_date" = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="minPackUnit != null">
        "min_pack_unit" = #{minPackUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackUnitCode != null">
        "min_pack_unit_code" = #{minPackUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="minPackDose != null">
        "min_pack_dose" = #{minPackDose,jdbcType=VARCHAR},
      </if>
      <if test="minPackDoseUnit != null">
        "min_pack_dose_unit" = #{minPackDoseUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackDoseUnitCode != null">
        "min_pack_dose_unit_code" = #{minPackDoseUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="packUnit != null">
        "pack_unit" = #{packUnit,jdbcType=VARCHAR},
      </if>
      <if test="packUnitCode != null">
        "pack_unit_code" = #{packUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="quantityPerPack != null">
        "quantity_per_pack" = #{quantityPerPack,jdbcType=VARCHAR},
      </if>
      <if test="orderDept != null">
        "order_dept" = #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDeptCode != null">
        "order_dept_code" = #{orderDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        "operation_time" = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        "operator" = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        "operator_id" = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.InpDrugDispence">
    update "public"."inp_drug_dispence"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "record_id" = #{recordId,jdbcType=VARCHAR},
      "order_id" = #{orderId,jdbcType=VARCHAR},
      "order_group_id" = #{orderGroupId,jdbcType=VARCHAR},
      "drug_name" = #{drugName,jdbcType=VARCHAR},
      "drug_code" = #{drugCode,jdbcType=VARCHAR},
      "brand_name" = #{brandName,jdbcType=VARCHAR},
      "generic_name" = #{genericName,jdbcType=VARCHAR},
      "drug_type" = #{drugType,jdbcType=VARCHAR},
      "drug_form" = #{drugForm,jdbcType=VARCHAR},
      "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      "firm" = #{firm,jdbcType=VARCHAR},
      "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      "administration_route_code" = #{administrationRouteCode,jdbcType=VARCHAR},
      "quantity" = #{quantity,jdbcType=VARCHAR},
      "unit" = #{unit,jdbcType=VARCHAR},
      "unit_code" = #{unitCode,jdbcType=VARCHAR},
      "days" = #{days,jdbcType=VARCHAR},
      "times" = #{times,jdbcType=VARCHAR},
      "costs" = #{costs,jdbcType=VARCHAR},
      "drug_approval_number" = #{drugApprovalNumber,jdbcType=VARCHAR},
      "batch_no" = #{batchNo,jdbcType=VARCHAR},
      "expiration_date" = #{expirationDate,jdbcType=TIMESTAMP},
      "min_pack_unit" = #{minPackUnit,jdbcType=VARCHAR},
      "min_pack_unit_code" = #{minPackUnitCode,jdbcType=VARCHAR},
      "min_pack_dose" = #{minPackDose,jdbcType=VARCHAR},
      "min_pack_dose_unit" = #{minPackDoseUnit,jdbcType=VARCHAR},
      "min_pack_dose_unit_code" = #{minPackDoseUnitCode,jdbcType=VARCHAR},
      "pack_unit" = #{packUnit,jdbcType=VARCHAR},
      "pack_unit_code" = #{packUnitCode,jdbcType=VARCHAR},
      "quantity_per_pack" = #{quantityPerPack,jdbcType=VARCHAR},
      "order_dept" = #{orderDept,jdbcType=VARCHAR},
      "order_dept_code" = #{orderDeptCode,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "operation_time" = #{operationTime,jdbcType=TIMESTAMP},
      "operator" = #{operator,jdbcType=VARCHAR},
      "operator_id" = #{operatorId,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>