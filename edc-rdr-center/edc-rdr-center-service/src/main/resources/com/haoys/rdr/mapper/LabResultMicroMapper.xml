<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.LabResultMicroMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.LabResultMicro">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="test_no" jdbcType="VARCHAR" property="testNo" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="print_order" jdbcType="VARCHAR" property="printOrder" />
    <result column="spcm_type" jdbcType="VARCHAR" property="spcmType" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="test_method" jdbcType="VARCHAR" property="testMethod" />
    <result column="culture_result" jdbcType="VARCHAR" property="cultureResult" />
    <result column="germ_code" jdbcType="VARCHAR" property="germCode" />
    <result column="germ_name" jdbcType="VARCHAR" property="germName" />
    <result column="germ_result_value" jdbcType="VARCHAR" property="germResultValue" />
    <result column="anti_code" jdbcType="VARCHAR" property="antiCode" />
    <result column="anti_name" jdbcType="VARCHAR" property="antiName" />
    <result column="anti_quantitative_result" jdbcType="DOUBLE" property="antiQuantitativeResult" />
    <result column="anti_qualitative_result" jdbcType="VARCHAR" property="antiQualitativeResult" />
    <result column="num_result" jdbcType="DOUBLE" property="numResult" />
    <result column="resistance_flag" jdbcType="VARCHAR" property="resistanceFlag" />
    <result column="normal_range" jdbcType="VARCHAR" property="normalRange" />
    <result column="equipment" jdbcType="VARCHAR" property="equipment" />
    <result column="result_date_time" jdbcType="TIMESTAMP" property="resultDateTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="test_group_items" jdbcType="VARCHAR" property="testGroupItems" />
    <result column="spcm_sample_date_time" jdbcType="TIMESTAMP" property="spcmSampleDateTime" />
    <result column="req_dept" jdbcType="VARCHAR" property="reqDept" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "test_no", "item_no", "print_order", "spcm_type",
    "item_name", "test_method", "culture_result", "germ_code", "germ_name", "germ_result_value",
    "anti_code", "anti_name", "anti_quantitative_result", "anti_qualitative_result",
    "num_result", "resistance_flag", "normal_range", "equipment", "result_date_time",
    "source_path", "pk_id", "data_state", "test_group_items", "spcm_sample_date_time",
    "req_dept", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.LabResultMicroExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."lab_result_micro"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."lab_result_micro"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."lab_result_micro"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.LabResultMicroExample">
    delete from "public"."lab_result_micro"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.LabResultMicro">
    insert into "public"."lab_result_micro" ("hospital_code", "patient_sn", "visit_sn",
      "test_no", "item_no", "print_order",
      "spcm_type", "item_name", "test_method",
      "culture_result", "germ_code", "germ_name",
      "germ_result_value", "anti_code", "anti_name",
      "anti_quantitative_result", "anti_qualitative_result",
      "num_result", "resistance_flag", "normal_range",
      "equipment", "result_date_time", "source_path",
      "pk_id", "data_state", "test_group_items",
      "spcm_sample_date_time", "req_dept", "patient_sn_org"
      )
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{testNo,jdbcType=VARCHAR}, #{itemNo,jdbcType=VARCHAR}, #{printOrder,jdbcType=VARCHAR},
      #{spcmType,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, #{testMethod,jdbcType=VARCHAR},
      #{cultureResult,jdbcType=VARCHAR}, #{germCode,jdbcType=VARCHAR}, #{germName,jdbcType=VARCHAR},
      #{germResultValue,jdbcType=VARCHAR}, #{antiCode,jdbcType=VARCHAR}, #{antiName,jdbcType=VARCHAR},
      #{antiQuantitativeResult,jdbcType=DOUBLE}, #{antiQualitativeResult,jdbcType=VARCHAR},
      #{numResult,jdbcType=DOUBLE}, #{resistanceFlag,jdbcType=VARCHAR}, #{normalRange,jdbcType=VARCHAR},
      #{equipment,jdbcType=VARCHAR}, #{resultDateTime,jdbcType=TIMESTAMP}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{testGroupItems,jdbcType=VARCHAR},
      #{spcmSampleDateTime,jdbcType=TIMESTAMP}, #{reqDept,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.LabResultMicro">
    insert into "public"."lab_result_micro"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="testNo != null">
        "test_no",
      </if>
      <if test="itemNo != null">
        "item_no",
      </if>
      <if test="printOrder != null">
        "print_order",
      </if>
      <if test="spcmType != null">
        "spcm_type",
      </if>
      <if test="itemName != null">
        "item_name",
      </if>
      <if test="testMethod != null">
        "test_method",
      </if>
      <if test="cultureResult != null">
        "culture_result",
      </if>
      <if test="germCode != null">
        "germ_code",
      </if>
      <if test="germName != null">
        "germ_name",
      </if>
      <if test="germResultValue != null">
        "germ_result_value",
      </if>
      <if test="antiCode != null">
        "anti_code",
      </if>
      <if test="antiName != null">
        "anti_name",
      </if>
      <if test="antiQuantitativeResult != null">
        "anti_quantitative_result",
      </if>
      <if test="antiQualitativeResult != null">
        "anti_qualitative_result",
      </if>
      <if test="numResult != null">
        "num_result",
      </if>
      <if test="resistanceFlag != null">
        "resistance_flag",
      </if>
      <if test="normalRange != null">
        "normal_range",
      </if>
      <if test="equipment != null">
        "equipment",
      </if>
      <if test="resultDateTime != null">
        "result_date_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="testGroupItems != null">
        "test_group_items",
      </if>
      <if test="spcmSampleDateTime != null">
        "spcm_sample_date_time",
      </if>
      <if test="reqDept != null">
        "req_dept",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="testNo != null">
        #{testNo,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="printOrder != null">
        #{printOrder,jdbcType=VARCHAR},
      </if>
      <if test="spcmType != null">
        #{spcmType,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="testMethod != null">
        #{testMethod,jdbcType=VARCHAR},
      </if>
      <if test="cultureResult != null">
        #{cultureResult,jdbcType=VARCHAR},
      </if>
      <if test="germCode != null">
        #{germCode,jdbcType=VARCHAR},
      </if>
      <if test="germName != null">
        #{germName,jdbcType=VARCHAR},
      </if>
      <if test="germResultValue != null">
        #{germResultValue,jdbcType=VARCHAR},
      </if>
      <if test="antiCode != null">
        #{antiCode,jdbcType=VARCHAR},
      </if>
      <if test="antiName != null">
        #{antiName,jdbcType=VARCHAR},
      </if>
      <if test="antiQuantitativeResult != null">
        #{antiQuantitativeResult,jdbcType=DOUBLE},
      </if>
      <if test="antiQualitativeResult != null">
        #{antiQualitativeResult,jdbcType=VARCHAR},
      </if>
      <if test="numResult != null">
        #{numResult,jdbcType=DOUBLE},
      </if>
      <if test="resistanceFlag != null">
        #{resistanceFlag,jdbcType=VARCHAR},
      </if>
      <if test="normalRange != null">
        #{normalRange,jdbcType=VARCHAR},
      </if>
      <if test="equipment != null">
        #{equipment,jdbcType=VARCHAR},
      </if>
      <if test="resultDateTime != null">
        #{resultDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="testGroupItems != null">
        #{testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="spcmSampleDateTime != null">
        #{spcmSampleDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reqDept != null">
        #{reqDept,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.LabResultMicroExample" resultType="java.lang.Long">
    select count(*) from "public"."lab_result_micro"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."lab_result_micro"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.testNo != null">
        "test_no" = #{record.testNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemNo != null">
        "item_no" = #{record.itemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.printOrder != null">
        "print_order" = #{record.printOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.spcmType != null">
        "spcm_type" = #{record.spcmType,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        "item_name" = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.testMethod != null">
        "test_method" = #{record.testMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.cultureResult != null">
        "culture_result" = #{record.cultureResult,jdbcType=VARCHAR},
      </if>
      <if test="record.germCode != null">
        "germ_code" = #{record.germCode,jdbcType=VARCHAR},
      </if>
      <if test="record.germName != null">
        "germ_name" = #{record.germName,jdbcType=VARCHAR},
      </if>
      <if test="record.germResultValue != null">
        "germ_result_value" = #{record.germResultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.antiCode != null">
        "anti_code" = #{record.antiCode,jdbcType=VARCHAR},
      </if>
      <if test="record.antiName != null">
        "anti_name" = #{record.antiName,jdbcType=VARCHAR},
      </if>
      <if test="record.antiQuantitativeResult != null">
        "anti_quantitative_result" = #{record.antiQuantitativeResult,jdbcType=DOUBLE},
      </if>
      <if test="record.antiQualitativeResult != null">
        "anti_qualitative_result" = #{record.antiQualitativeResult,jdbcType=VARCHAR},
      </if>
      <if test="record.numResult != null">
        "num_result" = #{record.numResult,jdbcType=DOUBLE},
      </if>
      <if test="record.resistanceFlag != null">
        "resistance_flag" = #{record.resistanceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.normalRange != null">
        "normal_range" = #{record.normalRange,jdbcType=VARCHAR},
      </if>
      <if test="record.equipment != null">
        "equipment" = #{record.equipment,jdbcType=VARCHAR},
      </if>
      <if test="record.resultDateTime != null">
        "result_date_time" = #{record.resultDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.testGroupItems != null">
        "test_group_items" = #{record.testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="record.spcmSampleDateTime != null">
        "spcm_sample_date_time" = #{record.spcmSampleDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reqDept != null">
        "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."lab_result_micro"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "test_no" = #{record.testNo,jdbcType=VARCHAR},
      "item_no" = #{record.itemNo,jdbcType=VARCHAR},
      "print_order" = #{record.printOrder,jdbcType=VARCHAR},
      "spcm_type" = #{record.spcmType,jdbcType=VARCHAR},
      "item_name" = #{record.itemName,jdbcType=VARCHAR},
      "test_method" = #{record.testMethod,jdbcType=VARCHAR},
      "culture_result" = #{record.cultureResult,jdbcType=VARCHAR},
      "germ_code" = #{record.germCode,jdbcType=VARCHAR},
      "germ_name" = #{record.germName,jdbcType=VARCHAR},
      "germ_result_value" = #{record.germResultValue,jdbcType=VARCHAR},
      "anti_code" = #{record.antiCode,jdbcType=VARCHAR},
      "anti_name" = #{record.antiName,jdbcType=VARCHAR},
      "anti_quantitative_result" = #{record.antiQuantitativeResult,jdbcType=DOUBLE},
      "anti_qualitative_result" = #{record.antiQualitativeResult,jdbcType=VARCHAR},
      "num_result" = #{record.numResult,jdbcType=DOUBLE},
      "resistance_flag" = #{record.resistanceFlag,jdbcType=VARCHAR},
      "normal_range" = #{record.normalRange,jdbcType=VARCHAR},
      "equipment" = #{record.equipment,jdbcType=VARCHAR},
      "result_date_time" = #{record.resultDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "test_group_items" = #{record.testGroupItems,jdbcType=VARCHAR},
      "spcm_sample_date_time" = #{record.spcmSampleDateTime,jdbcType=TIMESTAMP},
      "req_dept" = #{record.reqDept,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.LabResultMicro">
    update "public"."lab_result_micro"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="testNo != null">
        "test_no" = #{testNo,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        "item_no" = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="printOrder != null">
        "print_order" = #{printOrder,jdbcType=VARCHAR},
      </if>
      <if test="spcmType != null">
        "spcm_type" = #{spcmType,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        "item_name" = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="testMethod != null">
        "test_method" = #{testMethod,jdbcType=VARCHAR},
      </if>
      <if test="cultureResult != null">
        "culture_result" = #{cultureResult,jdbcType=VARCHAR},
      </if>
      <if test="germCode != null">
        "germ_code" = #{germCode,jdbcType=VARCHAR},
      </if>
      <if test="germName != null">
        "germ_name" = #{germName,jdbcType=VARCHAR},
      </if>
      <if test="germResultValue != null">
        "germ_result_value" = #{germResultValue,jdbcType=VARCHAR},
      </if>
      <if test="antiCode != null">
        "anti_code" = #{antiCode,jdbcType=VARCHAR},
      </if>
      <if test="antiName != null">
        "anti_name" = #{antiName,jdbcType=VARCHAR},
      </if>
      <if test="antiQuantitativeResult != null">
        "anti_quantitative_result" = #{antiQuantitativeResult,jdbcType=DOUBLE},
      </if>
      <if test="antiQualitativeResult != null">
        "anti_qualitative_result" = #{antiQualitativeResult,jdbcType=VARCHAR},
      </if>
      <if test="numResult != null">
        "num_result" = #{numResult,jdbcType=DOUBLE},
      </if>
      <if test="resistanceFlag != null">
        "resistance_flag" = #{resistanceFlag,jdbcType=VARCHAR},
      </if>
      <if test="normalRange != null">
        "normal_range" = #{normalRange,jdbcType=VARCHAR},
      </if>
      <if test="equipment != null">
        "equipment" = #{equipment,jdbcType=VARCHAR},
      </if>
      <if test="resultDateTime != null">
        "result_date_time" = #{resultDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="testGroupItems != null">
        "test_group_items" = #{testGroupItems,jdbcType=VARCHAR},
      </if>
      <if test="spcmSampleDateTime != null">
        "spcm_sample_date_time" = #{spcmSampleDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reqDept != null">
        "req_dept" = #{reqDept,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.LabResultMicro">
    update "public"."lab_result_micro"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "test_no" = #{testNo,jdbcType=VARCHAR},
      "item_no" = #{itemNo,jdbcType=VARCHAR},
      "print_order" = #{printOrder,jdbcType=VARCHAR},
      "spcm_type" = #{spcmType,jdbcType=VARCHAR},
      "item_name" = #{itemName,jdbcType=VARCHAR},
      "test_method" = #{testMethod,jdbcType=VARCHAR},
      "culture_result" = #{cultureResult,jdbcType=VARCHAR},
      "germ_code" = #{germCode,jdbcType=VARCHAR},
      "germ_name" = #{germName,jdbcType=VARCHAR},
      "germ_result_value" = #{germResultValue,jdbcType=VARCHAR},
      "anti_code" = #{antiCode,jdbcType=VARCHAR},
      "anti_name" = #{antiName,jdbcType=VARCHAR},
      "anti_quantitative_result" = #{antiQuantitativeResult,jdbcType=DOUBLE},
      "anti_qualitative_result" = #{antiQualitativeResult,jdbcType=VARCHAR},
      "num_result" = #{numResult,jdbcType=DOUBLE},
      "resistance_flag" = #{resistanceFlag,jdbcType=VARCHAR},
      "normal_range" = #{normalRange,jdbcType=VARCHAR},
      "equipment" = #{equipment,jdbcType=VARCHAR},
      "result_date_time" = #{resultDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "test_group_items" = #{testGroupItems,jdbcType=VARCHAR},
      "spcm_sample_date_time" = #{spcmSampleDateTime,jdbcType=TIMESTAMP},
      "req_dept" = #{reqDept,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getLabResultMicroList" resultMap="BaseResultMap">
    select * from "public"."lab_result_micro" where patient_sn = #{patientSn,jdbcType=VARCHAR} and visit_sn = #{visitSn,jdbcType=VARCHAR}
  </select>

</mapper>
