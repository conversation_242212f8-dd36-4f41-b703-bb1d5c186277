<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.MrHomepageDiagMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.MrHomepageDiag">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="admission_date_time" jdbcType="TIMESTAMP" property="admissionDateTime" />
    <result column="discharge_date_time" jdbcType="TIMESTAMP" property="dischargeDateTime" />
    <result column="diagnosis_type" jdbcType="VARCHAR" property="diagnosisType" />
    <result column="diagnosis_class" jdbcType="VARCHAR" property="diagnosisClass" />
    <result column="diagnosis_order_no" jdbcType="VARCHAR" property="diagnosisOrderNo" />
    <result column="diagnosis_name" jdbcType="VARCHAR" property="diagnosisName" />
    <result column="diagnosis_code" jdbcType="VARCHAR" property="diagnosisCode" />
    <result column="diagnosis_code2" jdbcType="VARCHAR" property="diagnosisCode2" />
    <result column="is_main" jdbcType="VARCHAR" property="isMain" />
    <result column="pathologic_no" jdbcType="VARCHAR" property="pathologicNo" />
    <result column="admission_status" jdbcType="VARCHAR" property="admissionStatus" />
    <result column="dis_diag_admit_stat" jdbcType="VARCHAR" property="disDiagAdmitStat" />
    <result column="dis_diag_dis_stat" jdbcType="VARCHAR" property="disDiagDisStat" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="diagnosis_date" jdbcType="TIMESTAMP" property="diagnosisDate" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "admission_date_time", "discharge_date_time",
    "diagnosis_type", "diagnosis_class", "diagnosis_order_no", "diagnosis_name", "diagnosis_code",
    "diagnosis_code2", "is_main", "pathologic_no", "admission_status", "dis_diag_admit_stat",
    "dis_diag_dis_stat", "source_path", "pk_id", "data_state", "diagnosis_date", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.MrHomepageDiagExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_diag"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."mr_homepage_diag"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.MrHomepageDiagExample">
    delete from "public"."mr_homepage_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.MrHomepageDiag">
    insert into "public"."mr_homepage_diag" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "admission_date_time", "discharge_date_time",
      "diagnosis_type", "diagnosis_class", "diagnosis_order_no",
      "diagnosis_name", "diagnosis_code", "diagnosis_code2",
      "is_main", "pathologic_no", "admission_status",
      "dis_diag_admit_stat", "dis_diag_dis_stat", "source_path",
      "pk_id", "data_state", "diagnosis_date",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{admissionDateTime,jdbcType=TIMESTAMP}, #{dischargeDateTime,jdbcType=TIMESTAMP},
      #{diagnosisType,jdbcType=VARCHAR}, #{diagnosisClass,jdbcType=VARCHAR}, #{diagnosisOrderNo,jdbcType=VARCHAR},
      #{diagnosisName,jdbcType=VARCHAR}, #{diagnosisCode,jdbcType=VARCHAR}, #{diagnosisCode2,jdbcType=VARCHAR},
      #{isMain,jdbcType=VARCHAR}, #{pathologicNo,jdbcType=VARCHAR}, #{admissionStatus,jdbcType=VARCHAR},
      #{disDiagAdmitStat,jdbcType=VARCHAR}, #{disDiagDisStat,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{diagnosisDate,jdbcType=TIMESTAMP},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.MrHomepageDiag">
    insert into "public"."mr_homepage_diag"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time",
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time",
      </if>
      <if test="diagnosisType != null">
        "diagnosis_type",
      </if>
      <if test="diagnosisClass != null">
        "diagnosis_class",
      </if>
      <if test="diagnosisOrderNo != null">
        "diagnosis_order_no",
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name",
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code",
      </if>
      <if test="diagnosisCode2 != null">
        "diagnosis_code2",
      </if>
      <if test="isMain != null">
        "is_main",
      </if>
      <if test="pathologicNo != null">
        "pathologic_no",
      </if>
      <if test="admissionStatus != null">
        "admission_status",
      </if>
      <if test="disDiagAdmitStat != null">
        "dis_diag_admit_stat",
      </if>
      <if test="disDiagDisStat != null">
        "dis_diag_dis_stat",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="diagnosisDate != null">
        "diagnosis_date",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDateTime != null">
        #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diagnosisType != null">
        #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClass != null">
        #{diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisOrderNo != null">
        #{diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode2 != null">
        #{diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        #{isMain,jdbcType=VARCHAR},
      </if>
      <if test="pathologicNo != null">
        #{pathologicNo,jdbcType=VARCHAR},
      </if>
      <if test="admissionStatus != null">
        #{admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="disDiagAdmitStat != null">
        #{disDiagAdmitStat,jdbcType=VARCHAR},
      </if>
      <if test="disDiagDisStat != null">
        #{disDiagDisStat,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisDate != null">
        #{diagnosisDate,jdbcType=TIMESTAMP},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.MrHomepageDiagExample" resultType="java.lang.Long">
    select count(*) from "public"."mr_homepage_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."mr_homepage_diag"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionDateTime != null">
        "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dischargeDateTime != null">
        "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.diagnosisType != null">
        "diagnosis_type" = #{record.diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisClass != null">
        "diagnosis_class" = #{record.diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisOrderNo != null">
        "diagnosis_order_no" = #{record.diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisName != null">
        "diagnosis_name" = #{record.diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisCode != null">
        "diagnosis_code" = #{record.diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisCode2 != null">
        "diagnosis_code2" = #{record.diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="record.isMain != null">
        "is_main" = #{record.isMain,jdbcType=VARCHAR},
      </if>
      <if test="record.pathologicNo != null">
        "pathologic_no" = #{record.pathologicNo,jdbcType=VARCHAR},
      </if>
      <if test="record.admissionStatus != null">
        "admission_status" = #{record.admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.disDiagAdmitStat != null">
        "dis_diag_admit_stat" = #{record.disDiagAdmitStat,jdbcType=VARCHAR},
      </if>
      <if test="record.disDiagDisStat != null">
        "dis_diag_dis_stat" = #{record.disDiagDisStat,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisDate != null">
        "diagnosis_date" = #{record.diagnosisDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."mr_homepage_diag"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "admission_date_time" = #{record.admissionDateTime,jdbcType=TIMESTAMP},
      "discharge_date_time" = #{record.dischargeDateTime,jdbcType=TIMESTAMP},
      "diagnosis_type" = #{record.diagnosisType,jdbcType=VARCHAR},
      "diagnosis_class" = #{record.diagnosisClass,jdbcType=VARCHAR},
      "diagnosis_order_no" = #{record.diagnosisOrderNo,jdbcType=VARCHAR},
      "diagnosis_name" = #{record.diagnosisName,jdbcType=VARCHAR},
      "diagnosis_code" = #{record.diagnosisCode,jdbcType=VARCHAR},
      "diagnosis_code2" = #{record.diagnosisCode2,jdbcType=VARCHAR},
      "is_main" = #{record.isMain,jdbcType=VARCHAR},
      "pathologic_no" = #{record.pathologicNo,jdbcType=VARCHAR},
      "admission_status" = #{record.admissionStatus,jdbcType=VARCHAR},
      "dis_diag_admit_stat" = #{record.disDiagAdmitStat,jdbcType=VARCHAR},
      "dis_diag_dis_stat" = #{record.disDiagDisStat,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "diagnosis_date" = #{record.diagnosisDate,jdbcType=TIMESTAMP},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.MrHomepageDiag">
    update "public"."mr_homepage_diag"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diagnosisType != null">
        "diagnosis_type" = #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClass != null">
        "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisOrderNo != null">
        "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode2 != null">
        "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        "is_main" = #{isMain,jdbcType=VARCHAR},
      </if>
      <if test="pathologicNo != null">
        "pathologic_no" = #{pathologicNo,jdbcType=VARCHAR},
      </if>
      <if test="admissionStatus != null">
        "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="disDiagAdmitStat != null">
        "dis_diag_admit_stat" = #{disDiagAdmitStat,jdbcType=VARCHAR},
      </if>
      <if test="disDiagDisStat != null">
        "dis_diag_dis_stat" = #{disDiagDisStat,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisDate != null">
        "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.MrHomepageDiag">
    update "public"."mr_homepage_diag"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      "diagnosis_type" = #{diagnosisType,jdbcType=VARCHAR},
      "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      "is_main" = #{isMain,jdbcType=VARCHAR},
      "pathologic_no" = #{pathologicNo,jdbcType=VARCHAR},
      "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      "dis_diag_admit_stat" = #{disDiagAdmitStat,jdbcType=VARCHAR},
      "dis_diag_dis_stat" = #{disDiagDisStat,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
