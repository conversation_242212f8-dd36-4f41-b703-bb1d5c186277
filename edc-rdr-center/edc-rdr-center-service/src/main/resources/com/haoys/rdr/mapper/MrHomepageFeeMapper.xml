<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.MrHomepageFeeMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.MrHomepageFee">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="total_costs" jdbcType="DOUBLE" property="totalCosts" />
    <result column="self_pay" jdbcType="DOUBLE" property="selfPay" />
    <result column="general_medical_service_fee" jdbcType="DOUBLE" property="generalMedicalServiceFee" />
    <result column="general_treatment_fee" jdbcType="DOUBLE" property="generalTreatmentFee" />
    <result column="nursing_care_fee" jdbcType="DOUBLE" property="nursingCareFee" />
    <result column="other_service_fee" jdbcType="DOUBLE" property="otherServiceFee" />
    <result column="pathological_diagnostic_fee" jdbcType="DOUBLE" property="pathologicalDiagnosticFee" />
    <result column="laboratory_diagnostic_fee" jdbcType="DOUBLE" property="laboratoryDiagnosticFee" />
    <result column="imaging_diagnostic_fee" jdbcType="DOUBLE" property="imagingDiagnosticFee" />
    <result column="clinical_diagnostic_fee" jdbcType="DOUBLE" property="clinicalDiagnosticFee" />
    <result column="nonsurgical_treatment_fee" jdbcType="DOUBLE" property="nonsurgicalTreatmentFee" />
    <result column="clinical_physical_therapy_fee" jdbcType="DOUBLE" property="clinicalPhysicalTherapyFee" />
    <result column="surgery_treatment_fee" jdbcType="DOUBLE" property="surgeryTreatmentFee" />
    <result column="anesthesia_fee" jdbcType="DOUBLE" property="anesthesiaFee" />
    <result column="surgery_fee" jdbcType="DOUBLE" property="surgeryFee" />
    <result column="rehabilitation_fee" jdbcType="DOUBLE" property="rehabilitationFee" />
    <result column="chinese_medicine_treatment_fee" jdbcType="DOUBLE" property="chineseMedicineTreatmentFee" />
    <result column="western_medicine_fee" jdbcType="DOUBLE" property="westernMedicineFee" />
    <result column="antibacterial_drug_fee" jdbcType="DOUBLE" property="antibacterialDrugFee" />
    <result column="chinese_patent_medcine_fee" jdbcType="DOUBLE" property="chinesePatentMedcineFee" />
    <result column="chinese_herbal_medicine_fee" jdbcType="DOUBLE" property="chineseHerbalMedicineFee" />
    <result column="blood_fee" jdbcType="DOUBLE" property="bloodFee" />
    <result column="albumin_fee" jdbcType="DOUBLE" property="albuminFee" />
    <result column="globulin_fee" jdbcType="DOUBLE" property="globulinFee" />
    <result column="blood_coagulation_factor_fee" jdbcType="DOUBLE" property="bloodCoagulationFactorFee" />
    <result column="cytokine_fee" jdbcType="DOUBLE" property="cytokineFee" />
    <result column="exam_disposable_mat_fee" jdbcType="DOUBLE" property="examDisposableMatFee" />
    <result column="treat_disposable_mat_fee" jdbcType="DOUBLE" property="treatDisposableMatFee" />
    <result column="surgery_disposable_mat_fee" jdbcType="DOUBLE" property="surgeryDisposableMatFee" />
    <result column="other_fee" jdbcType="DOUBLE" property="otherFee" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "total_costs", "self_pay", "general_medical_service_fee",
    "general_treatment_fee", "nursing_care_fee", "other_service_fee", "pathological_diagnostic_fee",
    "laboratory_diagnostic_fee", "imaging_diagnostic_fee", "clinical_diagnostic_fee",
    "nonsurgical_treatment_fee", "clinical_physical_therapy_fee", "surgery_treatment_fee",
    "anesthesia_fee", "surgery_fee", "rehabilitation_fee", "chinese_medicine_treatment_fee",
    "western_medicine_fee", "antibacterial_drug_fee", "chinese_patent_medcine_fee", "chinese_herbal_medicine_fee",
    "blood_fee", "albumin_fee", "globulin_fee", "blood_coagulation_factor_fee", "cytokine_fee",
    "exam_disposable_mat_fee", "treat_disposable_mat_fee", "surgery_disposable_mat_fee",
    "other_fee", "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.MrHomepageFeeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_fee"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_fee"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."mr_homepage_fee"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.MrHomepageFeeExample">
    delete from "public"."mr_homepage_fee"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.MrHomepageFee">
    insert into "public"."mr_homepage_fee" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "total_costs", "self_pay",
      "general_medical_service_fee", "general_treatment_fee",
      "nursing_care_fee", "other_service_fee", "pathological_diagnostic_fee",
      "laboratory_diagnostic_fee", "imaging_diagnostic_fee",
      "clinical_diagnostic_fee", "nonsurgical_treatment_fee",
      "clinical_physical_therapy_fee", "surgery_treatment_fee",
      "anesthesia_fee", "surgery_fee", "rehabilitation_fee",
      "chinese_medicine_treatment_fee", "western_medicine_fee",
      "antibacterial_drug_fee", "chinese_patent_medcine_fee",
      "chinese_herbal_medicine_fee", "blood_fee", "albumin_fee",
      "globulin_fee", "blood_coagulation_factor_fee", "cytokine_fee",
      "exam_disposable_mat_fee", "treat_disposable_mat_fee",
      "surgery_disposable_mat_fee", "other_fee", "source_path",
      "pk_id", "data_state", "patient_sn_org"
      )
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{totalCosts,jdbcType=DOUBLE}, #{selfPay,jdbcType=DOUBLE},
      #{generalMedicalServiceFee,jdbcType=DOUBLE}, #{generalTreatmentFee,jdbcType=DOUBLE},
      #{nursingCareFee,jdbcType=DOUBLE}, #{otherServiceFee,jdbcType=DOUBLE}, #{pathologicalDiagnosticFee,jdbcType=DOUBLE},
      #{laboratoryDiagnosticFee,jdbcType=DOUBLE}, #{imagingDiagnosticFee,jdbcType=DOUBLE},
      #{clinicalDiagnosticFee,jdbcType=DOUBLE}, #{nonsurgicalTreatmentFee,jdbcType=DOUBLE},
      #{clinicalPhysicalTherapyFee,jdbcType=DOUBLE}, #{surgeryTreatmentFee,jdbcType=DOUBLE},
      #{anesthesiaFee,jdbcType=DOUBLE}, #{surgeryFee,jdbcType=DOUBLE}, #{rehabilitationFee,jdbcType=DOUBLE},
      #{chineseMedicineTreatmentFee,jdbcType=DOUBLE}, #{westernMedicineFee,jdbcType=DOUBLE},
      #{antibacterialDrugFee,jdbcType=DOUBLE}, #{chinesePatentMedcineFee,jdbcType=DOUBLE},
      #{chineseHerbalMedicineFee,jdbcType=DOUBLE}, #{bloodFee,jdbcType=DOUBLE}, #{albuminFee,jdbcType=DOUBLE},
      #{globulinFee,jdbcType=DOUBLE}, #{bloodCoagulationFactorFee,jdbcType=DOUBLE}, #{cytokineFee,jdbcType=DOUBLE},
      #{examDisposableMatFee,jdbcType=DOUBLE}, #{treatDisposableMatFee,jdbcType=DOUBLE},
      #{surgeryDisposableMatFee,jdbcType=DOUBLE}, #{otherFee,jdbcType=DOUBLE}, #{sourcePath,jdbcType=VARCHAR},
      #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.MrHomepageFee">
    insert into "public"."mr_homepage_fee"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="totalCosts != null">
        "total_costs",
      </if>
      <if test="selfPay != null">
        "self_pay",
      </if>
      <if test="generalMedicalServiceFee != null">
        "general_medical_service_fee",
      </if>
      <if test="generalTreatmentFee != null">
        "general_treatment_fee",
      </if>
      <if test="nursingCareFee != null">
        "nursing_care_fee",
      </if>
      <if test="otherServiceFee != null">
        "other_service_fee",
      </if>
      <if test="pathologicalDiagnosticFee != null">
        "pathological_diagnostic_fee",
      </if>
      <if test="laboratoryDiagnosticFee != null">
        "laboratory_diagnostic_fee",
      </if>
      <if test="imagingDiagnosticFee != null">
        "imaging_diagnostic_fee",
      </if>
      <if test="clinicalDiagnosticFee != null">
        "clinical_diagnostic_fee",
      </if>
      <if test="nonsurgicalTreatmentFee != null">
        "nonsurgical_treatment_fee",
      </if>
      <if test="clinicalPhysicalTherapyFee != null">
        "clinical_physical_therapy_fee",
      </if>
      <if test="surgeryTreatmentFee != null">
        "surgery_treatment_fee",
      </if>
      <if test="anesthesiaFee != null">
        "anesthesia_fee",
      </if>
      <if test="surgeryFee != null">
        "surgery_fee",
      </if>
      <if test="rehabilitationFee != null">
        "rehabilitation_fee",
      </if>
      <if test="chineseMedicineTreatmentFee != null">
        "chinese_medicine_treatment_fee",
      </if>
      <if test="westernMedicineFee != null">
        "western_medicine_fee",
      </if>
      <if test="antibacterialDrugFee != null">
        "antibacterial_drug_fee",
      </if>
      <if test="chinesePatentMedcineFee != null">
        "chinese_patent_medcine_fee",
      </if>
      <if test="chineseHerbalMedicineFee != null">
        "chinese_herbal_medicine_fee",
      </if>
      <if test="bloodFee != null">
        "blood_fee",
      </if>
      <if test="albuminFee != null">
        "albumin_fee",
      </if>
      <if test="globulinFee != null">
        "globulin_fee",
      </if>
      <if test="bloodCoagulationFactorFee != null">
        "blood_coagulation_factor_fee",
      </if>
      <if test="cytokineFee != null">
        "cytokine_fee",
      </if>
      <if test="examDisposableMatFee != null">
        "exam_disposable_mat_fee",
      </if>
      <if test="treatDisposableMatFee != null">
        "treat_disposable_mat_fee",
      </if>
      <if test="surgeryDisposableMatFee != null">
        "surgery_disposable_mat_fee",
      </if>
      <if test="otherFee != null">
        "other_fee",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="totalCosts != null">
        #{totalCosts,jdbcType=DOUBLE},
      </if>
      <if test="selfPay != null">
        #{selfPay,jdbcType=DOUBLE},
      </if>
      <if test="generalMedicalServiceFee != null">
        #{generalMedicalServiceFee,jdbcType=DOUBLE},
      </if>
      <if test="generalTreatmentFee != null">
        #{generalTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="nursingCareFee != null">
        #{nursingCareFee,jdbcType=DOUBLE},
      </if>
      <if test="otherServiceFee != null">
        #{otherServiceFee,jdbcType=DOUBLE},
      </if>
      <if test="pathologicalDiagnosticFee != null">
        #{pathologicalDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="laboratoryDiagnosticFee != null">
        #{laboratoryDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="imagingDiagnosticFee != null">
        #{imagingDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="clinicalDiagnosticFee != null">
        #{clinicalDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="nonsurgicalTreatmentFee != null">
        #{nonsurgicalTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="clinicalPhysicalTherapyFee != null">
        #{clinicalPhysicalTherapyFee,jdbcType=DOUBLE},
      </if>
      <if test="surgeryTreatmentFee != null">
        #{surgeryTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="anesthesiaFee != null">
        #{anesthesiaFee,jdbcType=DOUBLE},
      </if>
      <if test="surgeryFee != null">
        #{surgeryFee,jdbcType=DOUBLE},
      </if>
      <if test="rehabilitationFee != null">
        #{rehabilitationFee,jdbcType=DOUBLE},
      </if>
      <if test="chineseMedicineTreatmentFee != null">
        #{chineseMedicineTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="westernMedicineFee != null">
        #{westernMedicineFee,jdbcType=DOUBLE},
      </if>
      <if test="antibacterialDrugFee != null">
        #{antibacterialDrugFee,jdbcType=DOUBLE},
      </if>
      <if test="chinesePatentMedcineFee != null">
        #{chinesePatentMedcineFee,jdbcType=DOUBLE},
      </if>
      <if test="chineseHerbalMedicineFee != null">
        #{chineseHerbalMedicineFee,jdbcType=DOUBLE},
      </if>
      <if test="bloodFee != null">
        #{bloodFee,jdbcType=DOUBLE},
      </if>
      <if test="albuminFee != null">
        #{albuminFee,jdbcType=DOUBLE},
      </if>
      <if test="globulinFee != null">
        #{globulinFee,jdbcType=DOUBLE},
      </if>
      <if test="bloodCoagulationFactorFee != null">
        #{bloodCoagulationFactorFee,jdbcType=DOUBLE},
      </if>
      <if test="cytokineFee != null">
        #{cytokineFee,jdbcType=DOUBLE},
      </if>
      <if test="examDisposableMatFee != null">
        #{examDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="treatDisposableMatFee != null">
        #{treatDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="surgeryDisposableMatFee != null">
        #{surgeryDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="otherFee != null">
        #{otherFee,jdbcType=DOUBLE},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.MrHomepageFeeExample" resultType="java.lang.Long">
    select count(*) from "public"."mr_homepage_fee"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."mr_homepage_fee"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.totalCosts != null">
        "total_costs" = #{record.totalCosts,jdbcType=DOUBLE},
      </if>
      <if test="record.selfPay != null">
        "self_pay" = #{record.selfPay,jdbcType=DOUBLE},
      </if>
      <if test="record.generalMedicalServiceFee != null">
        "general_medical_service_fee" = #{record.generalMedicalServiceFee,jdbcType=DOUBLE},
      </if>
      <if test="record.generalTreatmentFee != null">
        "general_treatment_fee" = #{record.generalTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="record.nursingCareFee != null">
        "nursing_care_fee" = #{record.nursingCareFee,jdbcType=DOUBLE},
      </if>
      <if test="record.otherServiceFee != null">
        "other_service_fee" = #{record.otherServiceFee,jdbcType=DOUBLE},
      </if>
      <if test="record.pathologicalDiagnosticFee != null">
        "pathological_diagnostic_fee" = #{record.pathologicalDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="record.laboratoryDiagnosticFee != null">
        "laboratory_diagnostic_fee" = #{record.laboratoryDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="record.imagingDiagnosticFee != null">
        "imaging_diagnostic_fee" = #{record.imagingDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="record.clinicalDiagnosticFee != null">
        "clinical_diagnostic_fee" = #{record.clinicalDiagnosticFee,jdbcType=DOUBLE},
      </if>
      <if test="record.nonsurgicalTreatmentFee != null">
        "nonsurgical_treatment_fee" = #{record.nonsurgicalTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="record.clinicalPhysicalTherapyFee != null">
        "clinical_physical_therapy_fee" = #{record.clinicalPhysicalTherapyFee,jdbcType=DOUBLE},
      </if>
      <if test="record.surgeryTreatmentFee != null">
        "surgery_treatment_fee" = #{record.surgeryTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="record.anesthesiaFee != null">
        "anesthesia_fee" = #{record.anesthesiaFee,jdbcType=DOUBLE},
      </if>
      <if test="record.surgeryFee != null">
        "surgery_fee" = #{record.surgeryFee,jdbcType=DOUBLE},
      </if>
      <if test="record.rehabilitationFee != null">
        "rehabilitation_fee" = #{record.rehabilitationFee,jdbcType=DOUBLE},
      </if>
      <if test="record.chineseMedicineTreatmentFee != null">
        "chinese_medicine_treatment_fee" = #{record.chineseMedicineTreatmentFee,jdbcType=DOUBLE},
      </if>
      <if test="record.westernMedicineFee != null">
        "western_medicine_fee" = #{record.westernMedicineFee,jdbcType=DOUBLE},
      </if>
      <if test="record.antibacterialDrugFee != null">
        "antibacterial_drug_fee" = #{record.antibacterialDrugFee,jdbcType=DOUBLE},
      </if>
      <if test="record.chinesePatentMedcineFee != null">
        "chinese_patent_medcine_fee" = #{record.chinesePatentMedcineFee,jdbcType=DOUBLE},
      </if>
      <if test="record.chineseHerbalMedicineFee != null">
        "chinese_herbal_medicine_fee" = #{record.chineseHerbalMedicineFee,jdbcType=DOUBLE},
      </if>
      <if test="record.bloodFee != null">
        "blood_fee" = #{record.bloodFee,jdbcType=DOUBLE},
      </if>
      <if test="record.albuminFee != null">
        "albumin_fee" = #{record.albuminFee,jdbcType=DOUBLE},
      </if>
      <if test="record.globulinFee != null">
        "globulin_fee" = #{record.globulinFee,jdbcType=DOUBLE},
      </if>
      <if test="record.bloodCoagulationFactorFee != null">
        "blood_coagulation_factor_fee" = #{record.bloodCoagulationFactorFee,jdbcType=DOUBLE},
      </if>
      <if test="record.cytokineFee != null">
        "cytokine_fee" = #{record.cytokineFee,jdbcType=DOUBLE},
      </if>
      <if test="record.examDisposableMatFee != null">
        "exam_disposable_mat_fee" = #{record.examDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="record.treatDisposableMatFee != null">
        "treat_disposable_mat_fee" = #{record.treatDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="record.surgeryDisposableMatFee != null">
        "surgery_disposable_mat_fee" = #{record.surgeryDisposableMatFee,jdbcType=DOUBLE},
      </if>
      <if test="record.otherFee != null">
        "other_fee" = #{record.otherFee,jdbcType=DOUBLE},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."mr_homepage_fee"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "total_costs" = #{record.totalCosts,jdbcType=DOUBLE},
      "self_pay" = #{record.selfPay,jdbcType=DOUBLE},
      "general_medical_service_fee" = #{record.generalMedicalServiceFee,jdbcType=DOUBLE},
      "general_treatment_fee" = #{record.generalTreatmentFee,jdbcType=DOUBLE},
      "nursing_care_fee" = #{record.nursingCareFee,jdbcType=DOUBLE},
      "other_service_fee" = #{record.otherServiceFee,jdbcType=DOUBLE},
      "pathological_diagnostic_fee" = #{record.pathologicalDiagnosticFee,jdbcType=DOUBLE},
      "laboratory_diagnostic_fee" = #{record.laboratoryDiagnosticFee,jdbcType=DOUBLE},
      "imaging_diagnostic_fee" = #{record.imagingDiagnosticFee,jdbcType=DOUBLE},
      "clinical_diagnostic_fee" = #{record.clinicalDiagnosticFee,jdbcType=DOUBLE},
      "nonsurgical_treatment_fee" = #{record.nonsurgicalTreatmentFee,jdbcType=DOUBLE},
      "clinical_physical_therapy_fee" = #{record.clinicalPhysicalTherapyFee,jdbcType=DOUBLE},
      "surgery_treatment_fee" = #{record.surgeryTreatmentFee,jdbcType=DOUBLE},
      "anesthesia_fee" = #{record.anesthesiaFee,jdbcType=DOUBLE},
      "surgery_fee" = #{record.surgeryFee,jdbcType=DOUBLE},
      "rehabilitation_fee" = #{record.rehabilitationFee,jdbcType=DOUBLE},
      "chinese_medicine_treatment_fee" = #{record.chineseMedicineTreatmentFee,jdbcType=DOUBLE},
      "western_medicine_fee" = #{record.westernMedicineFee,jdbcType=DOUBLE},
      "antibacterial_drug_fee" = #{record.antibacterialDrugFee,jdbcType=DOUBLE},
      "chinese_patent_medcine_fee" = #{record.chinesePatentMedcineFee,jdbcType=DOUBLE},
      "chinese_herbal_medicine_fee" = #{record.chineseHerbalMedicineFee,jdbcType=DOUBLE},
      "blood_fee" = #{record.bloodFee,jdbcType=DOUBLE},
      "albumin_fee" = #{record.albuminFee,jdbcType=DOUBLE},
      "globulin_fee" = #{record.globulinFee,jdbcType=DOUBLE},
      "blood_coagulation_factor_fee" = #{record.bloodCoagulationFactorFee,jdbcType=DOUBLE},
      "cytokine_fee" = #{record.cytokineFee,jdbcType=DOUBLE},
      "exam_disposable_mat_fee" = #{record.examDisposableMatFee,jdbcType=DOUBLE},
      "treat_disposable_mat_fee" = #{record.treatDisposableMatFee,jdbcType=DOUBLE},
      "surgery_disposable_mat_fee" = #{record.surgeryDisposableMatFee,jdbcType=DOUBLE},
      "other_fee" = #{record.otherFee,jdbcType=DOUBLE},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="getMrHomepageFee" resultMap="BaseResultMap">
    select * from mr_homepage_fee where patient_sn = #{patientId}
  </select>
</mapper>
