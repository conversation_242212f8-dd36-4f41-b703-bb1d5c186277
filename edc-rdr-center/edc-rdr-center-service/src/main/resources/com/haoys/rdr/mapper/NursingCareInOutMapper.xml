<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.NursingCareInOutMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.NursingCareInOut">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="in_out_type_code" jdbcType="VARCHAR" property="inOutTypeCode" />
    <result column="in_out_type_name" jdbcType="VARCHAR" property="inOutTypeName" />
    <result column="in_out_item" jdbcType="VARCHAR" property="inOutItem" />
    <result column="in_out_value" jdbcType="DOUBLE" property="inOutValue" />
    <result column="in_out_unit" jdbcType="VARCHAR" property="inOutUnit" />
    <result column="in_rate" jdbcType="VARCHAR" property="inRate" />
    <result column="in_rateuom" jdbcType="VARCHAR" property="inRateuom" />
    <result column="out_liquid_color" jdbcType="VARCHAR" property="outLiquidColor" />
    <result column="out_liquid_type" jdbcType="VARCHAR" property="outLiquidType" />
    <result column="in_out_names" jdbcType="VARCHAR" property="inOutNames" />
    <result column="recorder" jdbcType="VARCHAR" property="recorder" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "in_out_type_code", 
    "in_out_type_name", "in_out_item", "in_out_value", "in_out_unit", "in_rate", "in_rateuom", 
    "out_liquid_color", "out_liquid_type", "in_out_names", "recorder", "record_time", 
    "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.NursingCareInOutExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."nursing_care_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."nursing_care_in_out"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."nursing_care_in_out"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.NursingCareInOutExample">
    delete from "public"."nursing_care_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.NursingCareInOut">
    insert into "public"."nursing_care_in_out" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "in_out_type_code", 
      "in_out_type_name", "in_out_item", "in_out_value", 
      "in_out_unit", "in_rate", "in_rateuom", 
      "out_liquid_color", "out_liquid_type", "in_out_names", 
      "recorder", "record_time", "source_path", 
      "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{inOutTypeCode,jdbcType=VARCHAR}, 
      #{inOutTypeName,jdbcType=VARCHAR}, #{inOutItem,jdbcType=VARCHAR}, #{inOutValue,jdbcType=DOUBLE}, 
      #{inOutUnit,jdbcType=VARCHAR}, #{inRate,jdbcType=VARCHAR}, #{inRateuom,jdbcType=VARCHAR}, 
      #{outLiquidColor,jdbcType=VARCHAR}, #{outLiquidType,jdbcType=VARCHAR}, #{inOutNames,jdbcType=VARCHAR}, 
      #{recorder,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP}, #{sourcePath,jdbcType=VARCHAR}, 
      #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.NursingCareInOut">
    insert into "public"."nursing_care_in_out"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="inOutTypeCode != null">
        "in_out_type_code",
      </if>
      <if test="inOutTypeName != null">
        "in_out_type_name",
      </if>
      <if test="inOutItem != null">
        "in_out_item",
      </if>
      <if test="inOutValue != null">
        "in_out_value",
      </if>
      <if test="inOutUnit != null">
        "in_out_unit",
      </if>
      <if test="inRate != null">
        "in_rate",
      </if>
      <if test="inRateuom != null">
        "in_rateuom",
      </if>
      <if test="outLiquidColor != null">
        "out_liquid_color",
      </if>
      <if test="outLiquidType != null">
        "out_liquid_type",
      </if>
      <if test="inOutNames != null">
        "in_out_names",
      </if>
      <if test="recorder != null">
        "recorder",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="inOutTypeCode != null">
        #{inOutTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="inOutTypeName != null">
        #{inOutTypeName,jdbcType=VARCHAR},
      </if>
      <if test="inOutItem != null">
        #{inOutItem,jdbcType=VARCHAR},
      </if>
      <if test="inOutValue != null">
        #{inOutValue,jdbcType=DOUBLE},
      </if>
      <if test="inOutUnit != null">
        #{inOutUnit,jdbcType=VARCHAR},
      </if>
      <if test="inRate != null">
        #{inRate,jdbcType=VARCHAR},
      </if>
      <if test="inRateuom != null">
        #{inRateuom,jdbcType=VARCHAR},
      </if>
      <if test="outLiquidColor != null">
        #{outLiquidColor,jdbcType=VARCHAR},
      </if>
      <if test="outLiquidType != null">
        #{outLiquidType,jdbcType=VARCHAR},
      </if>
      <if test="inOutNames != null">
        #{inOutNames,jdbcType=VARCHAR},
      </if>
      <if test="recorder != null">
        #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.NursingCareInOutExample" resultType="java.lang.Long">
    select count(*) from "public"."nursing_care_in_out"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."nursing_care_in_out"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutTypeCode != null">
        "in_out_type_code" = #{record.inOutTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutTypeName != null">
        "in_out_type_name" = #{record.inOutTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutItem != null">
        "in_out_item" = #{record.inOutItem,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutValue != null">
        "in_out_value" = #{record.inOutValue,jdbcType=DOUBLE},
      </if>
      <if test="record.inOutUnit != null">
        "in_out_unit" = #{record.inOutUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.inRate != null">
        "in_rate" = #{record.inRate,jdbcType=VARCHAR},
      </if>
      <if test="record.inRateuom != null">
        "in_rateuom" = #{record.inRateuom,jdbcType=VARCHAR},
      </if>
      <if test="record.outLiquidColor != null">
        "out_liquid_color" = #{record.outLiquidColor,jdbcType=VARCHAR},
      </if>
      <if test="record.outLiquidType != null">
        "out_liquid_type" = #{record.outLiquidType,jdbcType=VARCHAR},
      </if>
      <if test="record.inOutNames != null">
        "in_out_names" = #{record.inOutNames,jdbcType=VARCHAR},
      </if>
      <if test="record.recorder != null">
        "recorder" = #{record.recorder,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."nursing_care_in_out"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "in_out_type_code" = #{record.inOutTypeCode,jdbcType=VARCHAR},
      "in_out_type_name" = #{record.inOutTypeName,jdbcType=VARCHAR},
      "in_out_item" = #{record.inOutItem,jdbcType=VARCHAR},
      "in_out_value" = #{record.inOutValue,jdbcType=DOUBLE},
      "in_out_unit" = #{record.inOutUnit,jdbcType=VARCHAR},
      "in_rate" = #{record.inRate,jdbcType=VARCHAR},
      "in_rateuom" = #{record.inRateuom,jdbcType=VARCHAR},
      "out_liquid_color" = #{record.outLiquidColor,jdbcType=VARCHAR},
      "out_liquid_type" = #{record.outLiquidType,jdbcType=VARCHAR},
      "in_out_names" = #{record.inOutNames,jdbcType=VARCHAR},
      "recorder" = #{record.recorder,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.NursingCareInOut">
    update "public"."nursing_care_in_out"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="inOutTypeCode != null">
        "in_out_type_code" = #{inOutTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="inOutTypeName != null">
        "in_out_type_name" = #{inOutTypeName,jdbcType=VARCHAR},
      </if>
      <if test="inOutItem != null">
        "in_out_item" = #{inOutItem,jdbcType=VARCHAR},
      </if>
      <if test="inOutValue != null">
        "in_out_value" = #{inOutValue,jdbcType=DOUBLE},
      </if>
      <if test="inOutUnit != null">
        "in_out_unit" = #{inOutUnit,jdbcType=VARCHAR},
      </if>
      <if test="inRate != null">
        "in_rate" = #{inRate,jdbcType=VARCHAR},
      </if>
      <if test="inRateuom != null">
        "in_rateuom" = #{inRateuom,jdbcType=VARCHAR},
      </if>
      <if test="outLiquidColor != null">
        "out_liquid_color" = #{outLiquidColor,jdbcType=VARCHAR},
      </if>
      <if test="outLiquidType != null">
        "out_liquid_type" = #{outLiquidType,jdbcType=VARCHAR},
      </if>
      <if test="inOutNames != null">
        "in_out_names" = #{inOutNames,jdbcType=VARCHAR},
      </if>
      <if test="recorder != null">
        "recorder" = #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.NursingCareInOut">
    update "public"."nursing_care_in_out"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "in_out_type_code" = #{inOutTypeCode,jdbcType=VARCHAR},
      "in_out_type_name" = #{inOutTypeName,jdbcType=VARCHAR},
      "in_out_item" = #{inOutItem,jdbcType=VARCHAR},
      "in_out_value" = #{inOutValue,jdbcType=DOUBLE},
      "in_out_unit" = #{inOutUnit,jdbcType=VARCHAR},
      "in_rate" = #{inRate,jdbcType=VARCHAR},
      "in_rateuom" = #{inRateuom,jdbcType=VARCHAR},
      "out_liquid_color" = #{outLiquidColor,jdbcType=VARCHAR},
      "out_liquid_type" = #{outLiquidType,jdbcType=VARCHAR},
      "in_out_names" = #{inOutNames,jdbcType=VARCHAR},
      "recorder" = #{recorder,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>