<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.NursingVitalSignsMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.NursingVitalSigns">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_value" jdbcType="VARCHAR" property="itemValue" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="item_value_num" jdbcType="DOUBLE" property="itemValueNum" />
    <result column="item_value_text" jdbcType="VARCHAR" property="itemValueText" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="recorder" jdbcType="VARCHAR" property="recorder" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="reference_range" jdbcType="VARCHAR" property="referenceRange" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "item_id", "item_name", "item_code",
    "item_value", "unit_name", "item_value_num", "item_value_text", "record_time", "recorder",
    "comment", "reference_range", "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.NursingVitalSignsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."nursing_vital_signs"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."nursing_vital_signs"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."nursing_vital_signs"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.NursingVitalSignsExample">
    delete from "public"."nursing_vital_signs"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.NursingVitalSigns">
    insert into "public"."nursing_vital_signs" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "item_id", "item_name",
      "item_code", "item_value", "unit_name",
      "item_value_num", "item_value_text", "record_time",
      "recorder", "comment", "reference_range",
      "source_path", "pk_id", "data_state",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{itemId,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR},
      #{itemCode,jdbcType=VARCHAR}, #{itemValue,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR},
      #{itemValueNum,jdbcType=DOUBLE}, #{itemValueText,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP},
      #{recorder,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{referenceRange,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.NursingVitalSigns">
    insert into "public"."nursing_vital_signs"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="itemId != null">
        "item_id",
      </if>
      <if test="itemName != null">
        "item_name",
      </if>
      <if test="itemCode != null">
        "item_code",
      </if>
      <if test="itemValue != null">
        "item_value",
      </if>
      <if test="unitName != null">
        "unit_name",
      </if>
      <if test="itemValueNum != null">
        "item_value_num",
      </if>
      <if test="itemValueText != null">
        "item_value_text",
      </if>
      <if test="recordTime != null">
        "record_time",
      </if>
      <if test="recorder != null">
        "recorder",
      </if>
      <if test="comment != null">
        "comment",
      </if>
      <if test="referenceRange != null">
        "reference_range",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemValue != null">
        #{itemValue,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="itemValueNum != null">
        #{itemValueNum,jdbcType=DOUBLE},
      </if>
      <if test="itemValueText != null">
        #{itemValueText,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recorder != null">
        #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="referenceRange != null">
        #{referenceRange,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.NursingVitalSignsExample" resultType="java.lang.Long">
    select count(*) from "public"."nursing_vital_signs"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."nursing_vital_signs"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.itemId != null">
        "item_id" = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        "item_name" = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemValue != null">
        "item_value" = #{record.itemValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitName != null">
        "unit_name" = #{record.unitName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemValueNum != null">
        "item_value_num" = #{record.itemValueNum,jdbcType=DOUBLE},
      </if>
      <if test="record.itemValueText != null">
        "item_value_text" = #{record.itemValueText,jdbcType=VARCHAR},
      </if>
      <if test="record.recordTime != null">
        "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recorder != null">
        "recorder" = #{record.recorder,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        "comment" = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.referenceRange != null">
        "reference_range" = #{record.referenceRange,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."nursing_vital_signs"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "item_id" = #{record.itemId,jdbcType=VARCHAR},
      "item_name" = #{record.itemName,jdbcType=VARCHAR},
      "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      "item_value" = #{record.itemValue,jdbcType=VARCHAR},
      "unit_name" = #{record.unitName,jdbcType=VARCHAR},
      "item_value_num" = #{record.itemValueNum,jdbcType=DOUBLE},
      "item_value_text" = #{record.itemValueText,jdbcType=VARCHAR},
      "record_time" = #{record.recordTime,jdbcType=TIMESTAMP},
      "recorder" = #{record.recorder,jdbcType=VARCHAR},
      "comment" = #{record.comment,jdbcType=VARCHAR},
      "reference_range" = #{record.referenceRange,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.NursingVitalSigns">
    update "public"."nursing_vital_signs"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        "item_id" = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        "item_name" = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        "item_code" = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemValue != null">
        "item_value" = #{itemValue,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        "unit_name" = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="itemValueNum != null">
        "item_value_num" = #{itemValueNum,jdbcType=DOUBLE},
      </if>
      <if test="itemValueText != null">
        "item_value_text" = #{itemValueText,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recorder != null">
        "recorder" = #{recorder,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        "comment" = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="referenceRange != null">
        "reference_range" = #{referenceRange,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.NursingVitalSigns">
    update "public"."nursing_vital_signs"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "item_id" = #{itemId,jdbcType=VARCHAR},
      "item_name" = #{itemName,jdbcType=VARCHAR},
      "item_code" = #{itemCode,jdbcType=VARCHAR},
      "item_value" = #{itemValue,jdbcType=VARCHAR},
      "unit_name" = #{unitName,jdbcType=VARCHAR},
      "item_value_num" = #{itemValueNum,jdbcType=DOUBLE},
      "item_value_text" = #{itemValueText,jdbcType=VARCHAR},
      "record_time" = #{recordTime,jdbcType=TIMESTAMP},
      "recorder" = #{recorder,jdbcType=VARCHAR},
      "comment" = #{comment,jdbcType=VARCHAR},
      "reference_range" = #{referenceRange,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
