<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpChargeDetailMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpChargeDetail">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="rcpt_id" jdbcType="VARCHAR" property="rcptId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="item_class" jdbcType="VARCHAR" property="itemClass" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_spec" jdbcType="VARCHAR" property="itemSpec" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_price" jdbcType="DOUBLE" property="unitPrice" />
    <result column="order_dept" jdbcType="VARCHAR" property="orderDept" />
    <result column="perform_dept" jdbcType="VARCHAR" property="performDept" />
    <result column="charges" jdbcType="DOUBLE" property="charges" />
    <result column="costs" jdbcType="DOUBLE" property="costs" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "visit_date", "rcpt_id", "item_no", 
    "order_no", "item_class", "item_name", "item_code", "item_spec", "amount", "unit", 
    "unit_price", "order_dept", "perform_dept", "charges", "costs", "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpChargeDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_charge_detail"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."outp_charge_detail"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_charge_detail"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpChargeDetailExample">
    delete from "public"."outp_charge_detail"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpChargeDetail">
    insert into "public"."outp_charge_detail" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "visit_date", "rcpt_id", 
      "item_no", "order_no", "item_class", 
      "item_name", "item_code", "item_spec", 
      "amount", "unit", "unit_price", 
      "order_dept", "perform_dept", "charges", 
      "costs", "source_path", "data_state"
      )
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{visitDate,jdbcType=TIMESTAMP}, #{rcptId,jdbcType=VARCHAR}, 
      #{itemNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{itemClass,jdbcType=VARCHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{itemSpec,jdbcType=VARCHAR}, 
      #{amount,jdbcType=DOUBLE}, #{unit,jdbcType=VARCHAR}, #{unitPrice,jdbcType=DOUBLE}, 
      #{orderDept,jdbcType=VARCHAR}, #{performDept,jdbcType=VARCHAR}, #{charges,jdbcType=DOUBLE}, 
      #{costs,jdbcType=DOUBLE}, #{sourcePath,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpChargeDetail">
    insert into "public"."outp_charge_detail"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="rcptId != null">
        "rcpt_id",
      </if>
      <if test="itemNo != null">
        "item_no",
      </if>
      <if test="orderNo != null">
        "order_no",
      </if>
      <if test="itemClass != null">
        "item_class",
      </if>
      <if test="itemName != null">
        "item_name",
      </if>
      <if test="itemCode != null">
        "item_code",
      </if>
      <if test="itemSpec != null">
        "item_spec",
      </if>
      <if test="amount != null">
        "amount",
      </if>
      <if test="unit != null">
        "unit",
      </if>
      <if test="unitPrice != null">
        "unit_price",
      </if>
      <if test="orderDept != null">
        "order_dept",
      </if>
      <if test="performDept != null">
        "perform_dept",
      </if>
      <if test="charges != null">
        "charges",
      </if>
      <if test="costs != null">
        "costs",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="rcptId != null">
        #{rcptId,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="itemClass != null">
        #{itemClass,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemSpec != null">
        #{itemSpec,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderDept != null">
        #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="performDept != null">
        #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="charges != null">
        #{charges,jdbcType=DOUBLE},
      </if>
      <if test="costs != null">
        #{costs,jdbcType=DOUBLE},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpChargeDetailExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_charge_detail"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_charge_detail"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rcptId != null">
        "rcpt_id" = #{record.rcptId,jdbcType=VARCHAR},
      </if>
      <if test="record.itemNo != null">
        "item_no" = #{record.itemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemClass != null">
        "item_class" = #{record.itemClass,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        "item_name" = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemSpec != null">
        "item_spec" = #{record.itemSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        "amount" = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.unit != null">
        "unit" = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.unitPrice != null">
        "unit_price" = #{record.unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.orderDept != null">
        "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.performDept != null">
        "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      </if>
      <if test="record.charges != null">
        "charges" = #{record.charges,jdbcType=DOUBLE},
      </if>
      <if test="record.costs != null">
        "costs" = #{record.costs,jdbcType=DOUBLE},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_charge_detail"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "rcpt_id" = #{record.rcptId,jdbcType=VARCHAR},
      "item_no" = #{record.itemNo,jdbcType=VARCHAR},
      "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      "item_class" = #{record.itemClass,jdbcType=VARCHAR},
      "item_name" = #{record.itemName,jdbcType=VARCHAR},
      "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      "item_spec" = #{record.itemSpec,jdbcType=VARCHAR},
      "amount" = #{record.amount,jdbcType=DOUBLE},
      "unit" = #{record.unit,jdbcType=VARCHAR},
      "unit_price" = #{record.unitPrice,jdbcType=DOUBLE},
      "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      "charges" = #{record.charges,jdbcType=DOUBLE},
      "costs" = #{record.costs,jdbcType=DOUBLE},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpChargeDetail">
    update "public"."outp_charge_detail"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="rcptId != null">
        "rcpt_id" = #{rcptId,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        "item_no" = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        "order_no" = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="itemClass != null">
        "item_class" = #{itemClass,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        "item_name" = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        "item_code" = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemSpec != null">
        "item_spec" = #{itemSpec,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        "amount" = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="unit != null">
        "unit" = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        "unit_price" = #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderDept != null">
        "order_dept" = #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="performDept != null">
        "perform_dept" = #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="charges != null">
        "charges" = #{charges,jdbcType=DOUBLE},
      </if>
      <if test="costs != null">
        "costs" = #{costs,jdbcType=DOUBLE},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpChargeDetail">
    update "public"."outp_charge_detail"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "rcpt_id" = #{rcptId,jdbcType=VARCHAR},
      "item_no" = #{itemNo,jdbcType=VARCHAR},
      "order_no" = #{orderNo,jdbcType=VARCHAR},
      "item_class" = #{itemClass,jdbcType=VARCHAR},
      "item_name" = #{itemName,jdbcType=VARCHAR},
      "item_code" = #{itemCode,jdbcType=VARCHAR},
      "item_spec" = #{itemSpec,jdbcType=VARCHAR},
      "amount" = #{amount,jdbcType=DOUBLE},
      "unit" = #{unit,jdbcType=VARCHAR},
      "unit_price" = #{unitPrice,jdbcType=DOUBLE},
      "order_dept" = #{orderDept,jdbcType=VARCHAR},
      "perform_dept" = #{performDept,jdbcType=VARCHAR},
      "charges" = #{charges,jdbcType=DOUBLE},
      "costs" = #{costs,jdbcType=DOUBLE},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>