<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpOrderMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpOrder">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_sn_org" jdbcType="BIGINT" property="visitSnOrg" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_group_no" jdbcType="VARCHAR" property="orderGroupNo" />
    <result column="order_sub_no" jdbcType="VARCHAR" property="orderSubNo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="order_dept" jdbcType="VARCHAR" property="orderDept" />
    <result column="order_doctor" jdbcType="VARCHAR" property="orderDoctor" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="generic_name" jdbcType="VARCHAR" property="genericName" />
    <result column="item_class" jdbcType="VARCHAR" property="itemClass" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="drug_spec" jdbcType="VARCHAR" property="drugSpec" />
    <result column="dose_form" jdbcType="VARCHAR" property="doseForm" />
    <result column="dosage" jdbcType="DOUBLE" property="dosage" />
    <result column="dosage_units" jdbcType="VARCHAR" property="dosageUnits" />
    <result column="least_number" jdbcType="DOUBLE" property="leastNumber" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="unit_price" jdbcType="DOUBLE" property="unitPrice" />
    <result column="administration_route" jdbcType="VARCHAR" property="administrationRoute" />
    <result column="days" jdbcType="INTEGER" property="days" />
    <result column="total_price" jdbcType="DOUBLE" property="totalPrice" />
    <result column="perform_dept" jdbcType="VARCHAR" property="performDept" />
    <result column="order_memo" jdbcType="VARCHAR" property="orderMemo" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_sn_org", "visit_date", "order_no",
    "order_group_no", "order_sub_no", "order_type", "order_dept", "order_doctor", "order_time",
    "order_status", "item_name", "brand_name", "generic_name", "item_class", "item_code",
    "drug_spec", "dose_form", "dosage", "dosage_units", "least_number", "frequency",
    "unit_price", "administration_route", "days", "total_price", "perform_dept", "order_memo",
    "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."outp_order"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_order"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpOrderExample">
    delete from "public"."outp_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpOrder">
    insert into "public"."outp_order" ("hospital_code", "patient_sn", "visit_sn",
      "visit_sn_org", "visit_date", "order_no",
      "order_group_no", "order_sub_no", "order_type",
      "order_dept", "order_doctor", "order_time",
      "order_status", "item_name", "brand_name",
      "generic_name", "item_class", "item_code",
      "drug_spec", "dose_form", "dosage",
      "dosage_units", "least_number", "frequency",
      "unit_price", "administration_route", "days",
      "total_price", "perform_dept", "order_memo",
      "source_path", "pk_id", "data_state",
      "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitSnOrg,jdbcType=BIGINT}, #{visitDate,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR},
      #{orderGroupNo,jdbcType=VARCHAR}, #{orderSubNo,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR},
      #{orderDept,jdbcType=VARCHAR}, #{orderDoctor,jdbcType=VARCHAR}, #{orderTime,jdbcType=TIMESTAMP},
      #{orderStatus,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR},
      #{genericName,jdbcType=VARCHAR}, #{itemClass,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR},
      #{drugSpec,jdbcType=VARCHAR}, #{doseForm,jdbcType=VARCHAR}, #{dosage,jdbcType=DOUBLE},
      #{dosageUnits,jdbcType=VARCHAR}, #{leastNumber,jdbcType=DOUBLE}, #{frequency,jdbcType=VARCHAR},
      #{unitPrice,jdbcType=DOUBLE}, #{administrationRoute,jdbcType=VARCHAR}, #{days,jdbcType=INTEGER},
      #{totalPrice,jdbcType=DOUBLE}, #{performDept,jdbcType=VARCHAR}, #{orderMemo,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpOrder">
    insert into "public"."outp_order"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitSnOrg != null">
        "visit_sn_org",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="orderNo != null">
        "order_no",
      </if>
      <if test="orderGroupNo != null">
        "order_group_no",
      </if>
      <if test="orderSubNo != null">
        "order_sub_no",
      </if>
      <if test="orderType != null">
        "order_type",
      </if>
      <if test="orderDept != null">
        "order_dept",
      </if>
      <if test="orderDoctor != null">
        "order_doctor",
      </if>
      <if test="orderTime != null">
        "order_time",
      </if>
      <if test="orderStatus != null">
        "order_status",
      </if>
      <if test="itemName != null">
        "item_name",
      </if>
      <if test="brandName != null">
        "brand_name",
      </if>
      <if test="genericName != null">
        "generic_name",
      </if>
      <if test="itemClass != null">
        "item_class",
      </if>
      <if test="itemCode != null">
        "item_code",
      </if>
      <if test="drugSpec != null">
        "drug_spec",
      </if>
      <if test="doseForm != null">
        "dose_form",
      </if>
      <if test="dosage != null">
        "dosage",
      </if>
      <if test="dosageUnits != null">
        "dosage_units",
      </if>
      <if test="leastNumber != null">
        "least_number",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="unitPrice != null">
        "unit_price",
      </if>
      <if test="administrationRoute != null">
        "administration_route",
      </if>
      <if test="days != null">
        "days",
      </if>
      <if test="totalPrice != null">
        "total_price",
      </if>
      <if test="performDept != null">
        "perform_dept",
      </if>
      <if test="orderMemo != null">
        "order_memo",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSnOrg != null">
        #{visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderGroupNo != null">
        #{orderGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSubNo != null">
        #{orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderDept != null">
        #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDoctor != null">
        #{orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="itemClass != null">
        #{itemClass,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="doseForm != null">
        #{doseForm,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="dosageUnits != null">
        #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="leastNumber != null">
        #{leastNumber,jdbcType=DOUBLE},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="administrationRoute != null">
        #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="days != null">
        #{days,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="performDept != null">
        #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="orderMemo != null">
        #{orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpOrderExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_order"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSnOrg != null">
        "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderNo != null">
        "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderGroupNo != null">
        "order_group_no" = #{record.orderGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSubNo != null">
        "order_sub_no" = #{record.orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        "order_type" = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDept != null">
        "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDoctor != null">
        "order_doctor" = #{record.orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTime != null">
        "order_time" = #{record.orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStatus != null">
        "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        "item_name" = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null">
        "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.genericName != null">
        "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemClass != null">
        "item_class" = #{record.itemClass,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.drugSpec != null">
        "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.doseForm != null">
        "dose_form" = #{record.doseForm,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        "dosage" = #{record.dosage,jdbcType=DOUBLE},
      </if>
      <if test="record.dosageUnits != null">
        "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="record.leastNumber != null">
        "least_number" = #{record.leastNumber,jdbcType=DOUBLE},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.unitPrice != null">
        "unit_price" = #{record.unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.administrationRoute != null">
        "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="record.days != null">
        "days" = #{record.days,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        "total_price" = #{record.totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.performDept != null">
        "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderMemo != null">
        "order_memo" = #{record.orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_order"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "order_no" = #{record.orderNo,jdbcType=VARCHAR},
      "order_group_no" = #{record.orderGroupNo,jdbcType=VARCHAR},
      "order_sub_no" = #{record.orderSubNo,jdbcType=VARCHAR},
      "order_type" = #{record.orderType,jdbcType=VARCHAR},
      "order_dept" = #{record.orderDept,jdbcType=VARCHAR},
      "order_doctor" = #{record.orderDoctor,jdbcType=VARCHAR},
      "order_time" = #{record.orderTime,jdbcType=TIMESTAMP},
      "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      "item_name" = #{record.itemName,jdbcType=VARCHAR},
      "brand_name" = #{record.brandName,jdbcType=VARCHAR},
      "generic_name" = #{record.genericName,jdbcType=VARCHAR},
      "item_class" = #{record.itemClass,jdbcType=VARCHAR},
      "item_code" = #{record.itemCode,jdbcType=VARCHAR},
      "drug_spec" = #{record.drugSpec,jdbcType=VARCHAR},
      "dose_form" = #{record.doseForm,jdbcType=VARCHAR},
      "dosage" = #{record.dosage,jdbcType=DOUBLE},
      "dosage_units" = #{record.dosageUnits,jdbcType=VARCHAR},
      "least_number" = #{record.leastNumber,jdbcType=DOUBLE},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "unit_price" = #{record.unitPrice,jdbcType=DOUBLE},
      "administration_route" = #{record.administrationRoute,jdbcType=VARCHAR},
      "days" = #{record.days,jdbcType=INTEGER},
      "total_price" = #{record.totalPrice,jdbcType=DOUBLE},
      "perform_dept" = #{record.performDept,jdbcType=VARCHAR},
      "order_memo" = #{record.orderMemo,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpOrder">
    update "public"."outp_order"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        "order_no" = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderGroupNo != null">
        "order_group_no" = #{orderGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="orderSubNo != null">
        "order_sub_no" = #{orderSubNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        "order_type" = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderDept != null">
        "order_dept" = #{orderDept,jdbcType=VARCHAR},
      </if>
      <if test="orderDoctor != null">
        "order_doctor" = #{orderDoctor,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        "order_time" = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        "order_status" = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        "item_name" = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        "brand_name" = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        "generic_name" = #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="itemClass != null">
        "item_class" = #{itemClass,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        "item_code" = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="drugSpec != null">
        "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      </if>
      <if test="doseForm != null">
        "dose_form" = #{doseForm,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        "dosage" = #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="dosageUnits != null">
        "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      </if>
      <if test="leastNumber != null">
        "least_number" = #{leastNumber,jdbcType=DOUBLE},
      </if>
      <if test="frequency != null">
        "frequency" = #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        "unit_price" = #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="administrationRoute != null">
        "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      </if>
      <if test="days != null">
        "days" = #{days,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        "total_price" = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="performDept != null">
        "perform_dept" = #{performDept,jdbcType=VARCHAR},
      </if>
      <if test="orderMemo != null">
        "order_memo" = #{orderMemo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpOrder">
    update "public"."outp_order"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "order_no" = #{orderNo,jdbcType=VARCHAR},
      "order_group_no" = #{orderGroupNo,jdbcType=VARCHAR},
      "order_sub_no" = #{orderSubNo,jdbcType=VARCHAR},
      "order_type" = #{orderType,jdbcType=VARCHAR},
      "order_dept" = #{orderDept,jdbcType=VARCHAR},
      "order_doctor" = #{orderDoctor,jdbcType=VARCHAR},
      "order_time" = #{orderTime,jdbcType=TIMESTAMP},
      "order_status" = #{orderStatus,jdbcType=VARCHAR},
      "item_name" = #{itemName,jdbcType=VARCHAR},
      "brand_name" = #{brandName,jdbcType=VARCHAR},
      "generic_name" = #{genericName,jdbcType=VARCHAR},
      "item_class" = #{itemClass,jdbcType=VARCHAR},
      "item_code" = #{itemCode,jdbcType=VARCHAR},
      "drug_spec" = #{drugSpec,jdbcType=VARCHAR},
      "dose_form" = #{doseForm,jdbcType=VARCHAR},
      "dosage" = #{dosage,jdbcType=DOUBLE},
      "dosage_units" = #{dosageUnits,jdbcType=VARCHAR},
      "least_number" = #{leastNumber,jdbcType=DOUBLE},
      "frequency" = #{frequency,jdbcType=VARCHAR},
      "unit_price" = #{unitPrice,jdbcType=DOUBLE},
      "administration_route" = #{administrationRoute,jdbcType=VARCHAR},
      "days" = #{days,jdbcType=INTEGER},
      "total_price" = #{totalPrice,jdbcType=DOUBLE},
      "perform_dept" = #{performDept,jdbcType=VARCHAR},
      "order_memo" = #{orderMemo,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getOutpOrder" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from "public"."outp_order" where patient_sn = #{patientSn} and visit_sn = #{visitSn}
  </select>
  <select id="getOrders" resultType="java.util.Map">
    select orderText as "orderText",date,stopTime  as "stopTime",orderDept  as "orderDept",orderDoctor  as "orderDoctor",source from (
                    SELECT
                      order_text AS orderText,
                      start_date_time AS date,
                      stop_date_time AS stopTime,
                      order_dept AS orderDept,
                      order_doctor AS orderDoctor,
                        '住院' as source
                    FROM
                      inp_order
                    WHERE
                        <if test="patientSn != null">
                          patient_sn = #{patientSn}
                        </if>
                        <if test="visitSn != null">
                          AND visit_sn = #{visitSn}
                        </if>
                       UNION ALL
                    SELECT
                      item_name AS orderText,
                      order_time AS date,
                      order_time AS stopTime,
                      order_dept AS orderDept,
                      order_doctor AS orderDoctor,
                      '门诊' as source
                    FROM
                      outp_order
                    WHERE
                    <if test="patientSn != null">
                      patient_sn = #{patientSn}
                    </if>
                    <if test="visitSn != null">
                      AND visit_sn = #{visitSn}
                    </if>
                  ) t order by t.date
  </select>

</mapper>
