<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpRegisterMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpRegister">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_sn_org" jdbcType="BIGINT" property="visitSnOrg" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="register_type" jdbcType="VARCHAR" property="registerType" />
    <result column="register_way" jdbcType="VARCHAR" property="registerWay" />
    <result column="register_dept" jdbcType="VARCHAR" property="registerDept" />
    <result column="register_doctor" jdbcType="VARCHAR" property="registerDoctor" />
    <result column="register_doctor_level" jdbcType="VARCHAR" property="registerDoctorLevel" />
    <result column="register_fee" jdbcType="DOUBLE" property="registerFee" />
    <result column="insurance_flag" jdbcType="VARCHAR" property="insuranceFlag" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="receipt_id" jdbcType="VARCHAR" property="receiptId" />
    <result column="emergency_flag" jdbcType="VARCHAR" property="emergencyFlag" />
    <result column="refound_flag" jdbcType="VARCHAR" property="refoundFlag" />
    <result column="visit_flag" jdbcType="VARCHAR" property="visitFlag" />
    <result column="register_state" jdbcType="VARCHAR" property="registerState" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_sn_org", "name", "register_time",
    "register_type", "register_way", "register_dept", "register_doctor", "register_doctor_level",
    "register_fee", "insurance_flag", "insurance_type", "receipt_id", "emergency_flag",
    "refound_flag", "visit_flag", "register_state", "source_path", "pk_id", "data_state",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpRegisterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_register"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."outp_register"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_register"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpRegisterExample">
    delete from "public"."outp_register"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpRegister">
    insert into "public"."outp_register" ("hospital_code", "patient_sn", "visit_sn",
      "visit_sn_org", "name", "register_time",
      "register_type", "register_way", "register_dept",
      "register_doctor", "register_doctor_level", "register_fee",
      "insurance_flag", "insurance_type", "receipt_id",
      "emergency_flag", "refound_flag", "visit_flag",
      "register_state", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitSnOrg,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{registerTime,jdbcType=TIMESTAMP},
      #{registerType,jdbcType=VARCHAR}, #{registerWay,jdbcType=VARCHAR}, #{registerDept,jdbcType=VARCHAR},
      #{registerDoctor,jdbcType=VARCHAR}, #{registerDoctorLevel,jdbcType=VARCHAR}, #{registerFee,jdbcType=DOUBLE},
      #{insuranceFlag,jdbcType=VARCHAR}, #{insuranceType,jdbcType=VARCHAR}, #{receiptId,jdbcType=VARCHAR},
      #{emergencyFlag,jdbcType=VARCHAR}, #{refoundFlag,jdbcType=VARCHAR}, #{visitFlag,jdbcType=VARCHAR},
      #{registerState,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpRegister">
    insert into "public"."outp_register"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitSnOrg != null">
        "visit_sn_org",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="registerTime != null">
        "register_time",
      </if>
      <if test="registerType != null">
        "register_type",
      </if>
      <if test="registerWay != null">
        "register_way",
      </if>
      <if test="registerDept != null">
        "register_dept",
      </if>
      <if test="registerDoctor != null">
        "register_doctor",
      </if>
      <if test="registerDoctorLevel != null">
        "register_doctor_level",
      </if>
      <if test="registerFee != null">
        "register_fee",
      </if>
      <if test="insuranceFlag != null">
        "insurance_flag",
      </if>
      <if test="insuranceType != null">
        "insurance_type",
      </if>
      <if test="receiptId != null">
        "receipt_id",
      </if>
      <if test="emergencyFlag != null">
        "emergency_flag",
      </if>
      <if test="refoundFlag != null">
        "refound_flag",
      </if>
      <if test="visitFlag != null">
        "visit_flag",
      </if>
      <if test="registerState != null">
        "register_state",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSnOrg != null">
        #{visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=VARCHAR},
      </if>
      <if test="registerWay != null">
        #{registerWay,jdbcType=VARCHAR},
      </if>
      <if test="registerDept != null">
        #{registerDept,jdbcType=VARCHAR},
      </if>
      <if test="registerDoctor != null">
        #{registerDoctor,jdbcType=VARCHAR},
      </if>
      <if test="registerDoctorLevel != null">
        #{registerDoctorLevel,jdbcType=VARCHAR},
      </if>
      <if test="registerFee != null">
        #{registerFee,jdbcType=DOUBLE},
      </if>
      <if test="insuranceFlag != null">
        #{insuranceFlag,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="receiptId != null">
        #{receiptId,jdbcType=VARCHAR},
      </if>
      <if test="emergencyFlag != null">
        #{emergencyFlag,jdbcType=VARCHAR},
      </if>
      <if test="refoundFlag != null">
        #{refoundFlag,jdbcType=VARCHAR},
      </if>
      <if test="visitFlag != null">
        #{visitFlag,jdbcType=VARCHAR},
      </if>
      <if test="registerState != null">
        #{registerState,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpRegisterExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_register"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_register"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSnOrg != null">
        "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.registerTime != null">
        "register_time" = #{record.registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registerType != null">
        "register_type" = #{record.registerType,jdbcType=VARCHAR},
      </if>
      <if test="record.registerWay != null">
        "register_way" = #{record.registerWay,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDept != null">
        "register_dept" = #{record.registerDept,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDoctor != null">
        "register_doctor" = #{record.registerDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDoctorLevel != null">
        "register_doctor_level" = #{record.registerDoctorLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.registerFee != null">
        "register_fee" = #{record.registerFee,jdbcType=DOUBLE},
      </if>
      <if test="record.insuranceFlag != null">
        "insurance_flag" = #{record.insuranceFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptId != null">
        "receipt_id" = #{record.receiptId,jdbcType=VARCHAR},
      </if>
      <if test="record.emergencyFlag != null">
        "emergency_flag" = #{record.emergencyFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.refoundFlag != null">
        "refound_flag" = #{record.refoundFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.visitFlag != null">
        "visit_flag" = #{record.visitFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.registerState != null">
        "register_state" = #{record.registerState,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_register"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      "name" = #{record.name,jdbcType=VARCHAR},
      "register_time" = #{record.registerTime,jdbcType=TIMESTAMP},
      "register_type" = #{record.registerType,jdbcType=VARCHAR},
      "register_way" = #{record.registerWay,jdbcType=VARCHAR},
      "register_dept" = #{record.registerDept,jdbcType=VARCHAR},
      "register_doctor" = #{record.registerDoctor,jdbcType=VARCHAR},
      "register_doctor_level" = #{record.registerDoctorLevel,jdbcType=VARCHAR},
      "register_fee" = #{record.registerFee,jdbcType=DOUBLE},
      "insurance_flag" = #{record.insuranceFlag,jdbcType=VARCHAR},
      "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      "receipt_id" = #{record.receiptId,jdbcType=VARCHAR},
      "emergency_flag" = #{record.emergencyFlag,jdbcType=VARCHAR},
      "refound_flag" = #{record.refoundFlag,jdbcType=VARCHAR},
      "visit_flag" = #{record.visitFlag,jdbcType=VARCHAR},
      "register_state" = #{record.registerState,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpRegister">
    update "public"."outp_register"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        "register_time" = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registerType != null">
        "register_type" = #{registerType,jdbcType=VARCHAR},
      </if>
      <if test="registerWay != null">
        "register_way" = #{registerWay,jdbcType=VARCHAR},
      </if>
      <if test="registerDept != null">
        "register_dept" = #{registerDept,jdbcType=VARCHAR},
      </if>
      <if test="registerDoctor != null">
        "register_doctor" = #{registerDoctor,jdbcType=VARCHAR},
      </if>
      <if test="registerDoctorLevel != null">
        "register_doctor_level" = #{registerDoctorLevel,jdbcType=VARCHAR},
      </if>
      <if test="registerFee != null">
        "register_fee" = #{registerFee,jdbcType=DOUBLE},
      </if>
      <if test="insuranceFlag != null">
        "insurance_flag" = #{insuranceFlag,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="receiptId != null">
        "receipt_id" = #{receiptId,jdbcType=VARCHAR},
      </if>
      <if test="emergencyFlag != null">
        "emergency_flag" = #{emergencyFlag,jdbcType=VARCHAR},
      </if>
      <if test="refoundFlag != null">
        "refound_flag" = #{refoundFlag,jdbcType=VARCHAR},
      </if>
      <if test="visitFlag != null">
        "visit_flag" = #{visitFlag,jdbcType=VARCHAR},
      </if>
      <if test="registerState != null">
        "register_state" = #{registerState,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpRegister">
    update "public"."outp_register"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "register_time" = #{registerTime,jdbcType=TIMESTAMP},
      "register_type" = #{registerType,jdbcType=VARCHAR},
      "register_way" = #{registerWay,jdbcType=VARCHAR},
      "register_dept" = #{registerDept,jdbcType=VARCHAR},
      "register_doctor" = #{registerDoctor,jdbcType=VARCHAR},
      "register_doctor_level" = #{registerDoctorLevel,jdbcType=VARCHAR},
      "register_fee" = #{registerFee,jdbcType=DOUBLE},
      "insurance_flag" = #{insuranceFlag,jdbcType=VARCHAR},
      "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      "receipt_id" = #{receiptId,jdbcType=VARCHAR},
      "emergency_flag" = #{emergencyFlag,jdbcType=VARCHAR},
      "refound_flag" = #{refoundFlag,jdbcType=VARCHAR},
      "visit_flag" = #{visitFlag,jdbcType=VARCHAR},
      "register_state" = #{registerState,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
