<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpSettleMasterMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpSettleMaster">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="rcpt_id" jdbcType="VARCHAR" property="rcptId" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="settling_date" jdbcType="TIMESTAMP" property="settlingDate" />
    <result column="charges" jdbcType="DOUBLE" property="charges" />
    <result column="costs" jdbcType="DOUBLE" property="costs" />
    <result column="insurance_self_pay" jdbcType="DOUBLE" property="insuranceSelfPay" />
    <result column="insurance_pay" jdbcType="DOUBLE" property="insurancePay" />
    <result column="billing_date_time" jdbcType="TIMESTAMP" property="billingDateTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "visit_date", "rcpt_id", "pay_way", 
    "insurance_type", "settling_date", "charges", "costs", "insurance_self_pay", "insurance_pay", 
    "billing_date_time", "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpSettleMasterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_settle_master"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."outp_settle_master"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_settle_master"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpSettleMasterExample">
    delete from "public"."outp_settle_master"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpSettleMaster">
    insert into "public"."outp_settle_master" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "visit_date", "rcpt_id", 
      "pay_way", "insurance_type", "settling_date", 
      "charges", "costs", "insurance_self_pay", 
      "insurance_pay", "billing_date_time", "source_path", 
      "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{visitDate,jdbcType=TIMESTAMP}, #{rcptId,jdbcType=VARCHAR}, 
      #{payWay,jdbcType=VARCHAR}, #{insuranceType,jdbcType=VARCHAR}, #{settlingDate,jdbcType=TIMESTAMP}, 
      #{charges,jdbcType=DOUBLE}, #{costs,jdbcType=DOUBLE}, #{insuranceSelfPay,jdbcType=DOUBLE}, 
      #{insurancePay,jdbcType=DOUBLE}, #{billingDateTime,jdbcType=TIMESTAMP}, #{sourcePath,jdbcType=VARCHAR}, 
      #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpSettleMaster">
    insert into "public"."outp_settle_master"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="rcptId != null">
        "rcpt_id",
      </if>
      <if test="payWay != null">
        "pay_way",
      </if>
      <if test="insuranceType != null">
        "insurance_type",
      </if>
      <if test="settlingDate != null">
        "settling_date",
      </if>
      <if test="charges != null">
        "charges",
      </if>
      <if test="costs != null">
        "costs",
      </if>
      <if test="insuranceSelfPay != null">
        "insurance_self_pay",
      </if>
      <if test="insurancePay != null">
        "insurance_pay",
      </if>
      <if test="billingDateTime != null">
        "billing_date_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="rcptId != null">
        #{rcptId,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="settlingDate != null">
        #{settlingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="charges != null">
        #{charges,jdbcType=DOUBLE},
      </if>
      <if test="costs != null">
        #{costs,jdbcType=DOUBLE},
      </if>
      <if test="insuranceSelfPay != null">
        #{insuranceSelfPay,jdbcType=DOUBLE},
      </if>
      <if test="insurancePay != null">
        #{insurancePay,jdbcType=DOUBLE},
      </if>
      <if test="billingDateTime != null">
        #{billingDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpSettleMasterExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_settle_master"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_settle_master"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rcptId != null">
        "rcpt_id" = #{record.rcptId,jdbcType=VARCHAR},
      </if>
      <if test="record.payWay != null">
        "pay_way" = #{record.payWay,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.settlingDate != null">
        "settling_date" = #{record.settlingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.charges != null">
        "charges" = #{record.charges,jdbcType=DOUBLE},
      </if>
      <if test="record.costs != null">
        "costs" = #{record.costs,jdbcType=DOUBLE},
      </if>
      <if test="record.insuranceSelfPay != null">
        "insurance_self_pay" = #{record.insuranceSelfPay,jdbcType=DOUBLE},
      </if>
      <if test="record.insurancePay != null">
        "insurance_pay" = #{record.insurancePay,jdbcType=DOUBLE},
      </if>
      <if test="record.billingDateTime != null">
        "billing_date_time" = #{record.billingDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_settle_master"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "rcpt_id" = #{record.rcptId,jdbcType=VARCHAR},
      "pay_way" = #{record.payWay,jdbcType=VARCHAR},
      "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      "settling_date" = #{record.settlingDate,jdbcType=TIMESTAMP},
      "charges" = #{record.charges,jdbcType=DOUBLE},
      "costs" = #{record.costs,jdbcType=DOUBLE},
      "insurance_self_pay" = #{record.insuranceSelfPay,jdbcType=DOUBLE},
      "insurance_pay" = #{record.insurancePay,jdbcType=DOUBLE},
      "billing_date_time" = #{record.billingDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpSettleMaster">
    update "public"."outp_settle_master"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="rcptId != null">
        "rcpt_id" = #{rcptId,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        "pay_way" = #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="settlingDate != null">
        "settling_date" = #{settlingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="charges != null">
        "charges" = #{charges,jdbcType=DOUBLE},
      </if>
      <if test="costs != null">
        "costs" = #{costs,jdbcType=DOUBLE},
      </if>
      <if test="insuranceSelfPay != null">
        "insurance_self_pay" = #{insuranceSelfPay,jdbcType=DOUBLE},
      </if>
      <if test="insurancePay != null">
        "insurance_pay" = #{insurancePay,jdbcType=DOUBLE},
      </if>
      <if test="billingDateTime != null">
        "billing_date_time" = #{billingDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpSettleMaster">
    update "public"."outp_settle_master"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "rcpt_id" = #{rcptId,jdbcType=VARCHAR},
      "pay_way" = #{payWay,jdbcType=VARCHAR},
      "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      "settling_date" = #{settlingDate,jdbcType=TIMESTAMP},
      "charges" = #{charges,jdbcType=DOUBLE},
      "costs" = #{costs,jdbcType=DOUBLE},
      "insurance_self_pay" = #{insuranceSelfPay,jdbcType=DOUBLE},
      "insurance_pay" = #{insurancePay,jdbcType=DOUBLE},
      "billing_date_time" = #{billingDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>