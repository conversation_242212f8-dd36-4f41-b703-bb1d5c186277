<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpVisitDiagMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpVisitDiag">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="diagnosis_class" jdbcType="VARCHAR" property="diagnosisClass" />
    <result column="diagnosis_order_no" jdbcType="VARCHAR" property="diagnosisOrderNo" />
    <result column="diagnosis_name" jdbcType="VARCHAR" property="diagnosisName" />
    <result column="diagnosis_code" jdbcType="VARCHAR" property="diagnosisCode" />
    <result column="diagnosis_code2" jdbcType="VARCHAR" property="diagnosisCode2" />
    <result column="is_main" jdbcType="VARCHAR" property="isMain" />
    <result column="diagnosis_date" jdbcType="TIMESTAMP" property="diagnosisDate" />
    <result column="doctor" jdbcType="VARCHAR" property="doctor" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "visit_date", "diagnosis_class", 
    "diagnosis_order_no", "diagnosis_name", "diagnosis_code", "diagnosis_code2", "is_main", 
    "diagnosis_date", "doctor", "dept_name", "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpVisitDiagExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_visit_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."outp_visit_diag"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_visit_diag"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpVisitDiagExample">
    delete from "public"."outp_visit_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpVisitDiag">
    insert into "public"."outp_visit_diag" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "visit_date", "diagnosis_class", 
      "diagnosis_order_no", "diagnosis_name", "diagnosis_code", 
      "diagnosis_code2", "is_main", "diagnosis_date", 
      "doctor", "dept_name", "source_path", 
      "data_state")
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{visitDate,jdbcType=TIMESTAMP}, #{diagnosisClass,jdbcType=VARCHAR}, 
      #{diagnosisOrderNo,jdbcType=VARCHAR}, #{diagnosisName,jdbcType=VARCHAR}, #{diagnosisCode,jdbcType=VARCHAR}, 
      #{diagnosisCode2,jdbcType=VARCHAR}, #{isMain,jdbcType=VARCHAR}, #{diagnosisDate,jdbcType=TIMESTAMP}, 
      #{doctor,jdbcType=VARCHAR}, #{deptName,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, 
      #{dataState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpVisitDiag">
    insert into "public"."outp_visit_diag"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="diagnosisClass != null">
        "diagnosis_class",
      </if>
      <if test="diagnosisOrderNo != null">
        "diagnosis_order_no",
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name",
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code",
      </if>
      <if test="diagnosisCode2 != null">
        "diagnosis_code2",
      </if>
      <if test="isMain != null">
        "is_main",
      </if>
      <if test="diagnosisDate != null">
        "diagnosis_date",
      </if>
      <if test="doctor != null">
        "doctor",
      </if>
      <if test="deptName != null">
        "dept_name",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="diagnosisClass != null">
        #{diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisOrderNo != null">
        #{diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode2 != null">
        #{diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        #{isMain,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisDate != null">
        #{diagnosisDate,jdbcType=TIMESTAMP},
      </if>
      <if test="doctor != null">
        #{doctor,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpVisitDiagExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_visit_diag"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_visit_diag"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.diagnosisClass != null">
        "diagnosis_class" = #{record.diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisOrderNo != null">
        "diagnosis_order_no" = #{record.diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisName != null">
        "diagnosis_name" = #{record.diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisCode != null">
        "diagnosis_code" = #{record.diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisCode2 != null">
        "diagnosis_code2" = #{record.diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="record.isMain != null">
        "is_main" = #{record.isMain,jdbcType=VARCHAR},
      </if>
      <if test="record.diagnosisDate != null">
        "diagnosis_date" = #{record.diagnosisDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.doctor != null">
        "doctor" = #{record.doctor,jdbcType=VARCHAR},
      </if>
      <if test="record.deptName != null">
        "dept_name" = #{record.deptName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_visit_diag"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "diagnosis_class" = #{record.diagnosisClass,jdbcType=VARCHAR},
      "diagnosis_order_no" = #{record.diagnosisOrderNo,jdbcType=VARCHAR},
      "diagnosis_name" = #{record.diagnosisName,jdbcType=VARCHAR},
      "diagnosis_code" = #{record.diagnosisCode,jdbcType=VARCHAR},
      "diagnosis_code2" = #{record.diagnosisCode2,jdbcType=VARCHAR},
      "is_main" = #{record.isMain,jdbcType=VARCHAR},
      "diagnosis_date" = #{record.diagnosisDate,jdbcType=TIMESTAMP},
      "doctor" = #{record.doctor,jdbcType=VARCHAR},
      "dept_name" = #{record.deptName,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpVisitDiag">
    update "public"."outp_visit_diag"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="diagnosisClass != null">
        "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisOrderNo != null">
        "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode2 != null">
        "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        "is_main" = #{isMain,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisDate != null">
        "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP},
      </if>
      <if test="doctor != null">
        "doctor" = #{doctor,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        "dept_name" = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpVisitDiag">
    update "public"."outp_visit_diag"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      "is_main" = #{isMain,jdbcType=VARCHAR},
      "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP},
      "doctor" = #{doctor,jdbcType=VARCHAR},
      "dept_name" = #{deptName,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>