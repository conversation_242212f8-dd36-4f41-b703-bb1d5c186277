<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.OutpVisitMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.OutpVisit">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_sn_org" jdbcType="BIGINT" property="visitSnOrg" />
    <result column="visit_date" jdbcType="TIMESTAMP" property="visitDate" />
    <result column="visit_times" jdbcType="VARCHAR" property="visitTimes" />
    <result column="register_type" jdbcType="VARCHAR" property="registerType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="charge_type" jdbcType="VARCHAR" property="chargeType" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="clinic_type" jdbcType="VARCHAR" property="clinicType" />
    <result column="outp_doctor" jdbcType="VARCHAR" property="outpDoctor" />
    <result column="outp_dept" jdbcType="VARCHAR" property="outpDept" />
    <result column="outp_status" jdbcType="VARCHAR" property="outpStatus" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_sn_org", "visit_date", "visit_times",
    "register_type", "name", "gender", "age", "charge_type", "insurance_type", "clinic_type",
    "outp_doctor", "outp_dept", "outp_status", "source_path", "pk_id", "data_state",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.OutpVisitExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."outp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."outp_visit"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."outp_visit"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.OutpVisitExample">
    delete from "public"."outp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.OutpVisit">
    insert into "public"."outp_visit" ("hospital_code", "patient_sn", "visit_sn",
      "visit_sn_org", "visit_date", "visit_times",
      "register_type", "name", "gender",
      "age", "charge_type", "insurance_type",
      "clinic_type", "outp_doctor", "outp_dept",
      "outp_status", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitSnOrg,jdbcType=BIGINT}, #{visitDate,jdbcType=TIMESTAMP}, #{visitTimes,jdbcType=VARCHAR},
      #{registerType,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR},
      #{age,jdbcType=INTEGER}, #{chargeType,jdbcType=VARCHAR}, #{insuranceType,jdbcType=VARCHAR},
      #{clinicType,jdbcType=VARCHAR}, #{outpDoctor,jdbcType=VARCHAR}, #{outpDept,jdbcType=VARCHAR},
      #{outpStatus,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.OutpVisit">
    insert into "public"."outp_visit"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitSnOrg != null">
        "visit_sn_org",
      </if>
      <if test="visitDate != null">
        "visit_date",
      </if>
      <if test="visitTimes != null">
        "visit_times",
      </if>
      <if test="registerType != null">
        "register_type",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="gender != null">
        "gender",
      </if>
      <if test="age != null">
        "age",
      </if>
      <if test="chargeType != null">
        "charge_type",
      </if>
      <if test="insuranceType != null">
        "insurance_type",
      </if>
      <if test="clinicType != null">
        "clinic_type",
      </if>
      <if test="outpDoctor != null">
        "outp_doctor",
      </if>
      <if test="outpDept != null">
        "outp_dept",
      </if>
      <if test="outpStatus != null">
        "outp_status",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSnOrg != null">
        #{visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="visitTimes != null">
        #{visitTimes,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="clinicType != null">
        #{clinicType,jdbcType=VARCHAR},
      </if>
      <if test="outpDoctor != null">
        #{outpDoctor,jdbcType=VARCHAR},
      </if>
      <if test="outpDept != null">
        #{outpDept,jdbcType=VARCHAR},
      </if>
      <if test="outpStatus != null">
        #{outpStatus,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.OutpVisitExample" resultType="java.lang.Long">
    select count(*) from "public"."outp_visit"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."outp_visit"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSnOrg != null">
        "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      </if>
      <if test="record.visitDate != null">
        "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.visitTimes != null">
        "visit_times" = #{record.visitTimes,jdbcType=VARCHAR},
      </if>
      <if test="record.registerType != null">
        "register_type" = #{record.registerType,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        "gender" = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        "age" = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.chargeType != null">
        "charge_type" = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.clinicType != null">
        "clinic_type" = #{record.clinicType,jdbcType=VARCHAR},
      </if>
      <if test="record.outpDoctor != null">
        "outp_doctor" = #{record.outpDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.outpDept != null">
        "outp_dept" = #{record.outpDept,jdbcType=VARCHAR},
      </if>
      <if test="record.outpStatus != null">
        "outp_status" = #{record.outpStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."outp_visit"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_sn_org" = #{record.visitSnOrg,jdbcType=BIGINT},
      "visit_date" = #{record.visitDate,jdbcType=TIMESTAMP},
      "visit_times" = #{record.visitTimes,jdbcType=VARCHAR},
      "register_type" = #{record.registerType,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "gender" = #{record.gender,jdbcType=VARCHAR},
      "age" = #{record.age,jdbcType=INTEGER},
      "charge_type" = #{record.chargeType,jdbcType=VARCHAR},
      "insurance_type" = #{record.insuranceType,jdbcType=VARCHAR},
      "clinic_type" = #{record.clinicType,jdbcType=VARCHAR},
      "outp_doctor" = #{record.outpDoctor,jdbcType=VARCHAR},
      "outp_dept" = #{record.outpDept,jdbcType=VARCHAR},
      "outp_status" = #{record.outpStatus,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.OutpVisit">
    update "public"."outp_visit"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="visitTimes != null">
        "visit_times" = #{visitTimes,jdbcType=VARCHAR},
      </if>
      <if test="registerType != null">
        "register_type" = #{registerType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        "charge_type" = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="clinicType != null">
        "clinic_type" = #{clinicType,jdbcType=VARCHAR},
      </if>
      <if test="outpDoctor != null">
        "outp_doctor" = #{outpDoctor,jdbcType=VARCHAR},
      </if>
      <if test="outpDept != null">
        "outp_dept" = #{outpDept,jdbcType=VARCHAR},
      </if>
      <if test="outpStatus != null">
        "outp_status" = #{outpStatus,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.OutpVisit">
    update "public"."outp_visit"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_date" = #{visitDate,jdbcType=TIMESTAMP},
      "visit_times" = #{visitTimes,jdbcType=VARCHAR},
      "register_type" = #{registerType,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "charge_type" = #{chargeType,jdbcType=VARCHAR},
      "insurance_type" = #{insuranceType,jdbcType=VARCHAR},
      "clinic_type" = #{clinicType,jdbcType=VARCHAR},
      "outp_doctor" = #{outpDoctor,jdbcType=VARCHAR},
      "outp_dept" = #{outpDept,jdbcType=VARCHAR},
      "outp_status" = #{outpStatus,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getOutpVisit" resultMap="BaseResultMap">
    select * from "public"."outp_visit" where patient_sn = #{patientSn} order by visit_date asc
  </select>

  <select id="count" resultType="long">
    select count(patient_sn) from "public"."outp_visit"
  </select>

</mapper>
