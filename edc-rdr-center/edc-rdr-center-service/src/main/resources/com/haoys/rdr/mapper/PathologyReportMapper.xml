<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.PathologyReportMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.PathologyReport">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_sn_org" jdbcType="VARCHAR" property="visitSnOrg" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="path_sys_code" jdbcType="VARCHAR" property="pathSysCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="patient_source" jdbcType="VARCHAR" property="patientSource" />
    <result column="sub_hospital" jdbcType="VARCHAR" property="subHospital" />
    <result column="sub_dept" jdbcType="VARCHAR" property="subDept" />
    <result column="sub_doctor" jdbcType="VARCHAR" property="subDoctor" />
    <result column="sub_date" jdbcType="TIMESTAMP" property="subDate" />
    <result column="sub_part" jdbcType="VARCHAR" property="subPart" />
    <result column="clin_diag" jdbcType="VARCHAR" property="clinDiag" />
    <result column="receipt_time" jdbcType="TIMESTAMP" property="receiptTime" />
    <result column="exam_time" jdbcType="TIMESTAMP" property="examTime" />
    <result column="exam_dept" jdbcType="VARCHAR" property="examDept" />
    <result column="exam_doctor" jdbcType="VARCHAR" property="examDoctor" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="path_eye_see" jdbcType="VARCHAR" property="pathEyeSee" />
    <result column="path_microscope" jdbcType="VARCHAR" property="pathMicroscope" />
    <result column="path_diag" jdbcType="VARCHAR" property="pathDiag" />
    <result column="path_diag_code" jdbcType="VARCHAR" property="pathDiagCode" />
    <result column="report_doctor" jdbcType="VARCHAR" property="reportDoctor" />
    <result column="review_doctor" jdbcType="VARCHAR" property="reviewDoctor" />
    <result column="report_status" jdbcType="VARCHAR" property="reportStatus" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="exam_name" jdbcType="VARCHAR" property="examName" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "visit_sn_org", "tpatno", "path_sys_code",
    "name", "patient_source", "sub_hospital", "sub_dept", "sub_doctor", "sub_date", "sub_part",
    "clin_diag", "receipt_time", "exam_time", "exam_dept", "exam_doctor", "report_time",
    "review_time", "path_eye_see", "path_microscope", "path_diag", "path_diag_code",
    "report_doctor", "review_doctor", "report_status", "source_path", "pk_id", "data_state",
    "exam_name", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.PathologyReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."pathology_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."pathology_report"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."pathology_report"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.PathologyReportExample">
    delete from "public"."pathology_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.PathologyReport">
    insert into "public"."pathology_report" ("hospital_code", "patient_sn", "visit_sn",
      "visit_sn_org", "tpatno", "path_sys_code",
      "name", "patient_source", "sub_hospital",
      "sub_dept", "sub_doctor", "sub_date",
      "sub_part", "clin_diag", "receipt_time",
      "exam_time", "exam_dept", "exam_doctor",
      "report_time", "review_time", "path_eye_see",
      "path_microscope", "path_diag", "path_diag_code",
      "report_doctor", "review_doctor", "report_status",
      "source_path", "pk_id", "data_state",
      "exam_name", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{visitSnOrg,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{pathSysCode,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{patientSource,jdbcType=VARCHAR}, #{subHospital,jdbcType=VARCHAR},
      #{subDept,jdbcType=VARCHAR}, #{subDoctor,jdbcType=VARCHAR}, #{subDate,jdbcType=TIMESTAMP},
      #{subPart,jdbcType=VARCHAR}, #{clinDiag,jdbcType=VARCHAR}, #{receiptTime,jdbcType=TIMESTAMP},
      #{examTime,jdbcType=TIMESTAMP}, #{examDept,jdbcType=VARCHAR}, #{examDoctor,jdbcType=VARCHAR},
      #{reportTime,jdbcType=TIMESTAMP}, #{reviewTime,jdbcType=TIMESTAMP}, #{pathEyeSee,jdbcType=VARCHAR},
      #{pathMicroscope,jdbcType=VARCHAR}, #{pathDiag,jdbcType=VARCHAR}, #{pathDiagCode,jdbcType=VARCHAR},
      #{reportDoctor,jdbcType=VARCHAR}, #{reviewDoctor,jdbcType=VARCHAR}, #{reportStatus,jdbcType=VARCHAR},
      #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR},
      #{examName,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.PathologyReport">
    insert into "public"."pathology_report"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitSnOrg != null">
        "visit_sn_org",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="pathSysCode != null">
        "path_sys_code",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="patientSource != null">
        "patient_source",
      </if>
      <if test="subHospital != null">
        "sub_hospital",
      </if>
      <if test="subDept != null">
        "sub_dept",
      </if>
      <if test="subDoctor != null">
        "sub_doctor",
      </if>
      <if test="subDate != null">
        "sub_date",
      </if>
      <if test="subPart != null">
        "sub_part",
      </if>
      <if test="clinDiag != null">
        "clin_diag",
      </if>
      <if test="receiptTime != null">
        "receipt_time",
      </if>
      <if test="examTime != null">
        "exam_time",
      </if>
      <if test="examDept != null">
        "exam_dept",
      </if>
      <if test="examDoctor != null">
        "exam_doctor",
      </if>
      <if test="reportTime != null">
        "report_time",
      </if>
      <if test="reviewTime != null">
        "review_time",
      </if>
      <if test="pathEyeSee != null">
        "path_eye_see",
      </if>
      <if test="pathMicroscope != null">
        "path_microscope",
      </if>
      <if test="pathDiag != null">
        "path_diag",
      </if>
      <if test="pathDiagCode != null">
        "path_diag_code",
      </if>
      <if test="reportDoctor != null">
        "report_doctor",
      </if>
      <if test="reviewDoctor != null">
        "review_doctor",
      </if>
      <if test="reportStatus != null">
        "report_status",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="examName != null">
        "exam_name",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSnOrg != null">
        #{visitSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="pathSysCode != null">
        #{pathSysCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="patientSource != null">
        #{patientSource,jdbcType=VARCHAR},
      </if>
      <if test="subHospital != null">
        #{subHospital,jdbcType=VARCHAR},
      </if>
      <if test="subDept != null">
        #{subDept,jdbcType=VARCHAR},
      </if>
      <if test="subDoctor != null">
        #{subDoctor,jdbcType=VARCHAR},
      </if>
      <if test="subDate != null">
        #{subDate,jdbcType=TIMESTAMP},
      </if>
      <if test="subPart != null">
        #{subPart,jdbcType=VARCHAR},
      </if>
      <if test="clinDiag != null">
        #{clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="receiptTime != null">
        #{receiptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="examTime != null">
        #{examTime,jdbcType=TIMESTAMP},
      </if>
      <if test="examDept != null">
        #{examDept,jdbcType=VARCHAR},
      </if>
      <if test="examDoctor != null">
        #{examDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewTime != null">
        #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pathEyeSee != null">
        #{pathEyeSee,jdbcType=VARCHAR},
      </if>
      <if test="pathMicroscope != null">
        #{pathMicroscope,jdbcType=VARCHAR},
      </if>
      <if test="pathDiag != null">
        #{pathDiag,jdbcType=VARCHAR},
      </if>
      <if test="pathDiagCode != null">
        #{pathDiagCode,jdbcType=VARCHAR},
      </if>
      <if test="reportDoctor != null">
        #{reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reviewDoctor != null">
        #{reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="examName != null">
        #{examName,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.PathologyReportExample" resultType="java.lang.Long">
    select count(*) from "public"."pathology_report"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."pathology_report"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSnOrg != null">
        "visit_sn_org" = #{record.visitSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.pathSysCode != null">
        "path_sys_code" = #{record.pathSysCode,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSource != null">
        "patient_source" = #{record.patientSource,jdbcType=VARCHAR},
      </if>
      <if test="record.subHospital != null">
        "sub_hospital" = #{record.subHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.subDept != null">
        "sub_dept" = #{record.subDept,jdbcType=VARCHAR},
      </if>
      <if test="record.subDoctor != null">
        "sub_doctor" = #{record.subDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.subDate != null">
        "sub_date" = #{record.subDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.subPart != null">
        "sub_part" = #{record.subPart,jdbcType=VARCHAR},
      </if>
      <if test="record.clinDiag != null">
        "clin_diag" = #{record.clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptTime != null">
        "receipt_time" = #{record.receiptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.examTime != null">
        "exam_time" = #{record.examTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.examDept != null">
        "exam_dept" = #{record.examDept,jdbcType=VARCHAR},
      </if>
      <if test="record.examDoctor != null">
        "exam_doctor" = #{record.examDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.reportTime != null">
        "report_time" = #{record.reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewTime != null">
        "review_time" = #{record.reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pathEyeSee != null">
        "path_eye_see" = #{record.pathEyeSee,jdbcType=VARCHAR},
      </if>
      <if test="record.pathMicroscope != null">
        "path_microscope" = #{record.pathMicroscope,jdbcType=VARCHAR},
      </if>
      <if test="record.pathDiag != null">
        "path_diag" = #{record.pathDiag,jdbcType=VARCHAR},
      </if>
      <if test="record.pathDiagCode != null">
        "path_diag_code" = #{record.pathDiagCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDoctor != null">
        "report_doctor" = #{record.reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewDoctor != null">
        "review_doctor" = #{record.reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.reportStatus != null">
        "report_status" = #{record.reportStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.examName != null">
        "exam_name" = #{record.examName,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."pathology_report"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_sn_org" = #{record.visitSnOrg,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "path_sys_code" = #{record.pathSysCode,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "patient_source" = #{record.patientSource,jdbcType=VARCHAR},
      "sub_hospital" = #{record.subHospital,jdbcType=VARCHAR},
      "sub_dept" = #{record.subDept,jdbcType=VARCHAR},
      "sub_doctor" = #{record.subDoctor,jdbcType=VARCHAR},
      "sub_date" = #{record.subDate,jdbcType=TIMESTAMP},
      "sub_part" = #{record.subPart,jdbcType=VARCHAR},
      "clin_diag" = #{record.clinDiag,jdbcType=VARCHAR},
      "receipt_time" = #{record.receiptTime,jdbcType=TIMESTAMP},
      "exam_time" = #{record.examTime,jdbcType=TIMESTAMP},
      "exam_dept" = #{record.examDept,jdbcType=VARCHAR},
      "exam_doctor" = #{record.examDoctor,jdbcType=VARCHAR},
      "report_time" = #{record.reportTime,jdbcType=TIMESTAMP},
      "review_time" = #{record.reviewTime,jdbcType=TIMESTAMP},
      "path_eye_see" = #{record.pathEyeSee,jdbcType=VARCHAR},
      "path_microscope" = #{record.pathMicroscope,jdbcType=VARCHAR},
      "path_diag" = #{record.pathDiag,jdbcType=VARCHAR},
      "path_diag_code" = #{record.pathDiagCode,jdbcType=VARCHAR},
      "report_doctor" = #{record.reportDoctor,jdbcType=VARCHAR},
      "review_doctor" = #{record.reviewDoctor,jdbcType=VARCHAR},
      "report_status" = #{record.reportStatus,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "exam_name" = #{record.examName,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.PathologyReport">
    update "public"."pathology_report"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="pathSysCode != null">
        "path_sys_code" = #{pathSysCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="patientSource != null">
        "patient_source" = #{patientSource,jdbcType=VARCHAR},
      </if>
      <if test="subHospital != null">
        "sub_hospital" = #{subHospital,jdbcType=VARCHAR},
      </if>
      <if test="subDept != null">
        "sub_dept" = #{subDept,jdbcType=VARCHAR},
      </if>
      <if test="subDoctor != null">
        "sub_doctor" = #{subDoctor,jdbcType=VARCHAR},
      </if>
      <if test="subDate != null">
        "sub_date" = #{subDate,jdbcType=TIMESTAMP},
      </if>
      <if test="subPart != null">
        "sub_part" = #{subPart,jdbcType=VARCHAR},
      </if>
      <if test="clinDiag != null">
        "clin_diag" = #{clinDiag,jdbcType=VARCHAR},
      </if>
      <if test="receiptTime != null">
        "receipt_time" = #{receiptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportTime != null">
        "report_time" = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewTime != null">
        "review_time" = #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pathEyeSee != null">
        "path_eye_see" = #{pathEyeSee,jdbcType=VARCHAR},
      </if>
      <if test="pathMicroscope != null">
        "path_microscope" = #{pathMicroscope,jdbcType=VARCHAR},
      </if>
      <if test="pathDiag != null">
        "path_diag" = #{pathDiag,jdbcType=VARCHAR},
      </if>
      <if test="pathDiagCode != null">
        "path_diag_code" = #{pathDiagCode,jdbcType=VARCHAR},
      </if>
      <if test="reportDoctor != null">
        "report_doctor" = #{reportDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reviewDoctor != null">
        "review_doctor" = #{reviewDoctor,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        "report_status" = #{reportStatus,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="examSmallClass != null">
        "exam_small_class" = #{examSmallClass,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.PathologyReport">
    update "public"."pathology_report"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "path_sys_code" = #{pathSysCode,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "patient_source" = #{patientSource,jdbcType=VARCHAR},
      "sub_hospital" = #{subHospital,jdbcType=VARCHAR},
      "sub_dept" = #{subDept,jdbcType=VARCHAR},
      "sub_doctor" = #{subDoctor,jdbcType=VARCHAR},
      "sub_date" = #{subDate,jdbcType=TIMESTAMP},
      "sub_part" = #{subPart,jdbcType=VARCHAR},
      "clin_diag" = #{clinDiag,jdbcType=VARCHAR},
      "receipt_time" = #{receiptTime,jdbcType=TIMESTAMP},
      "report_time" = #{reportTime,jdbcType=TIMESTAMP},
      "review_time" = #{reviewTime,jdbcType=TIMESTAMP},
      "path_eye_see" = #{pathEyeSee,jdbcType=VARCHAR},
      "path_microscope" = #{pathMicroscope,jdbcType=VARCHAR},
      "path_diag" = #{pathDiag,jdbcType=VARCHAR},
      "path_diag_code" = #{pathDiagCode,jdbcType=VARCHAR},
      "report_doctor" = #{reportDoctor,jdbcType=VARCHAR},
      "review_doctor" = #{reviewDoctor,jdbcType=VARCHAR},
      "report_status" = #{reportStatus,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "exam_small_class" = #{examSmallClass,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getPatientPathologyReport" resultMap="BaseResultMap">
    select * from public.pathology_report where patient_sn = #{patientId} order by report_time desc limit 1
  </select>
</mapper>
