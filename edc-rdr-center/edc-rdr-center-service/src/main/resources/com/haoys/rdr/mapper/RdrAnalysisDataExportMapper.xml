<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrAnalysisDataExportMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrAnalysisDataExport">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="testee_count" jdbcType="BIGINT" property="testeeCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="export_file_type" jdbcType="VARCHAR" property="exportFileType" />
    <result column="operator" jdbcType="BIGINT" property="operator" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="export_status" jdbcType="INTEGER" property="exportStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, task_name, testee_count, create_time, description, export_file_type,
    operator, org_id, org_name, status, download_url, tenant_id, platform_id, export_status
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.ProjectTesteeExportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_export
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_testee_export
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_export
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.ProjectTesteeExportExample">
    delete from project_testee_export
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrAnalysisDataExport">
    insert into project_testee_export (id, project_id, task_name,
      testee_count, create_time, description,
      export_file_type, operator, org_id,
      org_name, status, download_url,
      tenant_id, platform_id, export_status
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR},
      #{testeeCount,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR},
      #{exportFileType,jdbcType=VARCHAR}, #{operator,jdbcType=BIGINT}, #{orgId,jdbcType=VARCHAR},
      #{orgName,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{downloadUrl,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}, #{exportStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrAnalysisDataExport">
    insert into project_testee_export
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="testeeCount != null">
        testee_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="exportFileType != null">
        export_file_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="downloadUrl != null">
        download_url,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="exportStatus != null">
        export_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="testeeCount != null">
        #{testeeCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="exportFileType != null">
        #{exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        #{exportStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.ProjectTesteeExportExample" resultType="java.lang.Long">
    select count(*) from project_testee_export
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_export
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.testeeCount != null">
        testee_count = #{record.testeeCount,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.exportFileType != null">
        export_file_type = #{record.exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadUrl != null">
        download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.exportStatus != null">
        export_status = #{record.exportStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_export
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      testee_count = #{record.testeeCount,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      description = #{record.description,jdbcType=VARCHAR},
      export_file_type = #{record.exportFileType,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      export_status = #{record.exportStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrAnalysisDataExport">
    update project_testee_export
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="testeeCount != null">
        testee_count = #{testeeCount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="exportFileType != null">
        export_file_type = #{exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        export_status = #{exportStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrAnalysisDataExport">
    update project_testee_export
    set project_id = #{projectId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      testee_count = #{testeeCount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      export_file_type = #{exportFileType,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      download_url = #{downloadUrl,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      export_status = #{exportStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="rmById">
    update project_testee_export set status= 1 where id=#{id}
  </update>


  <update id="deleteById">
    delete from project_testee_export where id=#{id}
  </update>

  <select id="getPorjectTesteeCount" resultType="java.lang.Long">
    SELECT
      IFNULL(count(project_testee_info.id),0) count
    FROM
    project_testee_info
    LEFT JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId}
    <if test="orgId != null">
      AND project_testee_info.owner_org_id = #{orgId}
    </if>
  </select>

</mapper>
