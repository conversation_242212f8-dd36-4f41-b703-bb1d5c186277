<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrDiseaseDatabaseUserMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrDiseaseDatabaseUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="database_id" jdbcType="BIGINT" property="databaseId" />
    <result column="database_name" jdbcType="VARCHAR" property="databaseName" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="validate_start_date" jdbcType="TIMESTAMP" property="validateStartDate" />
    <result column="validate_end_date" jdbcType="TIMESTAMP" property="validateEndDate" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="getDiseaseDatabaseUser" resultMap="BaseResultMap">
    select * from disease_database_user where database_id = #{dataBaseId} and user_id = #{userId} limit 1
  </select>

  <select id="getDiseaseDatabaseListByUserId" resultMap="BaseResultMap">
    select * from  disease_database_user where user_id = #{userId}
  </select>
</mapper>