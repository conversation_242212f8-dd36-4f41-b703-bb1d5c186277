<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientAnalysisDatasetMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientAnalysisDataset">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="data_base_id" jdbcType="VARCHAR" property="dataBaseId" />
    <result column="dataset_name" jdbcType="VARCHAR" property="datasetName" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "data_base_id", "dataset_name", "batch_code", "description", "create_user_id", 
    "status", "enabled", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDatasetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_analysis_dataset"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_analysis_dataset"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_analysis_dataset"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDatasetExample">
    delete from "public"."rdr_patient_analysis_dataset"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDataset">
    insert into "public"."rdr_patient_analysis_dataset" ("id", "data_base_id", "dataset_name", 
      "batch_code", "description", "create_user_id", 
      "status", "enabled", "create_time"
      )
    values (#{id,jdbcType=VARCHAR}, #{dataBaseId,jdbcType=VARCHAR}, #{datasetName,jdbcType=VARCHAR}, 
      #{batchCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{enabled,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDataset">
    insert into "public"."rdr_patient_analysis_dataset"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="dataBaseId != null">
        "data_base_id",
      </if>
      <if test="datasetName != null">
        "dataset_name",
      </if>
      <if test="batchCode != null">
        "batch_code",
      </if>
      <if test="description != null">
        "description",
      </if>
      <if test="createUserId != null">
        "create_user_id",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="enabled != null">
        "enabled",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseId != null">
        #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="datasetName != null">
        #{datasetName,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDatasetExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_analysis_dataset"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_analysis_dataset"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBaseId != null">
        "data_base_id" = #{record.dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.datasetName != null">
        "dataset_name" = #{record.datasetName,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        "batch_code" = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        "description" = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.enabled != null">
        "enabled" = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_analysis_dataset"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "data_base_id" = #{record.dataBaseId,jdbcType=VARCHAR},
      "dataset_name" = #{record.datasetName,jdbcType=VARCHAR},
      "batch_code" = #{record.batchCode,jdbcType=VARCHAR},
      "description" = #{record.description,jdbcType=VARCHAR},
      "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "enabled" = #{record.enabled,jdbcType=BIT},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDataset">
    update "public"."rdr_patient_analysis_dataset"
    <set>
      <if test="dataBaseId != null">
        "data_base_id" = #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="datasetName != null">
        "dataset_name" = #{datasetName,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        "batch_code" = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        "description" = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        "create_user_id" = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        "enabled" = #{enabled,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientAnalysisDataset">
    update "public"."rdr_patient_analysis_dataset"
    set "data_base_id" = #{dataBaseId,jdbcType=VARCHAR},
      "dataset_name" = #{datasetName,jdbcType=VARCHAR},
      "batch_code" = #{batchCode,jdbcType=VARCHAR},
      "description" = #{description,jdbcType=VARCHAR},
      "create_user_id" = #{createUserId,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "enabled" = #{enabled,jdbcType=BIT},
      "create_time" = #{createTime,jdbcType=TIMESTAMP}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getRdrPatientAnalysisDataSetTitle" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from rdr_patient_analysis_dataset where dataset_name = #{title} limit 1
  </select>

  <select id="getRdrPatientAnalysisDataSetBatchCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from rdr_patient_analysis_dataset where batch_code = #{batchCode} limit 1
  </select>

  <select id="getPatientAnalysisDataSetForPage" resultType="com.haoys.rdr.domain.vo.RdrPatientAnalysisDatasetVo">
    select <include refid="Base_Column_List" /> from rdr_patient_analysis_dataset
    where 1 = 1
    <if test="dataBaseId != null and dataBaseId != ''">
      and data_base_id = #{dataBaseId}
    </if>
    <if test="batchCode != null and batchCode != ''">
      and (batch_code = #{batchCode} or dataset_name like '%${batchCode}%')
    </if>
    <if test="enabledValue != null">
      and enabled = #{enabledValue,jdbcType=BIT}
    </if>
    and create_user_id = #{createUserId,jdbcType=VARCHAR}
    order by create_time desc
  </select>
</mapper>