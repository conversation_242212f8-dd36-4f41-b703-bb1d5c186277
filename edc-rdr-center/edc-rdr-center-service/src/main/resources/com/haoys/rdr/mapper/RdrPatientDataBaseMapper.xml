<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientDataBaseMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientDataBase">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="data_base" jdbcType="VARCHAR" property="dataBase" />
    <result column="data_desc" jdbcType="VARCHAR" property="dataDesc" />
    <result column="patient_num" jdbcType="INTEGER" property="patientNum" />
    <result column="dept" jdbcType="VARCHAR" property="dept" />
    <result column="director" jdbcType="VARCHAR" property="director" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="data_image" jdbcType="VARCHAR" property="dataImage" />
    <result column="data_base_rule" jdbcType="VARCHAR" property="dataBaseRule" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="data_base_url" jdbcType="VARCHAR" property="dataBaseUrl" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "data_base", "data_desc", "patient_num", "dept", "director", "data_source", 
    "data_image", "data_base_rule", "create_time", "update_time", "create_by", "status", 
    "data_base_url", "update_by"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_data_base"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_data_base"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_data_base"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseExample">
    delete from "public"."rdr_patient_data_base"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientDataBase">
    insert into "public"."rdr_patient_data_base" ("id", "data_base", "data_desc", 
      "patient_num", "dept", "director", 
      "data_source", "data_image", "data_base_rule", 
      "create_time", "update_time", "create_by", 
      "status", "data_base_url", "update_by"
      )
    values (#{id,jdbcType=VARCHAR}, #{dataBase,jdbcType=VARCHAR}, #{dataDesc,jdbcType=VARCHAR}, 
      #{patientNum,jdbcType=INTEGER}, #{dept,jdbcType=VARCHAR}, #{director,jdbcType=VARCHAR}, 
      #{dataSource,jdbcType=VARCHAR}, #{dataImage,jdbcType=VARCHAR}, #{dataBaseRule,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{dataBaseUrl,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientDataBase">
    insert into "public"."rdr_patient_data_base"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="dataBase != null">
        "data_base",
      </if>
      <if test="dataDesc != null">
        "data_desc",
      </if>
      <if test="patientNum != null">
        "patient_num",
      </if>
      <if test="dept != null">
        "dept",
      </if>
      <if test="director != null">
        "director",
      </if>
      <if test="dataSource != null">
        "data_source",
      </if>
      <if test="dataImage != null">
        "data_image",
      </if>
      <if test="dataBaseRule != null">
        "data_base_rule",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
      <if test="updateTime != null">
        "update_time",
      </if>
      <if test="createBy != null">
        "create_by",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="dataBaseUrl != null">
        "data_base_url",
      </if>
      <if test="updateBy != null">
        "update_by",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dataBase != null">
        #{dataBase,jdbcType=VARCHAR},
      </if>
      <if test="dataDesc != null">
        #{dataDesc,jdbcType=VARCHAR},
      </if>
      <if test="patientNum != null">
        #{patientNum,jdbcType=INTEGER},
      </if>
      <if test="dept != null">
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="director != null">
        #{director,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="dataImage != null">
        #{dataImage,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseRule != null">
        #{dataBaseRule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseUrl != null">
        #{dataBaseUrl,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_data_base"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_data_base"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBase != null">
        "data_base" = #{record.dataBase,jdbcType=VARCHAR},
      </if>
      <if test="record.dataDesc != null">
        "data_desc" = #{record.dataDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.patientNum != null">
        "patient_num" = #{record.patientNum,jdbcType=INTEGER},
      </if>
      <if test="record.dept != null">
        "dept" = #{record.dept,jdbcType=VARCHAR},
      </if>
      <if test="record.director != null">
        "director" = #{record.director,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSource != null">
        "data_source" = #{record.dataSource,jdbcType=VARCHAR},
      </if>
      <if test="record.dataImage != null">
        "data_image" = #{record.dataImage,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBaseRule != null">
        "data_base_rule" = #{record.dataBaseRule,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        "update_time" = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        "create_by" = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBaseUrl != null">
        "data_base_url" = #{record.dataBaseUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        "update_by" = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_data_base"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "data_base" = #{record.dataBase,jdbcType=VARCHAR},
      "data_desc" = #{record.dataDesc,jdbcType=VARCHAR},
      "patient_num" = #{record.patientNum,jdbcType=INTEGER},
      "dept" = #{record.dept,jdbcType=VARCHAR},
      "director" = #{record.director,jdbcType=VARCHAR},
      "data_source" = #{record.dataSource,jdbcType=VARCHAR},
      "data_image" = #{record.dataImage,jdbcType=VARCHAR},
      "data_base_rule" = #{record.dataBaseRule,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      "update_time" = #{record.updateTime,jdbcType=TIMESTAMP},
      "create_by" = #{record.createBy,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "data_base_url" = #{record.dataBaseUrl,jdbcType=VARCHAR},
      "update_by" = #{record.updateBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientDataBase">
    update "public"."rdr_patient_data_base"
    <set>
      <if test="dataBase != null">
        "data_base" = #{dataBase,jdbcType=VARCHAR},
      </if>
      <if test="dataDesc != null">
        "data_desc" = #{dataDesc,jdbcType=VARCHAR},
      </if>
      <if test="patientNum != null">
        "patient_num" = #{patientNum,jdbcType=INTEGER},
      </if>
      <if test="dept != null">
        "dept" = #{dept,jdbcType=VARCHAR},
      </if>
      <if test="director != null">
        "director" = #{director,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        "data_source" = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="dataImage != null">
        "data_image" = #{dataImage,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseRule != null">
        "data_base_rule" = #{dataBaseRule,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        "update_time" = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        "create_by" = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseUrl != null">
        "data_base_url" = #{dataBaseUrl,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        "update_by" = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientDataBase">
    update "public"."rdr_patient_data_base"
    set "data_base" = #{dataBase,jdbcType=VARCHAR},
      "data_desc" = #{dataDesc,jdbcType=VARCHAR},
      "patient_num" = #{patientNum,jdbcType=INTEGER},
      "dept" = #{dept,jdbcType=VARCHAR},
      "director" = #{director,jdbcType=VARCHAR},
      "data_source" = #{dataSource,jdbcType=VARCHAR},
      "data_image" = #{dataImage,jdbcType=VARCHAR},
      "data_base_rule" = #{dataBaseRule,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP},
      "update_time" = #{updateTime,jdbcType=TIMESTAMP},
      "create_by" = #{createBy,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "data_base_url" = #{dataBaseUrl,jdbcType=VARCHAR},
      "update_by" = #{updateBy,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
</mapper>