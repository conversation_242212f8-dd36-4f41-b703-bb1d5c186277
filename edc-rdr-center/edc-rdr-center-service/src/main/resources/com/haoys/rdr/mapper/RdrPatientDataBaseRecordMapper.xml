<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientDataBaseRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientDataBaseRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="data_base_id" jdbcType="VARCHAR" property="dataBaseId" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, data_base_id, patient_sn
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rdr_patient_data_base_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rdr_patient_data_base_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from rdr_patient_data_base_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecordExample">
    delete from rdr_patient_data_base_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecord">
    insert into rdr_patient_data_base_record (id, data_base_id, patient_sn
      )
    values (#{id,jdbcType=VARCHAR}, #{dataBaseId,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecord">
    insert into rdr_patient_data_base_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dataBaseId != null">
        data_base_id,
      </if>
      <if test="patientSn != null">
        patient_sn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dataBaseId != null">
        #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecordExample" resultType="java.lang.Long">
    select count(*) from rdr_patient_data_base_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rdr_patient_data_base_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBaseId != null">
        data_base_id = #{record.dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        patient_sn = #{record.patientSn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rdr_patient_data_base_record
    set id = #{record.id,jdbcType=VARCHAR},
      data_base_id = #{record.dataBaseId,jdbcType=VARCHAR},
      patient_sn = #{record.patientSn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecord">
    update rdr_patient_data_base_record
    <set>
      <if test="dataBaseId != null">
        data_base_id = #{dataBaseId,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        patient_sn = #{patientSn,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientDataBaseRecord">
    update rdr_patient_data_base_record
    set data_base_id = #{dataBaseId,jdbcType=VARCHAR},
      patient_sn = #{patientSn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <!-- 修复SQL注入风险：使用参数化查询替代动态SQL拼接 -->
  <insert id="createDataBase" parameterType="com.haoys.rdr.dto.CreateDatabaseDto">
    INSERT INTO rdr_patient_data_base_record (
      id, data_base_id, patient_sn, create_time, create_user
    )
    SELECT
      #{recordId}, #{databaseId}, patient_sn, NOW(), #{createUser}
    FROM patient_info
    WHERE 1=1
    <if test="patientIds != null and patientIds.size() > 0">
      AND patient_sn IN
      <foreach collection="patientIds" item="patientId" open="(" close=")" separator=",">
        #{patientId}
      </foreach>
    </if>
    <if test="conditions != null and conditions.size() > 0">
      <foreach collection="conditions" item="condition">
        <if test="condition.field != null and condition.operator != null and condition.value != null">
          AND ${condition.field} ${condition.operator} #{condition.value}
        </if>
      </foreach>
    </if>
  </insert>

  <select id="getPatientsByDatabaseId" resultType="java.lang.String" useCache="true">
    select patient_sn from rdr_patient_data_base_record where data_base_id = #{databaseId}
  </select>

</mapper>
