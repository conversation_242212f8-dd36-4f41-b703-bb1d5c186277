<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientDataCenterMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientDataCenter">
    <result column="dataset_code" jdbcType="VARCHAR" property="datasetCode" />
    <result column="dataset_name" jdbcType="VARCHAR" property="datasetName" />
    <result column="year" jdbcType="VARCHAR" property="year" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="dataset_result" jdbcType="VARCHAR" property="datasetResult" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "dataset_code", "dataset_name", "year", "department", "visit_type", "dataset_result", 
    "sort", "expand", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientDataCenterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_data_center"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientDataCenterExample">
    delete from "public"."rdr_patient_data_center"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientDataCenter">
    insert into "public"."rdr_patient_data_center" ("dataset_code", "dataset_name", "year", 
      "department", "visit_type", "dataset_result", 
      "sort", "expand", "create_time"
      )
    values (#{datasetCode,jdbcType=VARCHAR}, #{datasetName,jdbcType=VARCHAR}, #{year,jdbcType=VARCHAR}, 
      #{department,jdbcType=VARCHAR}, #{visitType,jdbcType=VARCHAR}, #{datasetResult,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{expand,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientDataCenter">
    insert into "public"."rdr_patient_data_center"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="datasetCode != null">
        "dataset_code",
      </if>
      <if test="datasetName != null">
        "dataset_name",
      </if>
      <if test="year != null">
        "year",
      </if>
      <if test="department != null">
        "department",
      </if>
      <if test="visitType != null">
        "visit_type",
      </if>
      <if test="datasetResult != null">
        "dataset_result",
      </if>
      <if test="sort != null">
        "sort",
      </if>
      <if test="expand != null">
        "expand",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="datasetCode != null">
        #{datasetCode,jdbcType=VARCHAR},
      </if>
      <if test="datasetName != null">
        #{datasetName,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="datasetResult != null">
        #{datasetResult,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientDataCenterExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_data_center"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_data_center"
    <set>
      <if test="record.datasetCode != null">
        "dataset_code" = #{record.datasetCode,jdbcType=VARCHAR},
      </if>
      <if test="record.datasetName != null">
        "dataset_name" = #{record.datasetName,jdbcType=VARCHAR},
      </if>
      <if test="record.year != null">
        "year" = #{record.year,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        "department" = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.datasetResult != null">
        "dataset_result" = #{record.datasetResult,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        "sort" = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.expand != null">
        "expand" = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_data_center"
    set "dataset_code" = #{record.datasetCode,jdbcType=VARCHAR},
      "dataset_name" = #{record.datasetName,jdbcType=VARCHAR},
      "year" = #{record.year,jdbcType=VARCHAR},
      "department" = #{record.department,jdbcType=VARCHAR},
      "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      "dataset_result" = #{record.datasetResult,jdbcType=VARCHAR},
      "sort" = #{record.sort,jdbcType=INTEGER},
      "expand" = #{record.expand,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="getPatientResourceDataForDataSetCode" resultType="com.haoys.rdr.domain.bigscreen.RdrPatientDataCenterVo">
    select * from "public"."rdr_patient_data_center"
    where dataset_code similar to '${dataSetCode}%'
    <if test="year != null and year != ''">
      and year = #{year,jdbcType=VARCHAR}
    </if>
    <if test="department != null and department != ''">
      and department = #{department,jdbcType=VARCHAR}
    </if>
    <if test="visitType != null and visitType != ''">
      and visit_type = #{visitType,jdbcType=VARCHAR}
    </if>
  </select>

</mapper>