<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientExportFileMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientExportFile">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="export_status" jdbcType="VARCHAR" property="exportStatus" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "file_name", "file_type", "download_url", "export_status", "data_from", "status", 
    "operator", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientExportFileExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_export_file"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_export_file"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientExportFileExample">
    delete from "public"."rdr_patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientExportFile">
    insert into "public"."rdr_patient_export_file" ("id", "file_name", "file_type", 
      "download_url", "export_status", "data_from", 
      "status", "operator", "create_time"
      )
    values (#{id,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, 
      #{downloadUrl,jdbcType=VARCHAR}, #{exportStatus,jdbcType=VARCHAR}, #{dataFrom,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=DATE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientExportFile">
    insert into "public"."rdr_patient_export_file"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="fileName != null">
        "file_name",
      </if>
      <if test="fileType != null">
        "file_type",
      </if>
      <if test="downloadUrl != null">
        "download_url",
      </if>
      <if test="exportStatus != null">
        "export_status",
      </if>
      <if test="dataFrom != null">
        "data_from",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="operator != null">
        "operator",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        #{exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientExportFileExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_export_file"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        "file_name" = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        "file_type" = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadUrl != null">
        "download_url" = #{record.downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.exportStatus != null">
        "export_status" = #{record.exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        "data_from" = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        "operator" = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=DATE},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_export_file"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "file_name" = #{record.fileName,jdbcType=VARCHAR},
      "file_type" = #{record.fileType,jdbcType=VARCHAR},
      "download_url" = #{record.downloadUrl,jdbcType=VARCHAR},
      "export_status" = #{record.exportStatus,jdbcType=VARCHAR},
      "data_from" = #{record.dataFrom,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "operator" = #{record.operator,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=DATE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientExportFile">
    update "public"."rdr_patient_export_file"
    <set>
      <if test="fileName != null">
        "file_name" = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        "file_type" = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        "download_url" = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        "export_status" = #{exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        "data_from" = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        "operator" = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=DATE},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientExportFile">
    update "public"."rdr_patient_export_file"
    set "file_name" = #{fileName,jdbcType=VARCHAR},
      "file_type" = #{fileType,jdbcType=VARCHAR},
      "download_url" = #{downloadUrl,jdbcType=VARCHAR},
      "export_status" = #{exportStatus,jdbcType=VARCHAR},
      "data_from" = #{dataFrom,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "operator" = #{operator,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=DATE}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
</mapper>