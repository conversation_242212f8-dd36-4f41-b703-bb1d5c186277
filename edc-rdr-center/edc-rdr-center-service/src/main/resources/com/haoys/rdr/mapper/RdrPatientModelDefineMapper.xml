<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientModelDefineMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientModelDefine">
    <id column="model_source_id" jdbcType="VARCHAR" property="modelSourceId" />
    <result column="model_source_code" jdbcType="VARCHAR" property="modelSourceCode" />
    <result column="model_source_name" jdbcType="VARCHAR" property="modelSourceName" />
    <result column="custom_model" jdbcType="SMALLINT" property="customModel" />
    <result column="abbreviate_code" jdbcType="VARCHAR" property="abbreviateCode" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="group_by" jdbcType="VARCHAR" property="groupBy" />
    <result column="owner_group" jdbcType="VARCHAR" property="ownerGroup" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "model_source_id", "model_source_code", "model_source_name", "custom_model", "abbreviate_code", 
    "sort", "expand", "create_time", "group_by", "owner_group"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientModelDefineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_define"
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_model_define"
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientModelDefineExample">
    delete from "public"."rdr_patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientModelDefine">
    insert into "public"."rdr_patient_model_define" ("model_source_id", "model_source_code", "model_source_name", 
      "custom_model", "abbreviate_code", "sort", 
      "expand", "create_time", "group_by", 
      "owner_group")
    values (#{modelSourceId,jdbcType=VARCHAR}, #{modelSourceCode,jdbcType=VARCHAR}, #{modelSourceName,jdbcType=VARCHAR}, 
      #{customModel,jdbcType=SMALLINT}, #{abbreviateCode,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{expand,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{groupBy,jdbcType=VARCHAR}, 
      #{ownerGroup,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientModelDefine">
    insert into "public"."rdr_patient_model_define"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelSourceId != null">
        "model_source_id",
      </if>
      <if test="modelSourceCode != null">
        "model_source_code",
      </if>
      <if test="modelSourceName != null">
        "model_source_name",
      </if>
      <if test="customModel != null">
        "custom_model",
      </if>
      <if test="abbreviateCode != null">
        "abbreviate_code",
      </if>
      <if test="sort != null">
        "sort",
      </if>
      <if test="expand != null">
        "expand",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
      <if test="groupBy != null">
        "group_by",
      </if>
      <if test="ownerGroup != null">
        "owner_group",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelSourceId != null">
        #{modelSourceId,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceCode != null">
        #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="customModel != null">
        #{customModel,jdbcType=SMALLINT},
      </if>
      <if test="abbreviateCode != null">
        #{abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBy != null">
        #{groupBy,jdbcType=VARCHAR},
      </if>
      <if test="ownerGroup != null">
        #{ownerGroup,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientModelDefineExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_model_define"
    <set>
      <if test="record.modelSourceId != null">
        "model_source_id" = #{record.modelSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceCode != null">
        "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceName != null">
        "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.customModel != null">
        "custom_model" = #{record.customModel,jdbcType=SMALLINT},
      </if>
      <if test="record.abbreviateCode != null">
        "abbreviate_code" = #{record.abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        "sort" = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.expand != null">
        "expand" = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groupBy != null">
        "group_by" = #{record.groupBy,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerGroup != null">
        "owner_group" = #{record.ownerGroup,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_model_define"
    set "model_source_id" = #{record.modelSourceId,jdbcType=VARCHAR},
      "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      "custom_model" = #{record.customModel,jdbcType=SMALLINT},
      "abbreviate_code" = #{record.abbreviateCode,jdbcType=VARCHAR},
      "sort" = #{record.sort,jdbcType=INTEGER},
      "expand" = #{record.expand,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      "group_by" = #{record.groupBy,jdbcType=VARCHAR},
      "owner_group" = #{record.ownerGroup,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientModelDefine">
    update "public"."rdr_patient_model_define"
    <set>
      <if test="modelSourceCode != null">
        "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="customModel != null">
        "custom_model" = #{customModel,jdbcType=SMALLINT},
      </if>
      <if test="abbreviateCode != null">
        "abbreviate_code" = #{abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        "sort" = #{sort,jdbcType=INTEGER},
      </if>
      <if test="expand != null">
        "expand" = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBy != null">
        "group_by" = #{groupBy,jdbcType=VARCHAR},
      </if>
      <if test="ownerGroup != null">
        "owner_group" = #{ownerGroup,jdbcType=VARCHAR},
      </if>
    </set>
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientModelDefine">
    update "public"."rdr_patient_model_define"
    set "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
      "custom_model" = #{customModel,jdbcType=SMALLINT},
      "abbreviate_code" = #{abbreviateCode,jdbcType=VARCHAR},
      "sort" = #{sort,jdbcType=INTEGER},
      "expand" = #{expand,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP},
      "group_by" = #{groupBy,jdbcType=VARCHAR},
      "owner_group" = #{ownerGroup,jdbcType=VARCHAR}
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </update>


  <!--查询指定数据库的表信息-->
  <select id="getRdrTableModelConfigList" resultType="com.haoys.rdr.domain.scheme.RdrTableModelVo">
    SELECT
      relname AS "tableName" ,
      CAST ( obj_description ( OID, 'pg_class' ) AS VARCHAR ) AS "tableComment"
    FROM
      pg_class C
    WHERE
        relname IN ( SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND POSITION ( '_2' IN tablename ) = 0 AND relname NOT LIKE '%rdr_%' )
  </select>

  <!--查询表字段信息-->
  <select id="getTableModelColumnBySourceModelCode" resultType="com.haoys.rdr.domain.scheme.RdrTableModelVo">
    SELECT C.relname "tableName",
    CAST (obj_description (C.OID, 'pg_class') AS VARCHAR) "tableComment",
    A.attname "columnName",
    D.description "columnComment",
    T.typname AS "dataType"
    <!--concat_ws ( '', T.typname, SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\(.*\)' ) ) AS dataType-->
    FROM
      pg_class C,
      pg_attribute A,
      pg_type T,
      pg_description D
      WHERE C.relname = #{tableName}
      AND A.attnum > 0
      AND A.attrelid = C.OID
      AND A.atttypid = T.OID
      AND D.objoid = A.attrelid
      AND D.objsubid = A.attnum
      AND C.relname IN ( SELECT tablename FROM pg_tables
    WHERE schemaname = 'public' AND POSITION ( '_2' IN tablename ) = 0 )
    <!--AND A.attname is not null or A.attname != ''-->
    ORDER BY
    C.relname,
    A.attnum;
  </select>

  <!--查询指定表单模型字段列表-->
  <select id="getPatientModelConfigByModelCode" resultType="com.haoys.rdr.model.RdrPatientModelVariable">
    select * from rdr_patient_model_variable where model_source_code = #{modelSourceCode} and rdr_patient_model_variable.default_query = true
  </select>

  <!--根据模型来源代码查询模型定义-->
  <select id="getPatientModelDefineByModelSourceCode" resultMap="BaseResultMap">
    select * from rdr_patient_model_define where model_source_code = #{modelSourceCode}
  </select>

  <select id="getTableModelColumnOrdinalPositionBySourceModelCode" resultType="com.haoys.rdr.domain.scheme.RdrTableModelVo">
    select "table_catalog", "table_name", "column_name", "ordinal_position" from information_schema.columns where table_schema='public' and table_name = #{modelSourceCode};
  </select>

  <select id="getPatientDefaultModelConfigList" resultType="com.haoys.rdr.model.RdrPatientModelVariable">
    select * from rdr_patient_model_variable where 1 = 1 and model_source_code != 'patients' and (default_query = true) order by create_time desc
  </select>

  <select id="getPatientModelSourceConfigList" resultMap="BaseResultMap">
    SELECT
      relname AS "model_source_code",
      CAST ( obj_description ( OID, 'pg_class' ) AS VARCHAR ) AS "model_source_name"
    FROM
      pg_class C
    WHERE
        relname IN ( SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND POSITION ( '_2' IN tablename ) = 0 AND relname not like '%rdr_%' )
  </select>

  <select id="getSourceModelVariableTreeConfigListForRdr" resultType="com.haoys.rdr.domain.vo.RdrPatientModelDefineVo">
    select
        model_source_code as "variableCode",
        model_source_name as "variableName",
        expand
    from rdr_patient_model_define
    where 1 = 1 and sort is not null
    <if test="showPatients != null and showPatients != '' and showPatients == 'patient'">
      and model_source_code is not null
    </if>
    <if test="showPatients == null or showPatients == ''">
      and model_source_code != 'patients'
    </if>
    order by rdr_patient_model_define.sort asc
  </select>

  <update id="updatePatientModelVariable">
    update rdr_patient_model_variable set default_query = false
    <if test="patientModelVariableList != null and patientModelVariableList.size() > 0">
      where variable_code  IN (
      <foreach item="patientModelVariable" index="index" collection="patientModelVariableList" open="" separator="," close="">
        #{patientModelVariable.variableCode}
      </foreach>
      )
    </if>
  </update>

  <select id="cleanPatientModelDefineConfig">
    ${patientModelDefineValue}
  </select>

  <select id="getRdrTableSourceModel" resultType="java.lang.String">
    select model_source_code from rdr_patient_model_define where model_source_code = #{tableName}
  </select>

  <select id="getModelSourceCodeListByModelSourceCode" resultType="com.haoys.rdr.domain.vo.RdrPatientModelDefineVo">
    SELECT
      max(rdr_patient_model_variable.id) "variableId",
      rdr_patient_model_variable.variable_code "variableCode",
      max(rdr_patient_model_variable.variable_type) "variableType",
      max(rdr_patient_model_variable.variable_name) "variableName",
      max(rdr_patient_model_define.owner_group) "ownerGroup",
      <!--CONCAT(rdr_patient_model_variable.variable_name,' (',patient_model_define.abbreviate_code,')') variableName,-->
      bool_and(rdr_patient_model_variable.default_query) "defaultQuery",
      max(rdr_patient_model_variable.sort) "sort"
    FROM
      rdr_patient_model_variable
    INNER JOIN rdr_patient_model_define ON rdr_patient_model_define.model_source_code = rdr_patient_model_variable.model_source_code
    WHERE 1 = 1
    <if test="modelSourceCode != null and modelSourceCode != ''">
      and rdr_patient_model_variable.model_source_code = #{modelSourceCode}
    </if>
     and rdr_patient_model_variable.default_query = true
    GROUP BY
    rdr_patient_model_variable.variable_code
    ORDER BY
    max(rdr_patient_model_variable.sort) ASC
  </select>

  <select id="getModelSourceCodeListByOwnerGroup" resultType="com.haoys.rdr.domain.vo.RdrPatientModelDefineGroup">
    SELECT
      rdr_patient_model_group.group_code "groupCode",
      rdr_patient_model_group.group_name "groupName",
      rdr_patient_model_define.model_source_code "modelSourceCode",
      rdr_patient_model_define.model_source_name "modelSourceName",
      rdr_patient_model_group.sort "sort"
    FROM
      rdr_patient_model_define
    INNER JOIN rdr_patient_model_group ON rdr_patient_model_group.group_code = rdr_patient_model_define.owner_group
    WHERE 1 = 1
    <if test="groupCode != null and groupCode != ''">
      and rdr_patient_model_group.group_code = #{groupCode}
    </if>
    ORDER BY rdr_patient_model_group.sort ASC
  </select>

</mapper>