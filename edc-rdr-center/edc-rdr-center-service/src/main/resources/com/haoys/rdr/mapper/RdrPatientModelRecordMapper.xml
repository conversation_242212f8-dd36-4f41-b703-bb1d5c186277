<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientModelRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientModelRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="model_source_code" jdbcType="VARCHAR" property="modelSourceCode" />
    <result column="model_source_name" jdbcType="VARCHAR" property="modelSourceName" />
    <result column="content" jdbcType="OTHER" property="content" typeHandler="com.haoys.user.config.JSONTypeHandlerForPostgre"/>
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "patient_id", "model_source_code", "model_source_name", "content", "create_user_id",
    "status", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientModelRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_model_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientModelRecordExample">
    delete from "public"."rdr_patient_model_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientModelRecord">
    insert into "public"."rdr_patient_model_record" ("id", "patient_id", "model_source_code",
    "model_source_name", "content", "create_user_id",
    "status", "create_time")
    values (#{id,jdbcType=VARCHAR}, #{patientId,jdbcType=VARCHAR}, #{modelSourceCode,jdbcType=VARCHAR},
    #{modelSourceName,jdbcType=VARCHAR},
    #{content,jdbcType=OTHER,typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
    #{createUserId,jdbcType=VARCHAR},
    #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientModelRecord">
    insert into "public"."rdr_patient_model_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="patientId != null">
        "patient_id",
      </if>
      <if test="modelSourceCode != null">
        "model_source_code",
      </if>
      <if test="modelSourceName != null">
        "model_source_name",
      </if>
      <if test="content != null">
        "content",
      </if>
      <if test="createUserId != null">
        "create_user_id",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="patientId != null">
        #{patientId,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceCode != null">
        #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=OTHER, typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientModelRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_model_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_model_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.patientId != null">
        "patient_id" = #{record.patientId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceCode != null">
        "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceName != null">
        "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        "content" = #{record.content,jdbcType=OTHER, typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
      </if>
      <if test="record.createUserId != null">
        "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_model_record"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "patient_id" = #{record.patientId,jdbcType=VARCHAR},
      "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      "content" = #{record.content,jdbcType=OTHER, typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
      "create_user_id" = #{record.createUserId,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientModelRecord">
    update "public"."rdr_patient_model_record"
    <set>
      <if test="patientId != null">
        "patient_id" = #{patientId,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceCode != null">
        "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        "content" = #{content,jdbcType=OTHER, typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
      </if>
      <if test="createUserId != null">
        "create_user_id" = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientModelRecord">
    update "public"."rdr_patient_model_record"
    set "patient_id" = #{patientId,jdbcType=VARCHAR},
    "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
    "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
    "content" = #{content,jdbcType=OTHER, typeHandler=com.haoys.user.config.JSONTypeHandlerForPostgre},
    "create_user_id" = #{createUserId,jdbcType=VARCHAR},
    "status" = #{status,jdbcType=VARCHAR},
    "create_time" = #{createTime,jdbcType=TIMESTAMP}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>

  <!--患者数据探索综合查询-->
  <select id="getPatientMedicalRecordForPage" resultType="com.haoys.rdr.domain.wrapper.PatientBaseWrapper">
    SELECT
        patient_id
    FROM
    (
      SELECT
        rdr_patient_model_record.patient_id
      FROM
        rdr_patient_model_record
      INNER JOIN patients on patients.patient_sn = rdr_patient_model_record.patient_id
      INNER JOIN inp_visit on inp_visit.patient_sn = patients.patient_sn
      WHERE 1 = 1
      <if test="searchType != null and searchType != '' and searchType == '1'.toString()">
        AND rdr_patient_model_record.content::json::text like '%${searchWord}%'
        <!--and rdr_patient_model_record.patient_id in(
          select patient_id from rdr_patient_model_record,json_each_text(json(content)) content
          where content.value similar to '%${searchWord}%' group by patient_id
        )-->
      </if>
      <if test="searchType != null and searchType != '' and searchType == '2'.toString()">
        AND rdr_patient_model_record.model_source_code = #{modelSourceCode}
        <if test="searchWord != null and searchWord != ''">
          and rdr_patient_model_record.patient_id in(
            select patient_id from rdr_patient_model_record, json_each_text(json(content)) content
            where content.value like '%${searchWord}%' group by patient_id
          )
        </if>
      </if>
      <if test="searchType != null and searchType != '' and searchType == '3'.toString()">
        AND ${executeSqlValue}
      </if>
      <if test="querySegment != null and querySegment != '' and querySegment == 'query_segment_1'">
        AND inp_visit.admission_date_time >= CURRENT_DATE - INTERVAL '1' YEAR
      </if>
      <if test="querySegment != null and querySegment != '' and querySegment == 'query_segment_3'">
        AND inp_visit.admission_date_time >= CURRENT_DATE - INTERVAL '3' YEAR
      </if>
      <if test="querySegment != null and querySegment != '' and querySegment == 'query_segment_5'">
        AND inp_visit.admission_date_time >= CURRENT_DATE - INTERVAL '5' YEAR
      </if>
      <if test="querySegment != null and querySegment != '' and querySegment == 'custom_date_query'">
        AND inp_visit.admission_date_time between #{startDate} and #{endDate}
      </if>
      <if test="dataBaseId != null and dataBaseId != ''">
        <!--AND patients.patient_sn in (select patient_sn from rdr_patient_data_base_record where data_base_id = #{dataBaseId})-->
        <choose>
          <when test="dataSetId != null and dataSetId != ''">
            AND patients.patient_sn in (select patient_sn from rdr_patient_analysis_record where data_base_id = #{dataBaseId} and batch_code = #{dataSetId})
          </when>
          <otherwise>
            AND patients.patient_sn in (select patient_sn from rdr_patient_data_base_record where data_base_id = #{dataBaseId})
          </otherwise>
        </choose>
      </if>
      <!--<if test="dataSetId != null and dataSetId != ''">
        AND patients.patient_sn in (select patient_sn from rdr_patient_analysis_record where data_base_id = #{dataBaseId}) and batch_code = #{dataSetId}
      </if>-->
      group by rdr_patient_model_record.patient_id
    ) t GROUP BY patient_id

  </select>

  <select id="getPatientModelVariableRecord" resultType="com.haoys.rdr.domain.vo.RdrPatientVariableRecordVo">
    select
      patient_id, model_source_code, content.key,content.value
    from
      rdr_patient_model_record, json_each_text(json(content)) content
    where patient_id = #{patientId}
    <if test="modelSourceCode != null and modelSourceCode != ''">
      and model_source_code = #{modelSourceCode}
    </if>
    <if test="variableCode != null and variableCode != ''">
      and key = #{variableCode}
    </if>
  </select>

  <select id="getPatientModelVariableRecordViewDetail" resultType="com.haoys.rdr.domain.vo.RdrPatientVariableRecordVo">
    select
        id, patient_id, model_source_code, content.key,content.value
    from
        rdr_patient_model_record, json_each_text(json(content)) content
    where model_source_code = #{modelSourceCode} and patient_id = #{patientId} and id = #{recordId}
  </select>

  <select id="getPatientVisitListForRdr" resultType="java.util.Map">
    select v.*,r.discharge_diagnosis from inp_visit v left join emr_discharge_record r on v.visit_sn=r.visit_sn where v.patient_sn=#{patientSn}
  </select>

</mapper>
