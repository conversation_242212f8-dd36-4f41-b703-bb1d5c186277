<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientModelVariableMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientModelVariable">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="model_source_code" jdbcType="VARCHAR" property="modelSourceCode" />
    <result column="variable_code" jdbcType="VARCHAR" property="variableCode" />
    <result column="variable_name" jdbcType="VARCHAR" property="variableName" />
    <result column="variable_type" jdbcType="VARCHAR" property="variableType" />
    <result column="combobox_data" jdbcType="VARCHAR" property="comboboxData" />
    <result column="dictionary_value" jdbcType="VARCHAR" property="dictionaryValue" />
    <result column="default_query" jdbcType="BIT" property="defaultQuery" />
    <result column="custom_variable" jdbcType="BIT" property="customVariable" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "model_source_code", "variable_code", "variable_name", "variable_type", "combobox_data", 
    "dictionary_value", "default_query", "custom_variable", "group_name", "sort", "description", 
    "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientModelVariableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_variable"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_model_variable"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_model_variable"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientModelVariableExample">
    delete from "public"."rdr_patient_model_variable"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientModelVariable">
    insert into "public"."rdr_patient_model_variable" ("id", "model_source_code", "variable_code", 
      "variable_name", "variable_type", "combobox_data", 
      "dictionary_value", "default_query", "custom_variable", 
      "group_name", "sort", "description", 
      "create_time")
    values (#{id,jdbcType=VARCHAR}, #{modelSourceCode,jdbcType=VARCHAR}, #{variableCode,jdbcType=VARCHAR}, 
      #{variableName,jdbcType=VARCHAR}, #{variableType,jdbcType=VARCHAR}, #{comboboxData,jdbcType=VARCHAR}, 
      #{dictionaryValue,jdbcType=VARCHAR}, #{defaultQuery,jdbcType=BIT}, #{customVariable,jdbcType=BIT}, 
      #{groupName,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientModelVariable">
    insert into "public"."rdr_patient_model_variable"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="modelSourceCode != null">
        "model_source_code",
      </if>
      <if test="variableCode != null">
        "variable_code",
      </if>
      <if test="variableName != null">
        "variable_name",
      </if>
      <if test="variableType != null">
        "variable_type",
      </if>
      <if test="comboboxData != null">
        "combobox_data",
      </if>
      <if test="dictionaryValue != null">
        "dictionary_value",
      </if>
      <if test="defaultQuery != null">
        "default_query",
      </if>
      <if test="customVariable != null">
        "custom_variable",
      </if>
      <if test="groupName != null">
        "group_name",
      </if>
      <if test="sort != null">
        "sort",
      </if>
      <if test="description != null">
        "description",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceCode != null">
        #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="variableCode != null">
        #{variableCode,jdbcType=VARCHAR},
      </if>
      <if test="variableName != null">
        #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="variableType != null">
        #{variableType,jdbcType=VARCHAR},
      </if>
      <if test="comboboxData != null">
        #{comboboxData,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryValue != null">
        #{dictionaryValue,jdbcType=VARCHAR},
      </if>
      <if test="defaultQuery != null">
        #{defaultQuery,jdbcType=BIT},
      </if>
      <if test="customVariable != null">
        #{customVariable,jdbcType=BIT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientModelVariableExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_model_variable"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_model_variable"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceCode != null">
        "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.variableCode != null">
        "variable_code" = #{record.variableCode,jdbcType=VARCHAR},
      </if>
      <if test="record.variableName != null">
        "variable_name" = #{record.variableName,jdbcType=VARCHAR},
      </if>
      <if test="record.variableType != null">
        "variable_type" = #{record.variableType,jdbcType=VARCHAR},
      </if>
      <if test="record.comboboxData != null">
        "combobox_data" = #{record.comboboxData,jdbcType=VARCHAR},
      </if>
      <if test="record.dictionaryValue != null">
        "dictionary_value" = #{record.dictionaryValue,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultQuery != null">
        "default_query" = #{record.defaultQuery,jdbcType=BIT},
      </if>
      <if test="record.customVariable != null">
        "custom_variable" = #{record.customVariable,jdbcType=BIT},
      </if>
      <if test="record.groupName != null">
        "group_name" = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        "sort" = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.description != null">
        "description" = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_model_variable"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      "variable_code" = #{record.variableCode,jdbcType=VARCHAR},
      "variable_name" = #{record.variableName,jdbcType=VARCHAR},
      "variable_type" = #{record.variableType,jdbcType=VARCHAR},
      "combobox_data" = #{record.comboboxData,jdbcType=VARCHAR},
      "dictionary_value" = #{record.dictionaryValue,jdbcType=VARCHAR},
      "default_query" = #{record.defaultQuery,jdbcType=BIT},
      "custom_variable" = #{record.customVariable,jdbcType=BIT},
      "group_name" = #{record.groupName,jdbcType=VARCHAR},
      "sort" = #{record.sort,jdbcType=INTEGER},
      "description" = #{record.description,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientModelVariable">
    update "public"."rdr_patient_model_variable"
    <set>
      <if test="modelSourceCode != null">
        "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="variableCode != null">
        "variable_code" = #{variableCode,jdbcType=VARCHAR},
      </if>
      <if test="variableName != null">
        "variable_name" = #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="variableType != null">
        "variable_type" = #{variableType,jdbcType=VARCHAR},
      </if>
      <if test="comboboxData != null">
        "combobox_data" = #{comboboxData,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryValue != null">
        "dictionary_value" = #{dictionaryValue,jdbcType=VARCHAR},
      </if>
      <if test="defaultQuery != null">
        "default_query" = #{defaultQuery,jdbcType=BIT},
      </if>
      <if test="customVariable != null">
        "custom_variable" = #{customVariable,jdbcType=BIT},
      </if>
      <if test="groupName != null">
        "group_name" = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        "sort" = #{sort,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        "description" = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientModelVariable">
    update "public"."rdr_patient_model_variable"
    set "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      "variable_code" = #{variableCode,jdbcType=VARCHAR},
      "variable_name" = #{variableName,jdbcType=VARCHAR},
      "variable_type" = #{variableType,jdbcType=VARCHAR},
      "combobox_data" = #{comboboxData,jdbcType=VARCHAR},
      "dictionary_value" = #{dictionaryValue,jdbcType=VARCHAR},
      "default_query" = #{defaultQuery,jdbcType=BIT},
      "custom_variable" = #{customVariable,jdbcType=BIT},
      "group_name" = #{groupName,jdbcType=VARCHAR},
      "sort" = #{sort,jdbcType=INTEGER},
      "description" = #{description,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>

  <select id="cleanPatientModelVariableConfig">
    ${patientModelVariableValue}
  </select>

</mapper>