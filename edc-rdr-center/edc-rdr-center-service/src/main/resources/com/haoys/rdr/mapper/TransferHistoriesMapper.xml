<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.TransferHistoriesMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.TransferHistories">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="transfer_type" jdbcType="VARCHAR" property="transferType" />
    <result column="transfered_to_dept" jdbcType="VARCHAR" property="transferedToDept" />
    <result column="transfered_to_ward" jdbcType="VARCHAR" property="transferedToWard" />
    <result column="transfered_to_bed" jdbcType="VARCHAR" property="transferedToBed" />
    <result column="transfered_end_time" jdbcType="TIMESTAMP" property="transferedEndTime" />
    <result column="transfered_from_dept" jdbcType="VARCHAR" property="transferedFromDept" />
    <result column="transfered_from_ward" jdbcType="VARCHAR" property="transferedFromWard" />
    <result column="transfered_from_bed" jdbcType="VARCHAR" property="transferedFromBed" />
    <result column="transfered_start_time" jdbcType="TIMESTAMP" property="transferedStartTime" />
    <result column="reason_of_transfer" jdbcType="VARCHAR" property="reasonOfTransfer" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "transfer_type", "transfered_to_dept",
    "transfered_to_ward", "transfered_to_bed", "transfered_end_time", "transfered_from_dept",
    "transfered_from_ward", "transfered_from_bed", "transfered_start_time", "reason_of_transfer",
    "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.TransferHistoriesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."transfer_histories"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."transfer_histories"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."transfer_histories"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.TransferHistoriesExample">
    delete from "public"."transfer_histories"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.TransferHistories">
    insert into "public"."transfer_histories" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "transfer_type", "transfered_to_dept",
      "transfered_to_ward", "transfered_to_bed", "transfered_end_time",
      "transfered_from_dept", "transfered_from_ward",
      "transfered_from_bed", "transfered_start_time",
      "reason_of_transfer", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{transferType,jdbcType=VARCHAR}, #{transferedToDept,jdbcType=VARCHAR},
      #{transferedToWard,jdbcType=VARCHAR}, #{transferedToBed,jdbcType=VARCHAR}, #{transferedEndTime,jdbcType=TIMESTAMP},
      #{transferedFromDept,jdbcType=VARCHAR}, #{transferedFromWard,jdbcType=VARCHAR},
      #{transferedFromBed,jdbcType=VARCHAR}, #{transferedStartTime,jdbcType=TIMESTAMP},
      #{reasonOfTransfer,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.TransferHistories">
    insert into "public"."transfer_histories"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="transferType != null">
        "transfer_type",
      </if>
      <if test="transferedToDept != null">
        "transfered_to_dept",
      </if>
      <if test="transferedToWard != null">
        "transfered_to_ward",
      </if>
      <if test="transferedToBed != null">
        "transfered_to_bed",
      </if>
      <if test="transferedEndTime != null">
        "transfered_end_time",
      </if>
      <if test="transferedFromDept != null">
        "transfered_from_dept",
      </if>
      <if test="transferedFromWard != null">
        "transfered_from_ward",
      </if>
      <if test="transferedFromBed != null">
        "transfered_from_bed",
      </if>
      <if test="transferedStartTime != null">
        "transfered_start_time",
      </if>
      <if test="reasonOfTransfer != null">
        "reason_of_transfer",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="transferType != null">
        #{transferType,jdbcType=VARCHAR},
      </if>
      <if test="transferedToDept != null">
        #{transferedToDept,jdbcType=VARCHAR},
      </if>
      <if test="transferedToWard != null">
        #{transferedToWard,jdbcType=VARCHAR},
      </if>
      <if test="transferedToBed != null">
        #{transferedToBed,jdbcType=VARCHAR},
      </if>
      <if test="transferedEndTime != null">
        #{transferedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transferedFromDept != null">
        #{transferedFromDept,jdbcType=VARCHAR},
      </if>
      <if test="transferedFromWard != null">
        #{transferedFromWard,jdbcType=VARCHAR},
      </if>
      <if test="transferedFromBed != null">
        #{transferedFromBed,jdbcType=VARCHAR},
      </if>
      <if test="transferedStartTime != null">
        #{transferedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonOfTransfer != null">
        #{reasonOfTransfer,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.TransferHistoriesExample" resultType="java.lang.Long">
    select count(*) from "public"."transfer_histories"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."transfer_histories"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.transferType != null">
        "transfer_type" = #{record.transferType,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedToDept != null">
        "transfered_to_dept" = #{record.transferedToDept,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedToWard != null">
        "transfered_to_ward" = #{record.transferedToWard,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedToBed != null">
        "transfered_to_bed" = #{record.transferedToBed,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedEndTime != null">
        "transfered_end_time" = #{record.transferedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.transferedFromDept != null">
        "transfered_from_dept" = #{record.transferedFromDept,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedFromWard != null">
        "transfered_from_ward" = #{record.transferedFromWard,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedFromBed != null">
        "transfered_from_bed" = #{record.transferedFromBed,jdbcType=VARCHAR},
      </if>
      <if test="record.transferedStartTime != null">
        "transfered_start_time" = #{record.transferedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reasonOfTransfer != null">
        "reason_of_transfer" = #{record.reasonOfTransfer,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."transfer_histories"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "transfer_type" = #{record.transferType,jdbcType=VARCHAR},
      "transfered_to_dept" = #{record.transferedToDept,jdbcType=VARCHAR},
      "transfered_to_ward" = #{record.transferedToWard,jdbcType=VARCHAR},
      "transfered_to_bed" = #{record.transferedToBed,jdbcType=VARCHAR},
      "transfered_end_time" = #{record.transferedEndTime,jdbcType=TIMESTAMP},
      "transfered_from_dept" = #{record.transferedFromDept,jdbcType=VARCHAR},
      "transfered_from_ward" = #{record.transferedFromWard,jdbcType=VARCHAR},
      "transfered_from_bed" = #{record.transferedFromBed,jdbcType=VARCHAR},
      "transfered_start_time" = #{record.transferedStartTime,jdbcType=TIMESTAMP},
      "reason_of_transfer" = #{record.reasonOfTransfer,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.TransferHistories">
    update "public"."transfer_histories"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="transferType != null">
        "transfer_type" = #{transferType,jdbcType=VARCHAR},
      </if>
      <if test="transferedToDept != null">
        "transfered_to_dept" = #{transferedToDept,jdbcType=VARCHAR},
      </if>
      <if test="transferedToWard != null">
        "transfered_to_ward" = #{transferedToWard,jdbcType=VARCHAR},
      </if>
      <if test="transferedToBed != null">
        "transfered_to_bed" = #{transferedToBed,jdbcType=VARCHAR},
      </if>
      <if test="transferedEndTime != null">
        "transfered_end_time" = #{transferedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transferedFromDept != null">
        "transfered_from_dept" = #{transferedFromDept,jdbcType=VARCHAR},
      </if>
      <if test="transferedFromWard != null">
        "transfered_from_ward" = #{transferedFromWard,jdbcType=VARCHAR},
      </if>
      <if test="transferedFromBed != null">
        "transfered_from_bed" = #{transferedFromBed,jdbcType=VARCHAR},
      </if>
      <if test="transferedStartTime != null">
        "transfered_start_time" = #{transferedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonOfTransfer != null">
        "reason_of_transfer" = #{reasonOfTransfer,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.TransferHistories">
    update "public"."transfer_histories"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "transfer_type" = #{transferType,jdbcType=VARCHAR},
      "transfered_to_dept" = #{transferedToDept,jdbcType=VARCHAR},
      "transfered_to_ward" = #{transferedToWard,jdbcType=VARCHAR},
      "transfered_to_bed" = #{transferedToBed,jdbcType=VARCHAR},
      "transfered_end_time" = #{transferedEndTime,jdbcType=TIMESTAMP},
      "transfered_from_dept" = #{transferedFromDept,jdbcType=VARCHAR},
      "transfered_from_ward" = #{transferedFromWard,jdbcType=VARCHAR},
      "transfered_from_bed" = #{transferedFromBed,jdbcType=VARCHAR},
      "transfered_start_time" = #{transferedStartTime,jdbcType=TIMESTAMP},
      "reason_of_transfer" = #{reasonOfTransfer,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>
