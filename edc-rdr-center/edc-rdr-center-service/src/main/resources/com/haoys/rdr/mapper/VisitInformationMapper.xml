<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.VisitInformationMapper">
  <!-- 缓存配置，缓存大小为4096000，缓存过期时间为10分钟 -->
  <cache eviction="FIFO" flushInterval="60000000" size="4096000" readOnly="true"/>

  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.VisitInformation">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_or_admission_datetime" jdbcType="TIMESTAMP" property="visitOrAdmissionDatetime" />
    <result column="visit_or_admission_dept" jdbcType="VARCHAR" property="visitOrAdmissionDept" />
    <result column="visit_or_attending_doctor" jdbcType="VARCHAR" property="visitOrAttendingDoctor" />
    <result column="visit_age" jdbcType="INTEGER" property="visitAge" />
    <result column="diagnosis_name" jdbcType="ARRAY" property="diagnosisName" />
    <result column="diagnosis_code" jdbcType="ARRAY" property="diagnosisCode" />
    <result column="admission_way" jdbcType="VARCHAR" property="admissionWay" />
    <result column="discharge_datetime" jdbcType="TIMESTAMP" property="dischargeDatetime" />
    <result column="discharge_dept" jdbcType="VARCHAR" property="dischargeDept" />
    <result column="discharge_way" jdbcType="VARCHAR" property="dischargeWay" />
    <result column="in_days" jdbcType="INTEGER" property="inDays" />
    <result column="admission_number" jdbcType="INTEGER" property="admissionNumber" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "patient_sn", "patient_sn_org", "visit_sn", "visit_type", "visit_or_admission_datetime",
    "visit_or_admission_dept", "visit_or_attending_doctor", "visit_age", "diagnosis_name",
    "diagnosis_code", "admission_way", "discharge_datetime", "discharge_dept", "discharge_way",
    "in_days", "admission_number", "source_id", "source_path", "data_state"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.VisitInformationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."visit_information"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."visit_information"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.VisitInformationExample">
    delete from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.VisitInformation">
    insert into "public"."visit_information" ("pk_id", "patient_sn", "patient_sn_org",
      "visit_sn", "visit_type", "visit_or_admission_datetime",
      "visit_or_admission_dept", "visit_or_attending_doctor",
      "visit_age", "diagnosis_name", "diagnosis_code",
      "admission_way", "discharge_datetime", "discharge_dept",
      "discharge_way", "in_days", "admission_number",
      "source_id", "source_path", "data_state"
      )
    values (#{pkId,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR},
      #{visitSn,jdbcType=VARCHAR}, #{visitType,jdbcType=VARCHAR}, #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      #{visitOrAdmissionDept,jdbcType=VARCHAR}, #{visitOrAttendingDoctor,jdbcType=VARCHAR},
      #{visitAge,jdbcType=INTEGER}, #{diagnosisName,jdbcType=ARRAY}, #{diagnosisCode,jdbcType=ARRAY},
      #{admissionWay,jdbcType=VARCHAR}, #{dischargeDatetime,jdbcType=TIMESTAMP}, #{dischargeDept,jdbcType=VARCHAR},
      #{dischargeWay,jdbcType=VARCHAR}, #{inDays,jdbcType=INTEGER}, #{admissionNumber,jdbcType=INTEGER},
      #{sourceId,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.VisitInformation">
    insert into "public"."visit_information"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="visitType != null">
        "visit_type",
      </if>
      <if test="visitOrAdmissionDatetime != null">
        "visit_or_admission_datetime",
      </if>
      <if test="visitOrAdmissionDept != null">
        "visit_or_admission_dept",
      </if>
      <if test="visitOrAttendingDoctor != null">
        "visit_or_attending_doctor",
      </if>
      <if test="visitAge != null">
        "visit_age",
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name",
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code",
      </if>
      <if test="admissionWay != null">
        "admission_way",
      </if>
      <if test="dischargeDatetime != null">
        "discharge_datetime",
      </if>
      <if test="dischargeDept != null">
        "discharge_dept",
      </if>
      <if test="dischargeWay != null">
        "discharge_way",
      </if>
      <if test="inDays != null">
        "in_days",
      </if>
      <if test="admissionNumber != null">
        "admission_number",
      </if>
      <if test="sourceId != null">
        "source_id",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAdmissionDatetime != null">
        #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitOrAdmissionDept != null">
        #{visitOrAdmissionDept,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAttendingDoctor != null">
        #{visitOrAttendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="visitAge != null">
        #{visitAge,jdbcType=INTEGER},
      </if>
      <if test="diagnosisName != null">
        #{diagnosisName,jdbcType=ARRAY},
      </if>
      <if test="diagnosisCode != null">
        #{diagnosisCode,jdbcType=ARRAY},
      </if>
      <if test="admissionWay != null">
        #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDatetime != null">
        #{dischargeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDept != null">
        #{dischargeDept,jdbcType=VARCHAR},
      </if>
      <if test="dischargeWay != null">
        #{dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="inDays != null">
        #{inDays,jdbcType=INTEGER},
      </if>
      <if test="admissionNumber != null">
        #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.VisitInformationExample" resultType="java.lang.Long">
    select count(*) from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."visit_information"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitOrAdmissionDatetime != null">
        "visit_or_admission_datetime" = #{record.visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.visitOrAdmissionDept != null">
        "visit_or_admission_dept" = #{record.visitOrAdmissionDept,jdbcType=VARCHAR},
      </if>
      <if test="record.visitOrAttendingDoctor != null">
        "visit_or_attending_doctor" = #{record.visitOrAttendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.visitAge != null">
        "visit_age" = #{record.visitAge,jdbcType=INTEGER},
      </if>
      <if test="record.diagnosisName != null">
        "diagnosis_name" = #{record.diagnosisName,jdbcType=ARRAY},
      </if>
      <if test="record.diagnosisCode != null">
        "diagnosis_code" = #{record.diagnosisCode,jdbcType=ARRAY},
      </if>
      <if test="record.admissionWay != null">
        "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDatetime != null">
        "discharge_datetime" = #{record.dischargeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dischargeDept != null">
        "discharge_dept" = #{record.dischargeDept,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeWay != null">
        "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="record.inDays != null">
        "in_days" = #{record.inDays,jdbcType=INTEGER},
      </if>
      <if test="record.admissionNumber != null">
        "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null">
        "source_id" = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."visit_information"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      "visit_or_admission_datetime" = #{record.visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      "visit_or_admission_dept" = #{record.visitOrAdmissionDept,jdbcType=VARCHAR},
      "visit_or_attending_doctor" = #{record.visitOrAttendingDoctor,jdbcType=VARCHAR},
      "visit_age" = #{record.visitAge,jdbcType=INTEGER},
      "diagnosis_name" = #{record.diagnosisName,jdbcType=ARRAY},
      "diagnosis_code" = #{record.diagnosisCode,jdbcType=ARRAY},
      "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      "discharge_datetime" = #{record.dischargeDatetime,jdbcType=TIMESTAMP},
      "discharge_dept" = #{record.dischargeDept,jdbcType=VARCHAR},
      "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      "in_days" = #{record.inDays,jdbcType=INTEGER},
      "admission_number" = #{record.admissionNumber,jdbcType=INTEGER},
      "source_id" = #{record.sourceId,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.VisitInformation">
    update "public"."visit_information"
    <set>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org" = #{patientSnOrg,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        "visit_type" = #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAdmissionDatetime != null">
        "visit_or_admission_datetime" = #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitOrAdmissionDept != null">
        "visit_or_admission_dept" = #{visitOrAdmissionDept,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAttendingDoctor != null">
        "visit_or_attending_doctor" = #{visitOrAttendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="visitAge != null">
        "visit_age" = #{visitAge,jdbcType=INTEGER},
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name" = #{diagnosisName,jdbcType=ARRAY},
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code" = #{diagnosisCode,jdbcType=ARRAY},
      </if>
      <if test="admissionWay != null">
        "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDatetime != null">
        "discharge_datetime" = #{dischargeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDept != null">
        "discharge_dept" = #{dischargeDept,jdbcType=VARCHAR},
      </if>
      <if test="dischargeWay != null">
        "discharge_way" = #{dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="inDays != null">
        "in_days" = #{inDays,jdbcType=INTEGER},
      </if>
      <if test="admissionNumber != null">
        "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        "source_id" = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.VisitInformation">
    update "public"."visit_information"
    set "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "patient_sn_org" = #{patientSnOrg,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "visit_type" = #{visitType,jdbcType=VARCHAR},
      "visit_or_admission_datetime" = #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      "visit_or_admission_dept" = #{visitOrAdmissionDept,jdbcType=VARCHAR},
      "visit_or_attending_doctor" = #{visitOrAttendingDoctor,jdbcType=VARCHAR},
      "visit_age" = #{visitAge,jdbcType=INTEGER},
      "diagnosis_name" = #{diagnosisName,jdbcType=ARRAY},
      "diagnosis_code" = #{diagnosisCode,jdbcType=ARRAY},
      "admission_way" = #{admissionWay,jdbcType=VARCHAR},
      "discharge_datetime" = #{dischargeDatetime,jdbcType=TIMESTAMP},
      "discharge_dept" = #{dischargeDept,jdbcType=VARCHAR},
      "discharge_way" = #{dischargeWay,jdbcType=VARCHAR},
      "in_days" = #{inDays,jdbcType=INTEGER},
      "admission_number" = #{admissionNumber,jdbcType=INTEGER},
      "source_id" = #{sourceId,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="countPatientVisitCount" resultType="com.haoys.rdr.domain.bigscreen.PatientVisitCount" useCache="true">
    select
      count(case when "totalCount" = 0 then null else 1 end) "totalCount",
      count(case when "outpVisitCount" = 0 then null else 1 end) "outpVisitCount",
      count(case when "emergencyCount" = 0 then null else 1 end) "emergencyCount",
      count(case when "inpVisitCount" = 0 then null else 1 end) "inpVisitCount"
    from (
      select
        count(visit_information.patient_sn) "totalCount",
        count(case when visit_information.visit_type = '门诊' THEN visit_information.patient_sn END ) AS "outpVisitCount",
        count(case when visit_information.visit_type = '急诊' THEN visit_information.patient_sn END ) AS "emergencyCount",
        count(case when visit_information.visit_type = '住院' THEN visit_information.patient_sn END ) AS "inpVisitCount"
      from visit_information
          <if test="databaseId != null and databaseId!=''">
            join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=visit_information.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
          </if>
      group by visit_information.patient_sn
    ) t
  </select>

  <select id="getPatientVisitCountByMonth" resultType="com.haoys.rdr.domain.bigscreen.PatientVisitCount" useCache="true">
    select
      to_char(visit_information.visit_or_admission_datetime, 'YYYY-MM') as "dateCode",
      count(visit_information.patient_sn) "totalCount",
      count(case when visit_information.visit_type = '门诊' THEN visit_information.patient_sn END ) AS "outpVisitCount",
      count(case when visit_information.visit_type = '急诊' THEN visit_information.patient_sn END ) AS "emergencyCount",
      count(case when visit_information.visit_type = '住院' THEN visit_information.patient_sn END ) AS "inpVisitCount"
    from visit_information
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=visit_information.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    where 1 = 1
    <if test="year != null and year != ''">
      and visit_information.visit_or_admission_datetime between '${year}-01-01 00:00:00' and '${year}-12-31 23:59:59'
    </if>
    <if test="department != null">
      and visit_information.visit_or_admission_dept = #{department,jdbcType=VARCHAR}
    </if>
    group by "dateCode" order by "dateCode"
  </select>

  <select id="countPatientGenderPercent" resultType="com.haoys.rdr.domain.bigscreen.PatientVisitCount$PatientGenderCount" useCache="true">
    select
      gender,count(gender) count
    from patients
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    WHERE 1 = 1 group by gender HAVING gender IS NOT NULL
  </select>

  <select id="countPatientVisitDepartment" resultType="com.haoys.rdr.domain.bigscreen.PatientMedicalDataView" useCache="true">
    select
      visit_information.visit_or_admission_dept "name",count(visit_information.patient_sn)
    from visit_information
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=visit_information.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    group by visit_information.visit_or_admission_dept
  </select>

  <select id="countPatientVisitAreaForProvinceLevel" resultType="com.haoys.rdr.domain.bigscreen.AreaDistributeDataView$RegionDataView">
    SELECT
      area.parent_code parentCode,
      area.adcode as code,
      area.name as name,
      COALESCE(t.num, 0)as value
    FROM
      rdr_patient_area_info area
    LEFT JOIN (
        SELECT
          registered_residence_province AS name,
              count (0) AS num
          FROM patients
          <if test="databaseId != null and databaseId!=''">
            join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
          </if>
          GROUP BY registered_residence_province
    ) t ON t.name = area.name
    where area.parent_code = #{code,jdbcType=VARCHAR}
  </select>

  <select id="countPatientVisitAreaForCityLevel" resultType="com.haoys.rdr.domain.bigscreen.AreaDistributeDataView$RegionDataView">
    SELECT
      area.parent_code parentCode,
      area.adcode as code,
      area.name as name,
      COALESCE(t.num, 0)as value
    FROM
      rdr_patient_area_info area
    LEFT JOIN ( SELECT registered_residence_city AS name, count(0) AS num FROM patients
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    GROUP BY registered_residence_city ) t ON t.name = area.name
    where area.parent_code = #{code,jdbcType=VARCHAR}
  </select>

  <select id="countPatientAgeDataView" resultType="com.haoys.rdr.domain.bigscreen.PatientMedicalDataView" useCache="true">
    SELECT
      NAME "name",
      COUNT(NAME) AS count
    FROM
    (
      SELECT
        CASE
          WHEN age >= 90 THEN '>=90'
          WHEN age BETWEEN 85 AND 89 THEN '85-89'
          WHEN age BETWEEN 80 AND 84 THEN '80-84'
          WHEN age BETWEEN 75 AND 79 THEN '75-79'
          WHEN age BETWEEN 70 AND 74 THEN '70-74'
          WHEN age BETWEEN 65 AND 69 THEN '65-69'
          WHEN age BETWEEN 60 AND 64 THEN '60-64'
          WHEN age BETWEEN 55 AND 59 THEN '55-59'
          WHEN age BETWEEN 50 AND 54 THEN '50-54'
          WHEN age BETWEEN 45 AND 49 THEN '45-49'
          WHEN age BETWEEN 40 AND 44 THEN '40-44'
          WHEN age BETWEEN 35 AND 39 THEN '35-39'
          WHEN age BETWEEN 30 AND 34 THEN '30-34'
          WHEN age BETWEEN 25 AND 29 THEN '25-29'
          WHEN age BETWEEN 20 AND 24 THEN '20-24'
          WHEN age BETWEEN 15 AND 19 THEN '15-19'
          WHEN age BETWEEN 10 AND 14 THEN '19-14'
          WHEN age BETWEEN 5 AND 9 THEN '5-9'
          WHEN age BETWEEN 0 AND 4 THEN '0-4'
        END AS NAME
      FROM
        (select date_part('year', age(CAST(max(date_of_birth) AS DATE))) AS age
          from patients
          <if test="databaseId != null and databaseId!=''">
            join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
          </if>
          left join visit_information on visit_information.patient_sn = patients.patient_sn
          where 1 = 1 and patients.gender = #{gender,jdbcType=VARCHAR}
          <if test="year != null and year != ''">
            and visit_information.visit_or_admission_datetime between '${year}-01-01 00:00:00' and '${year}-12-31 23:59:59'
          </if>
          <if test="department != null and department != ''">
            and visit_information.visit_or_admission_dept = #{department,jdbcType=VARCHAR}
          </if>
          <if test="visitType != null and visitType != ''">
            and visit_information.visit_type = #{visitType,jdbcType=VARCHAR}
          </if>
          and patients.date_of_birth is not null
          group by visit_information.patient_sn) t
        ) t GROUP BY name ORDER BY length(name),name
  </select>

  <select id="getVisitDepartmentList" resultType="com.haoys.rdr.domain.bigscreen.PatientVisitDepartment" useCache="true">
    select
    visit_information.visit_or_admission_dept "departmentCode",visit_information.visit_or_admission_dept "departmentName"
    from visit_information
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=visit_information.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    where visit_information.visit_or_admission_dept is not null  group by visit_information.visit_or_admission_dept
  </select>

</mapper>
