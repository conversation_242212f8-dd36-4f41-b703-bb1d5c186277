<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator.properties"/>
    <context id="MySqlContext" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 为模型生成序列化方法-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <commentGenerator type="com.haoys.rdr.CommentGenerator">
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.connectionURL}"
                        userId="${jdbc.userId}"
                        password="${jdbc.password}">
            <!--解决mysql驱动升级到8.0后不生成指定数据库代码的问题-->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.haoys.rdr.model"
                            targetProject="/Users/<USER>/source_code/edc-research-master/edc-rdr-center/edc-rdr-center-service/src/main/java"/>

        <sqlMapGenerator targetPackage="com.haoys.rdr.mapper"
                         targetProject="/Users/<USER>/source_code/edc-research-master/edc-rdr-center/edc-rdr-center-service/src/main/resources"/>

        <javaClientGenerator targetPackage="com.haoys.rdr.mapper" type="XMLMAPPER"
                             targetProject="/Users/<USER>/source_code/edc-research-master/edc-rdr-center/edc-rdr-center-service/src/main/java"/>

<!--        <table schema="public" tableName="mr_homepage" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_admission_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_first_course" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_first_course_after_oper" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_course_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_oper_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_discharge_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="visit_diag" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="transfer_histories" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="pathology_report" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="mr_homepage_diag" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="mr_homepage_oper" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="mr_homepage_fee" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="patients" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="inp_visit" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="inp_order" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="outp_register" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="outp_visit" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="outp_order" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_24h_in_out" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_death_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_emergency_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_outp_record" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="emr_transfer" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="lab_master" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="lab_result" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="lab_result_micro" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="exam_report" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="nursing_vital_signs" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="bcg_ppd_test" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="visit_information" delimitIdentifiers="true" delimitAllColumns="true"/>-->

<!--        <table schema="public" tableName="visit_information" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="rdr_patient_model_define" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="rdr_patient_model_group" delimitIdentifiers="true" delimitAllColumns="true"/>-->
<!--        <table schema="public" tableName="rdr_patient_area_info" delimitIdentifiers="true" delimitAllColumns="true"/>-->
        <table schema="public" tableName="rdr_patient_data_center" delimitIdentifiers="true" delimitAllColumns="true"/>
    </context>
</generatorConfiguration>
