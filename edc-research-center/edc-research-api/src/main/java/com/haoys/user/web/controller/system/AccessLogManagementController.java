package com.haoys.user.web.controller.system;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.service.SystemRequestRecordService;
import com.haoys.user.model.SystemRequestRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志管理控制器
 * 提供访问日志的查询、统计和监控功能
 * 使用双重验证机制：配置秘钥 + AccessToken
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Api(tags = "系统访问日志管理")
@RestController
@RequestMapping("/access-log")
@Slf4j
public class AccessLogManagementController {

    @Autowired
    private SecureTokenService secureTokenService;

    @Autowired
    private SystemRequestRecordService systemRequestRecordService;

    @Value("${request.access.log.management.secret-key:EDC-ACCESS-LOG-MANAGEMENT-SECRET-2025}")
    private String accessLogSecretKey;

    @Value("${request.access.log.management.auto-generate-token:false}")
    private boolean autoGenerateToken;

    @Value("${request.access.log.management.dev-app-id:}")
    private String devAppId;

    @Value("${request.access.log.management.dev-app-secret:}")
    private String devAppSecret;

    @Value("${request.access.log.management.dev-environment:dev}")
    private String devEnvironment;

    /**
     * 访问日志管理页面
     */
    @ApiOperation("访问日志管理页面")
    @GetMapping(value = "/management", produces = "text/html; charset=utf-8")
    public void accessLogManagementPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            // 验证AccessToken - 只有在访问管理页面时才验证
            if (!validateAccessToken(request)) {
                // 重定向到登录页面
                response.sendRedirect("/api/access-log-management/login.html");
                return;
            }

            response.setContentType("text/html; charset=UTF-8");

            // 尝试从classpath读取HTML文件
            ClassPathResource resource = new ClassPathResource("static/access-log-management/management.html");
            if (resource.exists()) {
                String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);

                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    response.getWriter().write(content);
                    return;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/static/access-log-management/management.html");
            log.info("尝试从文件系统读取HTML文件: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));
            
            if (Files.exists(htmlPath)) {
                String content = new String(Files.readAllBytes(htmlPath), StandardCharsets.UTF_8);
                
                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    response.getWriter().write(content);
                    return;
                }
            }

            // 如果文件不存在，返回错误页面
            response.getWriter().write(generateErrorPage("访问日志管理页面文件不存在"));

        } catch (IOException e) {
            log.error("读取访问日志管理页面失败", e);
            try {
                response.getWriter().write(generateErrorPage("读取页面文件失败: " + e.getMessage()));
            } catch (IOException ioException) {
                log.error("写入错误页面失败", ioException);
            }
        } catch (Exception e) {
            log.error("访问日志管理页面处理失败", e);
            try {
                response.getWriter().write(generateErrorPage("页面处理失败: " + e.getMessage()));
            } catch (IOException ioException) {
                log.error("写入错误页面失败", ioException);
            }
        }
    }

    /**
     * 获取登录配置信息
     */
    @ApiOperation("获取登录配置信息")
    @GetMapping("/auth/config")
    public CommonResult<Map<String, Object>> getAuthConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("autoGenerateToken", autoGenerateToken);

            // 只在启用自动生成令牌时返回开发环境配置
            if (autoGenerateToken) {
                config.put("devAppId", devAppId);
                config.put("devAppSecret", devAppSecret);
                config.put("devEnvironment", devEnvironment);
            }

            log.debug("获取登录配置信息成功，自动生成令牌: {}", autoGenerateToken);
            return CommonResult.success(config, "获取配置成功");

        } catch (Exception e) {
            log.error("获取登录配置信息失败", e);
            return CommonResult.failed("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 验证配置秘钥
     */
    @ApiOperation("验证配置秘钥")
    @PostMapping("/auth/verify-secret")
    public CommonResult<Object> verifySecretKey(@RequestBody Map<String, String> request) {
        try {
            String secretKey = request.get("secretKey");

            if (secretKey == null || secretKey.trim().isEmpty()) {
                return CommonResult.failed("秘钥不能为空");
            }

            if (!accessLogSecretKey.equals(secretKey.trim())) {
                log.warn("访问日志管理秘钥验证失败，输入的秘钥: {}", secretKey);
                return CommonResult.failed("秘钥验证失败");
            }

            log.info("访问日志管理秘钥验证成功");
            return CommonResult.success(null, "秘钥验证成功");

        } catch (Exception e) {
            log.error("验证配置秘钥失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证Token - 修复：使用Code和RefreshCode获取AccessToken，而不是直接验证AccessToken
     */
    @ApiOperation("验证Token")
    @PostMapping("/auth/verify-token")
    public CommonResult<Object> verifyToken(@RequestBody Map<String, String> request) {
        try {
            String code = request.get("code");
            String refreshCode = request.get("refreshCode");

            if (code == null || code.trim().isEmpty()) {
                return CommonResult.failed("Code不能为空");
            }

            if (refreshCode == null || refreshCode.trim().isEmpty()) {
                return CommonResult.failed("RefreshCode不能为空");
            }

            // 使用Code和RefreshCode获取AccessToken
            SecureTokenParam.GetAccessTokenParam tokenParam = new SecureTokenParam.GetAccessTokenParam();
            tokenParam.setCode(code.trim());
            tokenParam.setRefreshCode(refreshCode.trim());

            SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(tokenParam);
            if (tokenResponse == null || tokenResponse.getAccessToken() == null || tokenResponse.getAccessToken().trim().isEmpty()) {
                log.warn("访问日志管理Token验证失败，Code: {}, RefreshCode: {}", code, refreshCode);
                return CommonResult.failed("Token验证失败，无法获取AccessToken");
            }

            log.info("访问日志管理Token验证成功，Code: {}", code);
            return CommonResult.success(tokenResponse, "Token验证成功");

        } catch (Exception e) {
            log.error("验证Token失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 查询访问日志列表
     */
    @ApiOperation("查询访问日志列表")
    @GetMapping("/management/list")
    public CommonResult<Object> getAccessLogList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String requestUrl,
            @RequestParam(required = false) String requestMethod,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String requestIp,
            HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);
            if (userId != null && !userId.trim().isEmpty()) {
                params.put("userId", userId.trim());
            }
            if (userName != null && !userName.trim().isEmpty()) {
                params.put("userName", userName.trim());
            }
            if (requestUrl != null && !requestUrl.trim().isEmpty()) {
                params.put("requestUrl", requestUrl.trim());
            }
            if (requestMethod != null && !requestMethod.trim().isEmpty()) {
                params.put("requestMethod", requestMethod.trim());
            }
            if (status != null && !status.trim().isEmpty()) {
                params.put("status", status.trim());
            }
            if (startTime != null && !startTime.trim().isEmpty()) {
                params.put("startTime", startTime.trim());
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                params.put("endTime", endTime.trim());
            }
            if (requestIp != null && !requestIp.trim().isEmpty()) {
                params.put("requestIp", requestIp.trim());
            }

            // 调用服务查询数据
            Object result = queryAccessLogList(params);
            
            return CommonResult.success(result, "查询成功");
            
        } catch (Exception e) {
            log.error("查询访问日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取访问日志详情
     */
    @ApiOperation("获取访问日志详情")
    @GetMapping("/management/detail/{id}")
    public CommonResult<Object> getAccessLogDetail(@PathVariable String id, HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取详情
            Object result = getAccessLogDetailById(id);
            
            if (result == null) {
                return CommonResult.failed("日志记录不存在");
            }
            
            return CommonResult.success(result, "获取详情成功");
            
        } catch (Exception e) {
            log.error("获取访问日志详情失败", e);
            return CommonResult.failed("获取详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计数据
     */
    @ApiOperation("获取统计数据")
    @GetMapping("/management/statistics")
    public CommonResult<Object> getStatistics(HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取统计数据
            Object result = getAccessLogStatistics();
            
            return CommonResult.success(result, "获取统计数据成功");
            
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return CommonResult.failed("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控数据
     */
    @ApiOperation("获取监控数据")
    @GetMapping("/management/monitoring")
    public CommonResult<Object> getMonitoring(HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取监控数据
            Object result = getAccessLogMonitoring();
            
            return CommonResult.success(result, "获取监控数据成功");
            
        } catch (Exception e) {
            log.error("获取监控数据失败", e);
            return CommonResult.failed("获取监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证AccessToken（支持多种方式）
     */
    private boolean validateAccessToken(HttpServletRequest request) {
        try {
            String accessToken = null;

            // 1. 从请求头获取
            accessToken = request.getHeader("X-Access-Token");
            if (accessToken != null && !accessToken.trim().isEmpty()) {
                log.debug("从请求头获取AccessToken: {}", accessToken.substring(0, Math.min(20, accessToken.length())) + "...");
            }

            // 2. 从URL参数获取
            if (accessToken == null || accessToken.trim().isEmpty()) {
                accessToken = request.getParameter("accessToken");
                if (accessToken != null && !accessToken.trim().isEmpty()) {
                    log.debug("从URL参数获取AccessToken: {}", accessToken.substring(0, Math.min(20, accessToken.length())) + "...");
                }
            }

            // 3. 从Authorization头获取（Bearer token格式）
            if (accessToken == null || accessToken.trim().isEmpty()) {
                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    accessToken = authHeader.substring(7);
                    log.debug("从Authorization头获取AccessToken: {}", accessToken.substring(0, Math.min(20, accessToken.length())) + "...");
                }
            }

            if (accessToken == null || accessToken.trim().isEmpty()) {
                log.debug("未找到AccessToken");
                return false;
            }

            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken.trim());
            boolean isValid = response != null && Boolean.TRUE.equals(response.getValid());
            log.debug("AccessToken验证结果: {}", isValid);
            return isValid;
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return false;
        }
    }

    /**
     * 获取服务器信息
     */
    private Map<String, Object> getServerInfo(HttpServletRequest request) {
        try {
            Map<String, Object> serverInfo = new HashMap<>();
            
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();
            
            String requestUrl;
            if ((scheme.equals("http") && serverPort == 80) || (scheme.equals("https") && serverPort == 443)) {
                requestUrl = scheme + "://" + serverName + contextPath;
            } else {
                requestUrl = scheme + "://" + serverName + ":" + serverPort + contextPath;
            }
            
            serverInfo.put("requestUrl", requestUrl);
            
            return serverInfo;
        } catch (Exception e) {
            log.error("获取服务器信息失败", e);
            return null;
        }
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String errorMessage) {
        return String.format(
            "<!DOCTYPE html><html><head><title>错误</title></head><body>" +
            "<h1>页面加载失败</h1><p>%s</p>" +
            "<p><a href='javascript:history.back()'>返回上一页</a></p>" +
            "</body></html>",
            errorMessage
        );
    }

    // 以下方法需要根据实际的服务实现进行调用
    
    /**
     * 查询访问日志列表 - 实际数据库查询实现
     */
    private Object queryAccessLogList(Map<String, Object> params) {
        try {
            // 获取分页参数
            int pageNum = (Integer) params.getOrDefault("pageNum", 1);
            int pageSize = (Integer) params.getOrDefault("pageSize", 20);

            // 获取查询条件
            String userId = (String) params.get("userId");
            String userName = (String) params.get("userName");
            String requestUrl = (String) params.get("requestUrl");
            String requestMethod = (String) params.get("requestMethod");
            String status = (String) params.get("status");
            String startTimeStr = (String) params.get("startTime");
            String endTimeStr = (String) params.get("endTime");
            String requestIp = (String) params.get("requestIp");

            // 解析时间参数
            Date startTime = null;
            Date endTime = null;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat dateOnlyFormat = new SimpleDateFormat("yyyy-MM-dd");

            if (startTimeStr != null && !startTimeStr.trim().isEmpty()) {
                try {
                    if (startTimeStr.length() == 10) {
                        // 只有日期，设置为当天开始时间
                        startTime = dateOnlyFormat.parse(startTimeStr);
                    } else {
                        startTime = dateFormat.parse(startTimeStr);
                    }
                } catch (ParseException e) {
                    log.warn("解析开始时间失败: {}", startTimeStr, e);
                }
            }

            if (endTimeStr != null && !endTimeStr.trim().isEmpty()) {
                try {
                    if (endTimeStr.length() == 10) {
                        // 只有日期，设置为当天结束时间
                        Date endDate = dateOnlyFormat.parse(endTimeStr);
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(endDate);
                        calendar.set(Calendar.HOUR_OF_DAY, 23);
                        calendar.set(Calendar.MINUTE, 59);
                        calendar.set(Calendar.SECOND, 59);
                        endTime = calendar.getTime();
                    } else {
                        endTime = dateFormat.parse(endTimeStr);
                    }
                } catch (ParseException e) {
                    log.warn("解析结束时间失败: {}", endTimeStr, e);
                }
            }

            // 处理成功状态参数
            Boolean isSuccess = null;
            if (status != null && !status.trim().isEmpty()) {
                if ("1".equals(status) || "success".equalsIgnoreCase(status)) {
                    isSuccess = true;
                } else if ("0".equals(status) || "error".equalsIgnoreCase(status)) {
                    isSuccess = false;
                }
            }

            // 调用服务层进行分页查询
            Map<String, Object> result = systemRequestRecordService.getRecordsByPage(
                pageNum, pageSize, userName, requestUrl, startTime, endTime, isSuccess);

            // 如果有其他条件需要进一步过滤，可以在这里处理
            if (result != null && result.get("records") != null) {
                List<SystemRequestRecord> records = (List<SystemRequestRecord>) result.get("records");

                // 根据其他条件进行过滤
                if (userId != null && !userId.trim().isEmpty()) {
                    records = records.stream()
                        .filter(record -> record.getUserId() != null &&
                                userId.equals(record.getUserId().toString()))
                        .collect(java.util.stream.Collectors.toList());
                }

                if (requestMethod != null && !requestMethod.trim().isEmpty()) {
                    records = records.stream()
                        .filter(record -> record.getRequestMethod() != null &&
                                record.getRequestMethod().toLowerCase().contains(requestMethod.toLowerCase()))
                        .collect(java.util.stream.Collectors.toList());
                }

                if (requestIp != null && !requestIp.trim().isEmpty()) {
                    records = records.stream()
                        .filter(record -> record.getRequestIp() != null &&
                                record.getRequestIp().contains(requestIp))
                        .collect(java.util.stream.Collectors.toList());
                }

                result.put("records", records);
                result.put("size", records.size());
            }

            log.info("查询访问日志列表成功，页码: {}, 页大小: {}, 总记录数: {}",
                    pageNum, pageSize, result.get("total"));

            return result;

        } catch (Exception e) {
            log.error("查询访问日志列表失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("records", new java.util.ArrayList<>());
            errorResult.put("total", 0L);
            errorResult.put("pageNum", params.getOrDefault("pageNum", 1));
            errorResult.put("pageSize", params.getOrDefault("pageSize", 20));
            errorResult.put("pages", 0L);
            errorResult.put("size", 0);
            return errorResult;
        }
    }
    
    /**
     * 根据ID获取访问日志详情 - 实际数据库查询实现
     */
    private Object getAccessLogDetailById(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                log.warn("获取访问日志详情失败：ID为空");
                return null;
            }

            Long logId;
            try {
                logId = Long.parseLong(id.trim());
            } catch (NumberFormatException e) {
                log.warn("获取访问日志详情失败：ID格式错误 - {}", id);
                return null;
            }

            // 调用服务层获取详情
            SystemRequestRecord record = systemRequestRecordService.getRecordById(logId);

            if (record == null) {
                log.warn("获取访问日志详情失败：记录不存在 - ID: {}", logId);
                return null;
            }

            // 构建详情响应对象
            Map<String, Object> detail = new HashMap<>();

            // 基本信息
            detail.put("id", record.getId());
            detail.put("userId", record.getUserId());
            detail.put("userName", record.getUserName());
            detail.put("realName", record.getRealName());
            detail.put("sessionId", record.getSessionId());
            detail.put("traceId", record.getTraceId());

            // 请求信息
            detail.put("requestName", record.getRequestName());
            detail.put("requestMethod", record.getRequestMethod());
            detail.put("requestUrl", record.getRequestUrl());
            detail.put("methodName", record.getMethodName());
            detail.put("requestParam", record.getRequestParam());
            detail.put("requestStartTime", record.getRequestStartTime());
            detail.put("requestEndTime", record.getRequestEndTime());

            // 响应信息
            detail.put("responseResult", record.getResponseResult());
            detail.put("responseTime", record.getResponseTime());
            detail.put("responseSize", record.getResponseSize());
            detail.put("httpStatus", record.getHttpStatus());
            detail.put("isSuccess", record.getIsSuccess());

            // 网络信息
            detail.put("requestIp", record.getRequestIp());
            detail.put("location", record.getLocation());
            detail.put("userAgent", record.getUserAgent());
            detail.put("referer", record.getReferer());

            // 设备信息
            detail.put("browser", record.getBrowser());
            detail.put("operatingSystem", record.getOperatingSystem());
            detail.put("deviceType", record.getDeviceType());

            // 业务信息
            detail.put("businessType", record.getBusinessType());
            detail.put("operatorType", record.getOperatorType());
            detail.put("dataFrom", record.getDataFrom());
            detail.put("accessType", record.getAccessType());
            detail.put("projectRecordLog", record.getProjectRecordLog());

            // 异常信息
            detail.put("errorMessage", record.getErrorMessage());
            detail.put("exceptionType", record.getExceptionType());
            detail.put("exceptionStack", record.getExceptionStack());

            // 系统信息
            detail.put("status", record.getStatus());
            detail.put("createTime", record.getCreateTime());
            detail.put("updateTime", record.getUpdateTime());

            // 添加一些计算字段
            if (record.getRequestStartTime() != null && record.getRequestEndTime() != null) {
                long duration = record.getRequestEndTime().getTime() - record.getRequestStartTime().getTime();
                detail.put("requestDuration", duration);
            }

            // 格式化时间显示
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (record.getCreateTime() != null) {
                detail.put("createTimeFormatted", dateFormat.format(record.getCreateTime()));
            }
            if (record.getRequestStartTime() != null) {
                detail.put("requestStartTimeFormatted", dateFormat.format(record.getRequestStartTime()));
            }
            if (record.getRequestEndTime() != null) {
                detail.put("requestEndTimeFormatted", dateFormat.format(record.getRequestEndTime()));
            }

            log.info("获取访问日志详情成功，ID: {}, 用户: {}, URL: {}",
                    logId, record.getUserName(), record.getRequestUrl());

            return detail;

        } catch (Exception e) {
            log.error("获取访问日志详情失败，ID: {}", id, e);
            return null;
        }
    }
    
    /**
     * 获取访问日志统计数据 - 实际数据库查询实现
     */
    private Object getAccessLogStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取时间范围 - 默认最近24小时
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.HOUR_OF_DAY, -24);
            Date startTime = calendar.getTime();

            // 获取基础统计信息
            Map<String, Object> tableStats = systemRequestRecordService.getTableStatistics();
            if (tableStats != null) {
                statistics.putAll(tableStats);
            }

            // 获取实时统计数据（最近1小时）
            Map<String, Object> realTimeStats = systemRequestRecordService.getRealTimeStatistics();
            if (realTimeStats != null) {
                // 修复：添加响应时间统计到实时数据中
                try {
                    // 获取最近1小时的时间范围
                    Calendar cal = Calendar.getInstance();
                    Date endTime1Hour = cal.getTime();
                    cal.add(Calendar.HOUR_OF_DAY, -1);
                    Date startTime1Hour = cal.getTime();

                    // 查询最近1小时的请求记录 - 使用现有的分页查询方法
                    Map<String, Object> pageResult = systemRequestRecordService.getRecordsByPage(
                        1, 1000, null, null, startTime1Hour, endTime1Hour, null);

                    @SuppressWarnings("unchecked")
                    List<SystemRequestRecord> recentRecords = (List<SystemRequestRecord>) pageResult.get("records");

                    if (recentRecords != null && !recentRecords.isEmpty()) {
                        // 过滤有效的响应时间数据
                        List<Long> responseTimes = new ArrayList<>();
                        for (SystemRequestRecord record : recentRecords) {
                            if (record.getResponseTime() != null && record.getResponseTime() > 0) {
                                responseTimes.add(record.getResponseTime());
                            }
                        }

                        if (!responseTimes.isEmpty()) {
                            // 计算平均响应时间
                            long sum = 0;
                            for (Long time : responseTimes) {
                                sum += time;
                            }
                            double avgResponseTime = (double) sum / responseTimes.size();

                            // 计算最大和最小响应时间
                            long maxResponseTime = responseTimes.get(0);
                            long minResponseTime = responseTimes.get(0);
                            for (Long time : responseTimes) {
                                if (time > maxResponseTime) maxResponseTime = time;
                                if (time < minResponseTime) minResponseTime = time;
                            }

                            realTimeStats.put("avgResponseTime", Math.round(avgResponseTime * 100.0) / 100.0);
                            realTimeStats.put("maxResponseTime", maxResponseTime);
                            realTimeStats.put("minResponseTime", minResponseTime);

                            log.info("响应时间统计添加成功: avg={}, max={}, min={}, 样本数={}",
                                realTimeStats.get("avgResponseTime"), maxResponseTime, minResponseTime, responseTimes.size());
                        } else {
                            realTimeStats.put("avgResponseTime", 0);
                            realTimeStats.put("maxResponseTime", 0);
                            realTimeStats.put("minResponseTime", 0);
                            log.info("没有有效的响应时间数据，使用默认值");
                        }
                    } else {
                        realTimeStats.put("avgResponseTime", 0);
                        realTimeStats.put("maxResponseTime", 0);
                        realTimeStats.put("minResponseTime", 0);
                        log.info("没有查询到最近1小时的请求记录");
                    }
                } catch (Exception e) {
                    log.error("计算响应时间统计失败", e);
                    realTimeStats.put("avgResponseTime", 0);
                    realTimeStats.put("maxResponseTime", 0);
                    realTimeStats.put("minResponseTime", 0);
                }

                statistics.put("realTime", realTimeStats);
            }

            // 获取24小时访问量趋势
            List<Map<String, Object>> hourlyTrend = systemRequestRecordService.getAccessStatistics(startTime, endTime, "hour");
            statistics.put("hourlyTrend", hourlyTrend);
            statistics.put("trend24h", hourlyTrend); // 前端期望的字段名

            // 获取最近7天的日访问量趋势
            calendar.setTime(new Date());
            Date weekEndTime = calendar.getTime();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date weekStartTime = calendar.getTime();
            List<Map<String, Object>> dailyTrend = systemRequestRecordService.getAccessStatistics(weekStartTime, weekEndTime, "day");
            statistics.put("dailyTrend", dailyTrend);

            // 获取热点URL统计（最近24小时）
            List<Map<String, Object>> hotUrls = systemRequestRecordService.getHotUrlStatistics(startTime, endTime, 10);
            statistics.put("hotUrls", hotUrls);

            // 获取用户访问统计（最近24小时）
            List<Map<String, Object>> userStats = systemRequestRecordService.getUserStatistics(startTime, endTime, 10);
            statistics.put("topUsers", userStats);

            // 获取IP访问统计（最近24小时）
            List<Map<String, Object>> ipStats = systemRequestRecordService.getIpStatistics(startTime, endTime, 10);
            statistics.put("topIps", ipStats);

            // 获取响应时间分布（最近24小时）
            List<Map<String, Object>> responseTimeDistribution = systemRequestRecordService.getResponseTimeDistribution(startTime, endTime);
            statistics.put("responseTimeDistribution", responseTimeDistribution);

            // 获取设备类型分布（最近24小时）
            List<Map<String, Object>> deviceTypeDistribution = systemRequestRecordService.getDeviceTypeDistribution(startTime, endTime);
            statistics.put("deviceTypeDistribution", deviceTypeDistribution);

            // 获取浏览器分布（最近24小时）
            List<Map<String, Object>> browserDistribution = systemRequestRecordService.getBrowserDistribution(startTime, endTime);
            statistics.put("browserDistribution", browserDistribution);

            // 获取错误日志统计（最近24小时）
            List<SystemRequestRecord> errorRecords = systemRequestRecordService.getErrorRecords(startTime, endTime, 10);
            statistics.put("recentErrors", errorRecords);

            // 获取慢请求统计（最近24小时）
            List<SystemRequestRecord> slowRecords = systemRequestRecordService.getSlowRecords(1000L, startTime, endTime, 10);
            statistics.put("slowRequests", slowRecords);

            // 计算一些额外的统计指标
            if (tableStats != null) {
                Object totalRecords = tableStats.get("total_records");
                Object successRecords = tableStats.get("success_records");
                Object errorRecordsCount = tableStats.get("error_records");
                Object avgResponseTime = tableStats.get("avg_response_time");

                if (totalRecords != null && successRecords != null && errorRecordsCount != null) {
                    long total = ((Number) totalRecords).longValue();
                    long success = ((Number) successRecords).longValue();
                    long error = ((Number) errorRecordsCount).longValue();

                    // 成功率
                    double successRate = total > 0 ? (double) success / total * 100 : 0;
                    statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);

                    // 错误率
                    double errorRate = total > 0 ? (double) error / total * 100 : 0;
                    statistics.put("errorRate", Math.round(errorRate * 100.0) / 100.0);
                }

                if (avgResponseTime != null) {
                    double avgTime = ((Number) avgResponseTime).doubleValue();
                    statistics.put("avgResponseTime", Math.round(avgTime * 100.0) / 100.0);
                }
            }

            // 添加统计时间信息
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            statistics.put("statisticsTime", dateFormat.format(new Date()));
            statistics.put("statisticsTimeRange", dateFormat.format(startTime) + " ~ " + dateFormat.format(endTime));

            log.info("获取访问日志统计数据成功，统计时间范围: {} ~ {}",
                    dateFormat.format(startTime), dateFormat.format(endTime));

            return statistics;

        } catch (Exception e) {
            log.error("获取访问日志统计数据失败", e);
            // 返回默认的统计数据
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalRequests", 0L);
            defaultStats.put("successRequests", 0L);
            defaultStats.put("errorRequests", 0L);
            defaultStats.put("successRate", 0.0);
            defaultStats.put("errorRate", 0.0);
            defaultStats.put("avgResponseTime", 0.0);
            defaultStats.put("slowRequests", new java.util.ArrayList<>());
            defaultStats.put("recentErrors", new java.util.ArrayList<>());
            defaultStats.put("hotUrls", new java.util.ArrayList<>());
            defaultStats.put("topUsers", new java.util.ArrayList<>());
            defaultStats.put("topIps", new java.util.ArrayList<>());
            defaultStats.put("hourlyTrend", new java.util.ArrayList<>());
            defaultStats.put("trend24h", new java.util.ArrayList<>()); // 前端期望的字段名
            defaultStats.put("dailyTrend", new java.util.ArrayList<>());
            defaultStats.put("responseTimeDistribution", new java.util.ArrayList<>());
            defaultStats.put("deviceTypeDistribution", new java.util.ArrayList<>());
            defaultStats.put("browserDistribution", new java.util.ArrayList<>());
            defaultStats.put("statisticsTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            return defaultStats;
        }
    }
    
    /**
     * 获取访问日志监控数据 - 实际数据库查询实现
     */
    private Object getAccessLogMonitoring() {
        try {
            // 调用服务层获取系统监控数据
            Map<String, Object> monitoringData = systemRequestRecordService.getSystemMonitorData();

            if (monitoringData == null) {
                monitoringData = new HashMap<>();
            }

            // 添加监控时间戳
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            monitoringData.put("monitoringTime", dateFormat.format(new Date()));

            // 添加系统健康状态
            boolean isHealthy = true;
            StringBuilder healthMessage = new StringBuilder();

            // 检查错误率
            Object realTimeObj = monitoringData.get("realTime");
            if (realTimeObj instanceof Map) {
                Map<String, Object> realTime = (Map<String, Object>) realTimeObj;
                Object totalRequestsObj = realTime.get("totalRequests");
                Object errorRequestsObj = realTime.get("errorRequests");

                if (totalRequestsObj != null && errorRequestsObj != null) {
                    long totalRequests = ((Number) totalRequestsObj).longValue();
                    long errorRequests = ((Number) errorRequestsObj).longValue();

                    if (totalRequests > 0) {
                        double errorRate = (double) errorRequests / totalRequests * 100;
                        if (errorRate > 10) { // 错误率超过10%
                            isHealthy = false;
                            healthMessage.append("错误率过高: ").append(String.format("%.2f%%", errorRate)).append("; ");
                        }
                    }
                }
            }

            // 检查慢请求
            Object slowRequestsObj = monitoringData.get("slowRequests");
            if (slowRequestsObj instanceof List) {
                List<?> slowRequests = (List<?>) slowRequestsObj;
                if (slowRequests.size() > 5) { // 慢请求超过5个
                    isHealthy = false;
                    healthMessage.append("慢请求过多: ").append(slowRequests.size()).append("个; ");
                }
            }

            monitoringData.put("systemHealthy", isHealthy);
            monitoringData.put("healthMessage", isHealthy ? "系统运行正常" : healthMessage.toString());

            log.info("获取访问日志监控数据成功，系统健康状态: {}", isHealthy ? "正常" : "异常");

            return monitoringData;

        } catch (Exception e) {
            log.error("获取访问日志监控数据失败", e);
            Map<String, Object> errorMonitoring = new HashMap<>();
            errorMonitoring.put("systemHealthy", false);
            errorMonitoring.put("healthMessage", "监控数据获取失败: " + e.getMessage());
            errorMonitoring.put("monitoringTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            return errorMonitoring;
        }
    }

    /**
     * 手动触发统计数据更新
     */
    @ApiOperation("手动触发统计数据更新")
    @PostMapping("/management/refresh-statistics")
    public CommonResult<Map<String, Object>> refreshStatistics(HttpServletRequest request) {

        // 验证AccessToken
        if (!validateAccessToken(request)) {
            return CommonResult.failed("AccessToken验证失败");
        }

        try {
            log.info("开始手动触发统计数据更新");

            // 执行统计数据更新任务
            Map<String, Object> result = new HashMap<>();

            // 1. 清理过期数据
            int cleanedRecords = systemRequestRecordService.cleanExpiredRecords();
            result.put("cleanedRecords", cleanedRecords);

            // 2. 更新统计缓存
            systemRequestRecordService.refreshStatisticsCache();
            result.put("cacheRefreshed", true);

            // 3. 重新计算监控数据
            Map<String, Object> monitoring = systemRequestRecordService.getSystemMonitorData();
            result.put("monitoringData", monitoring);

            // 4. 获取最新统计数据
            Map<String, Object> statistics = systemRequestRecordService.getRealTimeStatistics();
            result.put("statisticsData", statistics);

            result.put("refreshTime", new Date());
            result.put("message", "统计数据更新成功");

            log.info("统计数据更新完成，清理记录数: {}", cleanedRecords);

            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("手动触发统计数据更新失败", e);
            return CommonResult.failed("统计数据更新失败: " + e.getMessage());
        }
    }
}
