package com.haoys.user.web.controller.system;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.PageResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.domain.param.monitor.SystemMonitorParam;
import com.haoys.user.domain.vo.monitor.SystemAccessLogVo;
import com.haoys.user.domain.vo.monitor.SystemAccessStatisticsVo;
import com.haoys.user.domain.vo.monitor.SystemOnlineUserVo;
import com.haoys.user.domain.vo.system.SystemExceptionLogVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.service.SystemMonitorService;
import com.haoys.user.util.SecureTokenUtil;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统监控控制器
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Api(tags = "系统监控管理")
@RestController
@RequestMapping("/monitor")
public class SystemMonitorController extends BaseController {

    @Autowired
    private SystemMonitorService systemMonitorService;

    @Autowired
    private SecureTokenUtil secureTokenUtil;

    @Autowired
    private SecureTokenService secureTokenService;

    @Value("${access.monitor.secret-key:EDC-SYSTEM-MONITOR-SECRET-2025}")
    private String systemMonitorSecretKey;

    /**
     * 验证配置秘钥（第一步验证）
     */
    @ApiOperation("验证配置秘钥")
    @PostMapping("/auth/verify-secret")
    public CommonResult<Object> verifySecretKey(@RequestBody Map<String, String> request) {
        try {
            String secretKey = request.get("secretKey");

            if (secretKey == null || secretKey.trim().isEmpty()) {
                return CommonResult.failed("秘钥不能为空");
            }

            if (!systemMonitorSecretKey.equals(secretKey.trim())) {
                log.warn("系统监控秘钥验证失败，输入的秘钥: {}", secretKey);
                return CommonResult.failed("秘钥验证失败");
            }

            log.info("系统监控秘钥验证成功");
            return CommonResult.success(null, "秘钥验证成功");

        } catch (Exception e) {
            log.error("验证配置秘钥失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }







    /**
     * 验证AccessToken接口（兼容原有的/monitor/validate-token路径）
     */
    @ApiOperation("验证AccessToken")
    @GetMapping("/validate-token")
    public CommonResult<Boolean> validateToken(@RequestParam String accessToken) {
        try {
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return CommonResult.failed("AccessToken不能为空");
            }

            boolean isValid = secureTokenUtil.isValidAccessToken(accessToken.trim());
            log.debug("AccessToken验证结果: {}", isValid);

            return CommonResult.success(isValid);
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取AccessToken接口（使用提供的参数）
     */
    @ApiOperation("获取AccessToken")
    @PostMapping("/auth/get-access-token")
    public CommonResult<Map<String, Object>> getAccessToken(@RequestBody Map<String, String> request) {
        try {
            String appId = request.get("appId");
            String appSecret = request.get("appSecret");
            String environment = request.get("environment");
            String extraInfo = request.get("extraInfo");
            String userId = request.get("userId");

            if (appId == null || appId.trim().isEmpty()) {
                return CommonResult.failed("appId不能为空");
            }

            if (appSecret == null || appSecret.trim().isEmpty()) {
                return CommonResult.failed("appSecret不能为空");
            }

            // 使用SecureTokenService生成Code，然后获取AccessToken
            try {
                // 第一步：生成Code
                SecureTokenParam.GenerateCodeParam codeParam = new SecureTokenParam.GenerateCodeParam();
                codeParam.setAppId(appId.trim());
                codeParam.setAppSecret(appSecret.trim());
                codeParam.setEnvironment(environment != null ? environment.trim() : "dev");
                codeParam.setExtraInfo(extraInfo != null ? extraInfo.trim() : "");
                codeParam.setUserId(userId != null ? userId.trim() : "system");

                SecureTokenVo.CodeResponse codeResponse = secureTokenService.generateCode(codeParam);
                if (codeResponse == null || codeResponse.getCode() == null) {
                    return CommonResult.failed("生成Code失败");
                }

                // 第二步：使用Code获取AccessToken
                SecureTokenParam.GetAccessTokenParam tokenParam = new SecureTokenParam.GetAccessTokenParam();
                tokenParam.setCode(codeResponse.getCode());
                tokenParam.setRefreshCode(codeResponse.getRefreshCode());

                SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(tokenParam);
                if (tokenResponse != null && tokenResponse.getAccessToken() != null) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("accessToken", tokenResponse.getAccessToken());
                    result.put("message", "AccessToken获取成功");
                    result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                    result.put("userId", tokenResponse.getUserId());
                    result.put("expireTime", tokenResponse.getExpireTime());
                    result.put("expiresIn", tokenResponse.getExpiresIn());

                    log.info("系统监控AccessToken获取成功");
                    return CommonResult.success(result);
                } else {
                    return CommonResult.failed("AccessToken获取失败");
                }
            } catch (Exception e) {
                log.error("AccessToken获取失败", e);
                return CommonResult.failed("AccessToken获取失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("获取AccessToken失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 验证AccessToken（支持多种获取方式）
     */
    private boolean validateAccessToken(HttpServletRequest request) {
        try {
            String accessToken = getCurrentAccessToken(request);
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }

            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken.trim());
            return response != null && Boolean.TRUE.equals(response.getValid());
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return false;
        }
    }

    /**
     * 获取当前AccessToken（支持多种方式）
     */
    private String getCurrentAccessToken(HttpServletRequest request) {
        String accessToken = null;

        // 1. 从请求头获取
        accessToken = request.getHeader("X-Access-Token");
        if (accessToken != null && !accessToken.trim().isEmpty()) {
            return accessToken.trim();
        }

        // 2. 从URL参数获取
        accessToken = request.getParameter("accessToken");
        if (accessToken != null && !accessToken.trim().isEmpty()) {
            return accessToken.trim();
        }

        // 3. 从URL参数token获取（兼容性）
        accessToken = request.getParameter("token");
        if (accessToken != null && !accessToken.trim().isEmpty()) {
            return accessToken.trim();
        }

        return null;
    }

    /**
     * 验证AccessToken（兼容旧版本）
     */
    private boolean validateAccessTokenLegacy(HttpServletRequest request) {
        String accessToken = request.getHeader("Authorization");
        if (accessToken == null) {
            accessToken = request.getParameter("accessToken");
        }

        if (accessToken != null && accessToken.startsWith("Bearer ")) {
            accessToken = accessToken.substring(7);
        }

        if (accessToken == null || accessToken.trim().isEmpty()) {
            log.warn("AccessToken为空");
            return false;
        }

        try {
            boolean isValid = secureTokenUtil.isValidAccessToken(accessToken);
            if (!isValid) {
                log.warn("AccessToken验证失败: {}", accessToken.substring(0, Math.min(10, accessToken.length())) + "...");
            }
            return isValid;
        } catch (Exception e) {
            log.error("AccessToken验证异常: {}", e.getMessage());
            return false;
        }
    }



    /**
     * 验证Code和RefreshCode并获取AccessToken（第二步验证）
     */
    @ApiOperation("验证Code和RefreshCode获取AccessToken")
    @PostMapping("/auth/verify-token")
    public CommonResult<SecureTokenVo.AccessTokenResponse> verifyToken(@RequestBody SecureTokenParam.GetAccessTokenParam param) {
        try {
            if (param.getCode() == null || param.getCode().trim().isEmpty()) {
                return CommonResult.failed("Code不能为空");
            }
            if (param.getRefreshCode() == null || param.getRefreshCode().trim().isEmpty()) {
                return CommonResult.failed("RefreshCode不能为空");
            }

            SecureTokenVo.AccessTokenResponse response = secureTokenService.getAccessToken(param);
            if (response != null && response.getAccessToken() != null) {
                log.info("系统监控AccessToken获取成功");
                return CommonResult.success(response, "AccessToken获取成功");
            } else {
                return CommonResult.failed("获取AccessToken失败");
            }
        } catch (Exception e) {
            log.error("验证Token失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取统计概览", notes = "获取系统访问统计概览信息")
    @Log(title = "获取统计概览", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/overview")
    public CommonResult<SystemAccessStatisticsVo.StatisticsOverview> getStatisticsOverview(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            SystemAccessStatisticsVo.StatisticsOverview overview = systemMonitorService.getStatisticsOverview();
            return CommonResult.success(overview);
        } catch (Exception e) {
            log.error("获取统计概览失败", e);
            return CommonResult.failed("获取统计概览失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询访问日志", notes = "分页查询系统访问日志")
    @Log(title = "查询访问日志", businessType = BusinessType.OTHER)
    @PostMapping("/access-logs/list")
    public CommonResult<PageResult<SystemAccessLogVo>> getAccessLogList(
            @Validated @RequestBody SystemMonitorParam.AccessLogQueryParam param,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            PageResult<SystemAccessLogVo> result = systemMonitorService.getAccessLogList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询访问日志失败", e);
            return CommonResult.failed("查询访问日志失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询访问日志(GET)", notes = "GET方式分页查询系统访问日志")
    @Log(title = "查询访问日志", businessType = BusinessType.OTHER)
    @GetMapping("/access-logs/list")
    public CommonResult<PageResult<SystemAccessLogVo>> getAccessLogListByGet(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("用户ID") @RequestParam(required = false) String userId,
            @ApiParam("请求URL") @RequestParam(required = false) String requestUrl,
            @ApiParam("请求方法") @RequestParam(required = false) String requestMethod,
            @ApiParam("状态码") @RequestParam(required = false) String statusCode,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemMonitorParam.AccessLogQueryParam param = new SystemMonitorParam.AccessLogQueryParam();
            param.setPageNum(pageNum);
            param.setPageSize(pageSize);
            param.setUserId(userId);
            param.setRequestUrl(requestUrl);
            param.setRequestMethod(requestMethod);
            param.setStatusCode(statusCode);

            // 处理时间参数
            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    param.setStartTime(DateUtil.getAutoParseDate(startTime));
                } catch (Exception e) {
                    log.warn("解析开始时间失败: {}", startTime);
                }
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    param.setEndTime(DateUtil.getAutoParseDate(endTime));
                } catch (Exception e) {
                    log.warn("解析结束时间失败: {}", endTime);
                }
            }

            PageResult<SystemAccessLogVo> result = systemMonitorService.getAccessLogList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询访问日志失败", e);
            return CommonResult.failed("查询访问日志失败: " + e.getMessage());
        }
    }



    @ApiOperation(value = "分页查询在线用户", notes = "分页查询当前在线用户")
    @Log(title = "查询在线用户", businessType = BusinessType.OTHER)
    @PostMapping("/online-users/list")
    public CommonResult<PageResult<SystemOnlineUserVo>> getOnlineUserList(
            @Validated @RequestBody SystemMonitorParam.OnlineUserQueryParam param,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            PageResult<SystemOnlineUserVo> result = systemMonitorService.getOnlineUserList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询在线用户失败", e);
            return CommonResult.failed("查询在线用户失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询在线用户(GET)", notes = "GET方式分页查询当前在线用户")
    @Log(title = "查询在线用户", businessType = BusinessType.OTHER)
    @GetMapping("/online-users/list")
    public CommonResult<PageResult<SystemOnlineUserVo>> getOnlineUserListByGet(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String ipAddress,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemMonitorParam.OnlineUserQueryParam param = new SystemMonitorParam.OnlineUserQueryParam();
            param.setPageNum(pageNum);
            param.setPageSize(pageSize);
            param.setUserId(userId);
            param.setUserName(username);
            param.setStatus(status);
            param.setLoginIp(ipAddress);

            PageResult<SystemOnlineUserVo> result = systemMonitorService.getOnlineUserList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询在线用户失败", e);
            return CommonResult.failed("查询在线用户失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询异常日志", notes = "分页查询系统异常日志")
    @Log(title = "查询异常日志", businessType = BusinessType.OTHER)
    @PostMapping("/exception-logs/list")
    public CommonResult<PageResult<SystemExceptionLogVo>> getExceptionLogList(
            @Validated @RequestBody SystemMonitorParam.ExceptionLogQueryParam param,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            PageResult<SystemExceptionLogVo> result = systemMonitorService.getExceptionLogList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询异常日志失败", e);
            return CommonResult.failed("查询异常日志失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询异常日志(GET)", notes = "GET方式分页查询系统异常日志")
    @Log(title = "查询异常日志", businessType = BusinessType.OTHER)
    @GetMapping("/exception-logs/list")
    public CommonResult<PageResult<SystemExceptionLogVo>> getExceptionLogListByGet(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String exceptionType,
            @RequestParam(required = false) String exceptionMessage,
            @RequestParam(required = false) String logLevel,
            @RequestParam(required = false) String requestUrl,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemMonitorParam.ExceptionLogQueryParam param = new SystemMonitorParam.ExceptionLogQueryParam();
            param.setPageNum(pageNum);
            param.setPageSize(pageSize);
            param.setExceptionType(exceptionType);
            param.setExceptionMessage(exceptionMessage);
            param.setLogLevel(logLevel);
            param.setRequestUrl(requestUrl);

            PageResult<SystemExceptionLogVo> result = systemMonitorService.getExceptionLogList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询异常日志失败", e);
            return CommonResult.failed("查询异常日志失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取访问日志详情", notes = "根据ID获取访问日志详细信息")
    @Log(title = "查看访问日志详情", businessType = BusinessType.OTHER)
    @GetMapping("/access-logs/detail/{id}")
    public CommonResult<SystemAccessLogVo> getAccessLogDetail(
            @ApiParam("日志ID") @PathVariable String id,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemAccessLogVo detail = systemMonitorService.getAccessLogDetail(id);
            if (detail == null) {
                return CommonResult.failed("访问日志不存在");
            }
            return CommonResult.success(detail);
        } catch (Exception e) {
            log.error("获取访问日志详情失败", e);
            return CommonResult.failed("获取访问日志详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取在线用户详情", notes = "根据会话ID获取在线用户详细信息")
    @Log(title = "查看在线用户详情", businessType = BusinessType.OTHER)
    @GetMapping("/online-users/detail/{sessionId}")
    public CommonResult<SystemOnlineUserVo> getOnlineUserDetail(
            @ApiParam("会话ID") @PathVariable String sessionId,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemOnlineUserVo detail = systemMonitorService.getOnlineUserDetail(sessionId);
            if (detail == null) {
                return CommonResult.failed("在线用户不存在");
            }
            return CommonResult.success(detail);
        } catch (Exception e) {
            log.error("获取在线用户详情失败", e);
            return CommonResult.failed("获取在线用户详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取异常日志详情", notes = "根据ID获取异常日志详细信息")
    @Log(title = "查看异常日志详情", businessType = BusinessType.OTHER)
    @GetMapping("/exception-logs/detail/{id}")
    public CommonResult<SystemExceptionLogVo> getExceptionLogDetail(
            @ApiParam("日志ID") @PathVariable String id,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            SystemExceptionLogVo detail = systemMonitorService.getExceptionLogDetail(id);
            if (detail == null) {
                return CommonResult.failed("异常日志不存在");
            }
            return CommonResult.success(detail);
        } catch (Exception e) {
            log.error("获取异常日志详情失败", e);
            return CommonResult.failed("获取异常日志详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询访问统计", notes = "分页查询系统访问统计数据")
    @Log(title = "查询访问统计", businessType = BusinessType.OTHER)
    @PostMapping("/statistics/list")
    public CommonResult<PageResult<SystemAccessStatisticsVo>> getAccessStatisticsList(
            @Validated @RequestBody SystemMonitorParam.StatisticsQueryParam param,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            PageResult<SystemAccessStatisticsVo> result = systemMonitorService.getAccessStatisticsList(param);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("查询访问统计失败", e);
            return CommonResult.failed("查询访问统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取实时统计", notes = "获取系统实时统计数据")
    @Log(title = "获取实时统计", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/realtime")
    public CommonResult<Map<String, Object>> getRealTimeStatistics(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            Map<String, Object> result = systemMonitorService.getRealTimeStatistics();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取实时统计失败", e);
            return CommonResult.failed("获取实时统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取访问趋势", notes = "获取指定天数的访问趋势数据")
    @Log(title = "获取访问趋势", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/trend")
    public CommonResult<List<Map<String, Object>>> getAccessTrend(
            @ApiParam(value = "天数", example = "7") @RequestParam(defaultValue = "7") int days,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            List<Map<String, Object>> result = systemMonitorService.getAccessTrend(days);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取访问趋势失败", e);
            return CommonResult.failed("获取访问趋势失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取热门页面", notes = "获取访问量最高的页面")
    @Log(title = "获取热门页面", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/popular-pages")
    public CommonResult<List<Map<String, Object>>> getPopularPages(
            @ApiParam(value = "限制数量", example = "10") @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            List<Map<String, Object>> result = systemMonitorService.getPopularPages(limit);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取热门页面失败", e);
            return CommonResult.failed("获取热门页面失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取地域分布", notes = "获取访问地域分布数据")
    @Log(title = "获取地域分布", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/location-distribution")
    public CommonResult<List<Map<String, Object>>> getLocationDistribution(
            @ApiParam(value = "限制数量", example = "10") @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            List<Map<String, Object>> result = systemMonitorService.getLocationDistribution(limit);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取地域分布失败", e);
            return CommonResult.failed("获取地域分布失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "强制下线用户", notes = "强制指定用户下线")
    @Log(title = "强制下线用户", businessType = BusinessType.OTHER)
    @PostMapping("/online-user/force-offline")
    public CommonResult<Boolean> forceOfflineUser(
            @ApiParam(value = "会话ID") @RequestParam String sessionId,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            boolean result = systemMonitorService.forceOfflineUser(sessionId);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("强制下线用户失败", e);
            return CommonResult.failed("强制下线用户失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "批量强制下线用户", notes = "批量强制指定用户下线")
    @Log(title = "批量强制下线用户", businessType = BusinessType.OTHER)
    @PostMapping("/online-user/batch-force-offline")
    public CommonResult<Integer> batchForceOfflineUsers(
            @ApiParam(value = "会话ID列表") @RequestBody List<String> sessionIds,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            int result = systemMonitorService.batchForceOfflineUsers(sessionIds);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("批量强制下线用户失败", e);
            return CommonResult.failed("批量强制下线用户失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "生成每日统计", notes = "手动生成指定日期的统计数据")
    @Log(title = "生成每日统计", businessType = BusinessType.OTHER)
    @PostMapping("/statistics/generate-daily")
    public CommonResult<Boolean> generateDailyStatistics(
            @ApiParam(value = "统计日期(yyyy-MM-dd)", example = "2025-01-15") @RequestParam String statDate,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            boolean result = systemMonitorService.generateDailyStatistics(statDate);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("生成每日统计失败", e);
            return CommonResult.failed("生成每日统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "清理过期在线用户", notes = "清理超时的在线用户记录")
    @Log(title = "清理过期在线用户", businessType = BusinessType.OTHER)
    @PostMapping("/online-user/clean-expired")
    public CommonResult<Integer> cleanExpiredOnlineUsers(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }
        
        try {
            int result = systemMonitorService.cleanExpiredOnlineUsers();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("清理过期在线用户失败", e);
            return CommonResult.failed("清理过期在线用户失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取登录统计信息", notes = "获取指定时间范围内的登录统计信息")
    @Log(title = "获取登录统计信息", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/login")
    public CommonResult<Map<String, Object>> getLoginStatistics(
            @ApiParam(value = "开始日期", example = "2025-01-01") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期", example = "2025-01-07") @RequestParam(required = false) String endDate,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            // 默认查询最近7天
            if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                Date now = new Date();
                endDate = DateUtil.formatDate(now.getTime());
                startDate = DateUtil.formatDate(now.getTime() - 6 * 24 * 60 * 60 * 1000L);
            }

            Map<String, Object> result = systemMonitorService.getLoginStatistics(startDate, endDate);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取登录统计信息失败", e);
            return CommonResult.failed("获取登录统计信息失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取登录类型统计", notes = "获取按登录类型分组的统计数据")
    @Log(title = "获取登录类型统计", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/login-type")
    public CommonResult<List<Map<String, Object>>> getLoginTypeStatistics(
            @ApiParam(value = "开始日期", example = "2025-01-01") @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期", example = "2025-01-07") @RequestParam(required = false) String endDate,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            // 默认查询最近7天
            if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                Date now = new Date();
                endDate = DateUtil.formatDate(now.getTime());
                startDate = DateUtil.formatDate(now.getTime() - 6 * 24 * 60 * 60 * 1000L);
            }

            List<Map<String, Object>> result = systemMonitorService.getLoginTypeStatistics(startDate, endDate);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取登录类型统计失败", e);
            return CommonResult.failed("获取登录类型统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取今日登录统计", notes = "获取今日登录统计信息")
    @Log(title = "获取今日登录统计", businessType = BusinessType.OTHER)
    @GetMapping("/statistics/today-login")
    public CommonResult<Map<String, Object>> getTodayLoginStatistics(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            Map<String, Object> result = systemMonitorService.getTodayLoginStatistics();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取今日登录统计失败", e);
            return CommonResult.failed("获取今日登录统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取用户登录历史", notes = "获取指定用户的登录历史记录")
    @Log(title = "获取用户登录历史", businessType = BusinessType.OTHER)
    @GetMapping("/user/login-history")
    public CommonResult<List<Map<String, Object>>> getUserLoginHistory(
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "限制数量", example = "10") @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            List<Map<String, Object>> result = systemMonitorService.getUserLoginHistory(userId, limit);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取用户登录历史失败", e);
            return CommonResult.failed("获取用户登录历史失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取在线用户统计", notes = "获取在线用户统计信息")
    @Log(title = "获取在线用户统计", businessType = BusinessType.OTHER)
    @GetMapping("/online-users/statistics")
    public CommonResult<Map<String, Object>> getOnlineUserStatistics(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            Map<String, Object> result = systemMonitorService.getOnlineUserStatistics();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取在线用户统计失败", e);
            return CommonResult.failed("获取在线用户统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取异常日志统计", notes = "获取异常日志统计信息")
    @Log(title = "获取异常日志统计", businessType = BusinessType.OTHER)
    @GetMapping("/exception-logs/statistics")
    public CommonResult<Map<String, Object>> getExceptionLogStatistics(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            Map<String, Object> result = systemMonitorService.getExceptionLogStatistics();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取异常日志统计失败", e);
            return CommonResult.failed("获取异常日志统计失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "强制刷新统计数据", notes = "强制刷新系统监控统计数据")
    @Log(title = "强制刷新统计数据", businessType = BusinessType.OTHER)
    @PostMapping("/statistics/refresh")
    public CommonResult<Map<String, Object>> refreshStatistics(HttpServletRequest request) {
        if (!validateAccessToken(request)) {
            return CommonResult.failed("访问令牌无效或已过期");
        }

        try {
            Map<String, Object> result = new HashMap<>();

            // 强制生成今日统计
            String today = DateUtil.formatDate(System.currentTimeMillis());
            boolean dailyGenerated = systemMonitorService.generateDailyStatistics(today);

            // 清理过期在线用户
            int cleanedUsers = systemMonitorService.cleanExpiredOnlineUsers();

            // 获取最新统计概览
            SystemAccessStatisticsVo.StatisticsOverview overview = systemMonitorService.getStatisticsOverview();

            result.put("dailyStatisticsGenerated", dailyGenerated);
            result.put("cleanedExpiredUsers", cleanedUsers);
            result.put("refreshTime", new Date());
            result.put("statisticsOverview", overview);

            log.info("强制刷新统计数据完成: 清理过期用户{}个, 生成今日统计:{}", cleanedUsers, dailyGenerated);
            return CommonResult.success(result, "统计数据刷新完成");
        } catch (Exception e) {
            log.error("强制刷新统计数据失败", e);
            return CommonResult.failed("强制刷新统计数据失败: " + e.getMessage());
        }
    }


}
