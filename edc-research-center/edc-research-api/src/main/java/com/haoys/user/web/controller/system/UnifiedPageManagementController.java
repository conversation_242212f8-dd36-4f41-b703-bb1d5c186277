package com.haoys.user.web.controller.system;

import com.haoys.user.config.PlatformOssConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一管理门户控制器
 * 提供一个美观的管理门户页面，展示所有管理模块的卡片入口
 *
 * <p>支持的管理功能：</p>
 * <ul>
 *   <li>访问日志管理</li>
 *   <li>监控日志管理</li>
 *   <li>Redis数据管理</li>
 *   <li>集群监控管理</li>
 *   <li>定时任务管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Controller
@Api(tags = "统一管理门户")
@RequestMapping("/unified-management-portal")
public class UnifiedPageManagementController {

    @Autowired
    private PlatformOssConfig platformOssConfig;

    /**
     * 统一管理门户首页
     * 展示所有管理模块的卡片，无需Token验证
     */
    @ApiOperation("统一管理门户首页")
    @GetMapping("/index")
    public void index(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("访问统一管理门户首页");

        // 设置响应类型
        response.setContentType("text/html;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        try {
            // 构建管理模块列表
            List<Map<String, Object>> managementModules = buildManagementModules();

            // 获取基础URL用于前端构建链接
            String baseUrl = getBaseUrl();

            log.info("管理门户数据准备完成，模块数量: {}, 基础URL: {}", managementModules.size(), baseUrl);

            // 生成HTML内容
            String htmlContent = generateHtmlContent(managementModules, baseUrl);

            // 直接写入响应
            PrintWriter writer = response.getWriter();
            writer.write(htmlContent);
            writer.flush();

        } catch (Exception e) {
            log.error("生成统一管理门户页面失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

            String errorHtml = generateErrorPage(e.getMessage());
            PrintWriter writer = response.getWriter();
            writer.write(errorHtml);
            writer.flush();
        }
    }

    /**
     * 默认路径重定向到首页
     */
    @GetMapping({"/"})
    public String defaultRedirect() {
        return "redirect:/api/unified-management-portal/index";
    }

    /**
     * 构建管理模块列表
     */
    private List<Map<String, Object>> buildManagementModules() {
        List<Map<String, Object>> modules = new ArrayList<>();

        // 访问日志管理
        modules.add(createModuleCard(
            "access-log-management",
            "访问日志管理",
            "查看和分析系统访问日志，监控用户行为和系统使用情况",
            "fas fa-chart-line",
            "#4CAF50",
            "access-log-management/login.html"
        ));

        // 监控日志管理
        modules.add(createModuleCard(
            "log-management",
            "监控日志管理",
            "实时查看系统运行日志，快速定位和解决系统问题",
            "fas fa-desktop",
            "#2196F3",
            "log-management/log-login.html"
        ));

        // Redis数据管理
        modules.add(createModuleCard(
            "redis-management",
            "Redis数据管理",
            "管理Redis缓存数据，监控缓存性能和数据状态",
            "fas fa-database",
            "#FF5722",
            "redis-management/login.html"
        ));

        // 集群监控管理
        modules.add(createModuleCard(
            "cluster-monitor",
            "集群监控管理",
            "监控集群节点状态，查看系统性能指标和健康状况",
            "fas fa-server",
            "#9C27B0",
            "cluster-monitor/login.html"
        ));

        // 定时任务管理
        modules.add(createModuleCard(
            "quartz-management",
            "定时任务管理",
            "管理系统定时任务，查看任务执行状态和调度情况",
            "fas fa-clock",
            "#FF9800",
            "quartz-management/login.html"
        ));

        // 系统监控中心管理
        modules.add(createModuleCard(
            "system-monitor",
            "系统监控中心管理",
            "实时监控系统性能指标，查看服务器状态和资源使用情况",
            "fas fa-chart-bar",
            "#607D8B",
            "system-monitor/login.html"
        ));

        return modules;
    }

    /**
     * 创建模块卡片数据
     */
    private Map<String, Object> createModuleCard(String moduleId, String title, String description,
                                                String icon, String color, String loginPath) {
        Map<String, Object> card = new HashMap<>();
        card.put("moduleId", moduleId);
        card.put("title", title);
        card.put("description", description);
        card.put("icon", icon);
        card.put("color", color);
        card.put("loginPath", loginPath);
        return card;
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        // 优先使用platform.oss.viewUrl配置
        String configuredViewUrl = platformOssConfig.getOss().getViewUrl();

        if (configuredViewUrl != null && !configuredViewUrl.trim().isEmpty()) {
            log.info("使用配置的platform.oss.viewUrl: {}", configuredViewUrl);
            String baseUrl = configuredViewUrl.trim();
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            return baseUrl;
        } else {
            // 备用方案
            log.warn("platform.oss.viewUrl未配置，使用备用方案");
            return "/api/";
        }
    }

    /**
     * 生成HTML内容
     */
    private String generateHtmlContent(List<Map<String, Object>> managementModules, String baseUrl) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n")
            .append("<html lang=\"zh-CN\">\n")
            .append("<head>\n")
            .append("    <meta charset=\"UTF-8\">\n")
            .append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n")
            .append("    <title>EDC科研协作平台 - 统一管理门户</title>\n")
            .append("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n")
            .append("    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n")
            .append("    <style>\n")
            .append(getCssStyles())
            .append("    </style>\n")
            .append("</head>\n")
            .append("<body>\n")
            .append("    <div class=\"container\">\n")
            .append("        <div class=\"header fade-in\">\n")
            .append("            <h1>EDC科研协作平台</h1>\n")
            .append("            <p>统一管理门户 - 一站式系统管理中心</p>\n")
            .append("        </div>\n")
            .append("        <div class=\"modules-grid\">\n");

        // 生成模块卡片
        for (int i = 0; i < managementModules.size(); i++) {
            Map<String, Object> module = managementModules.get(i);
            html.append(generateModuleCard(module, baseUrl, i));
        }

        html.append("        </div>\n")
            .append("        <div class=\"footer fade-in\" style=\"animation-delay: 0.8s\">\n")
            .append("            <p>&copy; 2025 EDC科研协作平台. 统一管理门户系统</p>\n")
            .append("        </div>\n")
            .append("    </div>\n")
            .append("    <script>\n")
            .append(getJavaScript())
            .append("    </script>\n")
            .append("</body>\n")
            .append("</html>");

        return html.toString();
    }

    /**
     * 生成模块卡片HTML
     */
    private String generateModuleCard(Map<String, Object> module, String baseUrl, int index) {
        String moduleId = (String) module.get("moduleId");
        String title = (String) module.get("title");
        String description = (String) module.get("description");
        String icon = (String) module.get("icon");
        String color = (String) module.get("color");
        String loginPath = (String) module.get("loginPath");
        String fullUrl = baseUrl + loginPath;

        return String.format(
            "            <div class=\"module-card fade-in\" data-color=\"%s\" style=\"animation-delay: %ss\" onclick=\"navigateToModule('%s')\">\n" +
            "                <div class=\"module-icon\">\n" +
            "                    <i class=\"%s\"></i>\n" +
            "                </div>\n" +
            "                <h3 class=\"module-title\">%s</h3>\n" +
            "                <p class=\"module-description\">%s</p>\n" +
            "                <div class=\"module-action\">\n" +
            "                    <span>进入管理</span>\n" +
            "                    <i class=\"fas fa-arrow-right\"></i>\n" +
            "                </div>\n" +
            "            </div>\n",
            color, (index * 0.1), fullUrl, icon, title, description
        );
    }

    /**
     * 获取CSS样式
     */
    private String getCssStyles() {
        return "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
               "body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: #333; }\n" +
               ".container { max-width: 1200px; margin: 0 auto; padding: 2rem; }\n" +
               ".header { text-align: center; margin-bottom: 3rem; color: white; }\n" +
               ".header h1 { font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem; text-shadow: 0 2px 4px rgba(0,0,0,0.3); }\n" +
               ".header p { font-size: 1.1rem; opacity: 0.9; font-weight: 300; }\n" +
               ".modules-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 2rem; }\n" +
               ".module-card { background: white; border-radius: 16px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: all 0.3s ease; cursor: pointer; position: relative; overflow: hidden; }\n" +
               ".module-card:hover { transform: translateY(-8px); box-shadow: 0 20px 40px rgba(0,0,0,0.15); }\n" +
               ".module-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--card-color); }\n" +
               ".module-icon { width: 60px; height: 60px; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; background: var(--card-color); color: white; font-size: 1.5rem; }\n" +
               ".module-title { font-size: 1.3rem; font-weight: 600; margin-bottom: 0.8rem; color: #2d3748; }\n" +
               ".module-description { color: #718096; line-height: 1.6; margin-bottom: 1.5rem; font-size: 0.95rem; }\n" +
               ".module-action { display: flex; align-items: center; color: var(--card-color); font-weight: 500; font-size: 0.9rem; }\n" +
               ".module-action i { margin-left: 0.5rem; transition: transform 0.2s ease; }\n" +
               ".module-card:hover .module-action i { transform: translateX(4px); }\n" +
               ".footer { text-align: center; margin-top: 4rem; color: white; opacity: 0.8; }\n" +
               ".footer p { font-size: 0.9rem; }\n" +
               ".fade-in { animation: fadeIn 0.6s ease-out; }\n" +
               "@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }\n" +
               ".module-card[data-color=\"#4CAF50\"] { --card-color: #4CAF50; }\n" +
               ".module-card[data-color=\"#2196F3\"] { --card-color: #2196F3; }\n" +
               ".module-card[data-color=\"#FF5722\"] { --card-color: #FF5722; }\n" +
               ".module-card[data-color=\"#9C27B0\"] { --card-color: #9C27B0; }\n" +
               ".module-card[data-color=\"#FF9800\"] { --card-color: #FF9800; }\n" +
               ".module-card[data-color=\"#607D8B\"] { --card-color: #607D8B; }\n" +
               "@media (max-width: 768px) { .container { padding: 1rem; } .header h1 { font-size: 2rem; } .modules-grid { grid-template-columns: 1fr; gap: 1.5rem; } .module-card { padding: 1.5rem; } }";
    }

    /**
     * 获取JavaScript代码
     */
    private String getJavaScript() {
        return "function navigateToModule(url) { console.log('跳转到模块:', url); window.open(url, '_blank'); }\n" +
               "document.addEventListener('DOMContentLoaded', function() { console.log('统一管理门户页面加载完成'); });\n" +
               "document.addEventListener('keydown', function(e) { if (e.key >= '1' && e.key <= '6') { const moduleIndex = parseInt(e.key) - 1; const cards = document.querySelectorAll('.module-card'); if (cards[moduleIndex]) { cards[moduleIndex].click(); } } });";
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String errorMessage) {
        return "<!DOCTYPE html>\n" +
               "<html lang=\"zh-CN\">\n" +
               "<head>\n" +
               "    <meta charset=\"UTF-8\">\n" +
               "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
               "    <title>EDC科研协作平台 - 系统错误</title>\n" +
               "    <style>\n" +
               "        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 2rem; }\n" +
               "        .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n" +
               "        .error-title { color: #e74c3c; font-size: 1.5rem; margin-bottom: 1rem; }\n" +
               "        .error-message { color: #666; line-height: 1.6; }\n" +
               "        .back-button { display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }\n" +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <div class=\"error-container\">\n" +
               "        <h1 class=\"error-title\">系统错误</h1>\n" +
               "        <p class=\"error-message\">抱歉，统一管理门户页面生成失败。</p>\n" +
               "        <p class=\"error-message\">错误信息：" + (errorMessage != null ? errorMessage : "未知错误") + "</p>\n" +
               "        <a href=\"javascript:history.back()\" class=\"back-button\">返回上一页</a>\n" +
               "    </div>\n" +
               "</body>\n" +
               "</html>";
    }
}


