package com.haoys.user.web.controller.system;

import com.haoys.user.config.PlatformOssConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一管理门户控制器
 * 提供一个美观的管理门户页面，展示所有管理模块的卡片入口
 *
 * <p>支持的管理功能：</p>
 * <ul>
 *   <li>访问日志管理</li>
 *   <li>监控日志管理</li>
 *   <li>Redis数据管理</li>
 *   <li>集群监控管理</li>
 *   <li>定时任务管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Controller
@Api(tags = "统一管理门户")
@RequestMapping("/unified-management-portal")
public class UnifiedPageManagementController {

    @Autowired
    private PlatformOssConfig platformOssConfig;

    /**
     * 统一管理门户首页
     * 展示所有管理模块的卡片，无需Token验证
     */
    @ApiOperation("统一管理门户首页")
    @GetMapping("/index")
    public String index(Model model, HttpServletRequest request) {
        log.info("访问统一管理门户首页");

        // 构建管理模块列表
        List<Map<String, Object>> managementModules = buildManagementModules();

        // 获取基础URL用于前端构建链接
        String baseUrl = getBaseUrl();

        // 传递数据到前端
        model.addAttribute("managementModules", managementModules);
        model.addAttribute("baseUrl", baseUrl);
        model.addAttribute("pageTitle", "EDC科研协作平台 - 统一管理门户");

        log.info("管理门户数据准备完成，模块数量: {}, 基础URL: {}", managementModules.size(), baseUrl);

        return "system/unified-management-portal";
    }

    /**
     * 默认路径重定向到首页
     */
    @GetMapping({"/"})
    public String defaultRedirect() {
        return "redirect:/api/unified-management-portal/index";
    }

    /**
     * 构建管理模块列表
     */
    private List<Map<String, Object>> buildManagementModules() {
        List<Map<String, Object>> modules = new ArrayList<>();

        // 访问日志管理
        modules.add(createModuleCard(
            "access-log-management",
            "访问日志管理",
            "查看和分析系统访问日志，监控用户行为和系统使用情况",
            "fas fa-chart-line",
            "#4CAF50",
            "access-log-management/login.html"
        ));

        // 监控日志管理
        modules.add(createModuleCard(
            "log-management",
            "监控日志管理",
            "实时查看系统运行日志，快速定位和解决系统问题",
            "fas fa-desktop",
            "#2196F3",
            "log-management/login"
        ));

        // Redis数据管理
        modules.add(createModuleCard(
            "redis-management",
            "Redis数据管理",
            "管理Redis缓存数据，监控缓存性能和数据状态",
            "fas fa-database",
            "#FF5722",
            "redis-management/login.html"
        ));

        // 集群监控管理
        modules.add(createModuleCard(
            "cluster-monitor",
            "集群监控管理",
            "监控集群节点状态，查看系统性能指标和健康状况",
            "fas fa-server",
            "#9C27B0",
            "cluster-monitor/login.html"
        ));

        // 定时任务管理
        modules.add(createModuleCard(
            "quartz-management",
            "定时任务管理",
            "管理系统定时任务，查看任务执行状态和调度情况",
            "fas fa-clock",
            "#FF9800",
            "quartz-management/login.html"
        ));

        return modules;
    }

    /**
     * 创建模块卡片数据
     */
    private Map<String, Object> createModuleCard(String moduleId, String title, String description,
                                                String icon, String color, String loginPath) {
        Map<String, Object> card = new HashMap<>();
        card.put("moduleId", moduleId);
        card.put("title", title);
        card.put("description", description);
        card.put("icon", icon);
        card.put("color", color);
        card.put("loginPath", loginPath);
        return card;
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        // 优先使用platform.oss.viewUrl配置
        String configuredViewUrl = platformOssConfig.getOss().getViewUrl();

        if (configuredViewUrl != null && !configuredViewUrl.trim().isEmpty()) {
            log.info("使用配置的platform.oss.viewUrl: {}", configuredViewUrl);
            String baseUrl = configuredViewUrl.trim();
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            return baseUrl;
        } else {
            // 备用方案
            log.warn("platform.oss.viewUrl未配置，使用备用方案");
            return "/api/";
        }
    }
}


