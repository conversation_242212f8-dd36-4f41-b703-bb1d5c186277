package com.haoys.user.web.controller.system;

import com.haoys.user.config.PlatformOssConfig;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.util.SecureTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 统一页面管理控制器
 * 提供各种管理功能的统一登录入口，支持动态路径配置
 * 
 * <p>支持的管理功能：</p>
 * <ul>
 *   <li>访问日志管理</li>
 *   <li>监控日志管理</li>
 *   <li>Redis数据管理</li>
 *   <li>集群监控管理</li>
 *   <li>定时任务管理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Controller
@Api(tags = "统一页面管理")
@RequestMapping("/unified-page-management")
public class UnifiedPageManagementController {

    @Autowired
    private SecureTokenUtil secureTokenUtil;

    @Autowired
    private SecureTokenService secureTokenService;

    @Autowired
    private PlatformOssConfig platformOssConfig;

    @Value("${unified.page.management.secret-key:EDC-UNIFIED-PAGE-MANAGE-SECRET-2025}")
    private String unifiedPageSecretKey;

    /**
     * 访问日志管理登录页面
     */
    @ApiOperation("访问日志管理登录页面")
    @GetMapping("/access-log-login")
    public String accessLogLogin() {
        log.info("访问日志管理登录页面请求");
        return "redirect:" + buildManagementUrl("access-log-management", "login.html");
    }

    /**
     * 访问日志管理页面（需要AccessToken验证）
     */
    @ApiOperation("访问日志管理页面")
    @GetMapping("/access-log-management")
    public void accessLogManagement(
            @ApiParam(value = "访问令牌", required = true) @RequestParam String accessToken,
            HttpServletResponse response) throws IOException {
        
        log.info("访问日志管理页面请求，AccessToken: {}", accessToken);
        
        if (!validateAccessToken(accessToken)) {
            log.warn("AccessToken验证失败，重定向到登录页面");
            response.sendRedirect(buildManagementUrl("access-log-management", "login.html"));
            return;
        }
        
        String managementUrl = buildManagementUrl("access-log-management", "management.html") + "?accessToken=" + accessToken;
        log.info("重定向到访问日志管理页面: {}", managementUrl);
        response.sendRedirect(managementUrl);
    }

    /**
     * 监控日志管理登录页面
     */
    @ApiOperation("监控日志管理登录页面")
    @GetMapping("/log-management-login")
    public String logManagementLogin() {
        log.info("监控日志管理登录页面请求");
        return "redirect:" + buildManagementUrl("log-management", "login");
    }

    /**
     * 监控日志管理页面（需要AccessToken验证）
     */
    @ApiOperation("监控日志管理页面")
    @GetMapping("/log-management")
    public void logManagement(
            @ApiParam(value = "访问令牌", required = true) @RequestParam String accessToken,
            HttpServletResponse response) throws IOException {
        
        log.info("监控日志管理页面请求，AccessToken: {}", accessToken);
        
        if (!validateAccessToken(accessToken)) {
            log.warn("AccessToken验证失败，重定向到登录页面");
            response.sendRedirect(buildManagementUrl("log-management", "login"));
            return;
        }
        
        String managementUrl = buildManagementUrl("log-management", "log-login.html") + "?accessToken=" + accessToken;
        log.info("重定向到监控日志管理页面: {}", managementUrl);
        response.sendRedirect(managementUrl);
    }

    /**
     * Redis数据管理登录页面
     */
    @ApiOperation("Redis数据管理登录页面")
    @GetMapping("/redis-management-login")
    public String redisManagementLogin() {
        log.info("Redis数据管理登录页面请求");
        return "redirect:" + buildManagementUrl("redis-management", "login.html");
    }

    /**
     * Redis数据管理页面（需要AccessToken验证）
     */
    @ApiOperation("Redis数据管理页面")
    @GetMapping("/redis-management")
    public void redisManagement(
            @ApiParam(value = "访问令牌", required = true) @RequestParam String accessToken,
            HttpServletResponse response) throws IOException {
        
        log.info("Redis数据管理页面请求，AccessToken: {}", accessToken);
        
        if (!validateAccessToken(accessToken)) {
            log.warn("AccessToken验证失败，重定向到登录页面");
            response.sendRedirect(buildManagementUrl("redis-management", "login.html"));
            return;
        }
        
        String managementUrl = buildManagementUrl("redis-management", "management.html") + "?accessToken=" + accessToken;
        log.info("重定向到Redis数据管理页面: {}", managementUrl);
        response.sendRedirect(managementUrl);
    }

    /**
     * 集群监控管理登录页面
     */
    @ApiOperation("集群监控管理登录页面")
    @GetMapping("/cluster-monitor-login")
    public String clusterMonitorLogin() {
        log.info("集群监控管理登录页面请求");
        return "redirect:" + buildManagementUrl("cluster-monitor", "login.html");
    }

    /**
     * 集群监控管理页面（需要AccessToken验证）
     */
    @ApiOperation("集群监控管理页面")
    @GetMapping("/cluster-monitor")
    public void clusterMonitor(
            @ApiParam(value = "访问令牌", required = true) @RequestParam String accessToken,
            HttpServletResponse response) throws IOException {
        
        log.info("集群监控管理页面请求，AccessToken: {}", accessToken);
        
        if (!validateAccessToken(accessToken)) {
            log.warn("AccessToken验证失败，重定向到登录页面");
            response.sendRedirect(buildManagementUrl("cluster-monitor", "login.html"));
            return;
        }
        
        String managementUrl = buildManagementUrl("cluster-monitor", "index.html") + "?accessToken=" + accessToken;
        log.info("重定向到集群监控管理页面: {}", managementUrl);
        response.sendRedirect(managementUrl);
    }

    /**
     * 定时任务管理登录页面
     */
    @ApiOperation("定时任务管理登录页面")
    @GetMapping("/quartz-management-login")
    public String quartzManagementLogin() {
        log.info("定时任务管理登录页面请求");
        return "redirect:" + buildManagementUrl("quartz-management", "login.html");
    }

    /**
     * 定时任务管理页面（需要AccessToken验证）
     */
    @ApiOperation("定时任务管理页面")
    @GetMapping("/quartz-management")
    public void quartzManagement(
            @ApiParam(value = "访问令牌", required = true) @RequestParam String accessToken,
            HttpServletResponse response) throws IOException {
        
        log.info("定时任务管理页面请求，AccessToken: {}", accessToken);
        
        if (!validateAccessToken(accessToken)) {
            log.warn("AccessToken验证失败，重定向到登录页面");
            response.sendRedirect(buildManagementUrl("quartz-management", "login.html"));
            return;
        }
        
        String managementUrl = buildManagementUrl("quartz-management", "management.html") + "?accessToken=" + accessToken;
        log.info("重定向到定时任务管理页面: {}", managementUrl);
        response.sendRedirect(managementUrl);
    }

    /**
     * 验证AccessToken
     */
    private boolean validateAccessToken(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            return false;
        }

        try {
            return secureTokenUtil.isValidAccessToken(accessToken.trim());
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return false;
        }
    }

    /**
     * 构建管理页面URL（使用platform.oss.viewUrl配置，避免路径重复问题）
     */
    private String buildManagementUrl(String module, String page) {
        // 优先使用platform.oss.viewUrl配置，这是nginx代理的最终接口地址
        String configuredViewUrl = platformOssConfig.getOss().getViewUrl();

        if (configuredViewUrl != null && !configuredViewUrl.trim().isEmpty()) {
            // 使用配置的viewUrl作为基础URL，确保路径正确
            log.info("使用配置的platform.oss.viewUrl构建管理URL: {}", configuredViewUrl);

            // 确保URL格式正确
            String baseUrl = configuredViewUrl.trim();
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }

            // 构建完整URL：baseUrl + module/ + page
            String fullUrl = baseUrl + module + "/" + page;
            log.info("构建的管理URL: {}", fullUrl);
            return fullUrl;
        } else {
            // 备用方案：使用原有的相对路径
            log.warn("platform.oss.viewUrl未配置，使用备用方案");
            return "/api/" + module + "/" + page;
        }
    }
}
