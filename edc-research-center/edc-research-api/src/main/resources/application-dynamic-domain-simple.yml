# 动态域名配置 - 简化版本
# 只包含必要的配置项，便于维护和部署

dynamic-domain:
  # 启用动态域名检测
  enabled: true

  # 默认配置（当域名未匹配时使用）
  defaultSettings:
    name: "默认配置"
    apiPath: "/api/"
    includePort: true
    clientType: "web"
    theme: "default"
    layout: "standard"

  # 域名配置映射
  domains:
    # 本地开发环境
    localhost:
      name: "本地开发"
      clientType: "dev"
      theme: "debug"
      layout: "full"
      customConfig:
        debug: true
        sidebar: true

    # PC端域名（使用引号包围域名）
    'dev-1.info.com':
      name: "PC端"
      clientType: "pc"
      theme: "desktop"
      layout: "full"
      customConfig:
        sidebar: true
        toolbar: "full"

    # 移动H5端域名（使用引号包围域名）
    'dev-2.info.com':
      name: "移动H5端"
      clientType: "mobile"
      theme: "mobile"
      layout: "compact"
      customConfig:
        sidebar: false
        toolbar: "minimal"
