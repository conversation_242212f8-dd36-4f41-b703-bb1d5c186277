# 工作节点-01 配置文件 (端口8001)
server:
  port: 8001

# 集群配置
cluster:
  management:
    # 当前节点配置
    current-node:
      node-id: "worker-node-01"
      node-name: "工作节点-01"
      node-type: SLAVE
      ip: "127.0.0.1"
      port: 8001
      enabled: true
      description: "集群工作节点-01"

    # 主节点配置
    master-node:
      ip: "127.0.0.1"
      port: 8000
      
# 日志配置
logging:
  file:
    name: logs/worker-node-01.log
  level:
    com.haoys: DEBUG
    root: INFO

# 应用名称
spring:
  application:
    name: edc-research-worker-01
