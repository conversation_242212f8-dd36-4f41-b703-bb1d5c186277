# 工作节点-02 配置文件 (端口8002)
server:
  port: 8002

# 集群配置
cluster:
  management:
    # 当前节点配置
    current-node:
      node-id: "worker-node-02"
      node-name: "工作节点-02"
      node-type: SLAVE
      ip: "127.0.0.1"
      port: 8002
      enabled: true
      description: "集群工作节点-02"

    # 主节点配置
    master-node:
      ip: "127.0.0.1"
      port: 8000
      
# 日志配置
logging:
  file:
    name: logs/worker-node-02.log
  level:
    com.haoys: DEBUG
    root: INFO

# 应用名称
spring:
  application:
    name: edc-research-worker-02
