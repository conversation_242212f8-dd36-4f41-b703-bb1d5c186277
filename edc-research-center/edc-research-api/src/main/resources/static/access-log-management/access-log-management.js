/**
 * 系统访问日志管理 JavaScript
 * 提供完整的日志查询、统计和监控功能
 */

// 全局变量
let currentPage = 1;
let currentPageSize = 20;
let accessToken = '';
let isAuthenticated = false;
let tokenExpireTime = 0;
let tokenStatusTimer = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('系统访问日志管理页面初始化...');
    initializePage();
});

/**
 * 初始化页面
 */
async function initializePage() {
    console.log('开始初始化页面...');

    // 1. 优先从URL参数获取AccessToken
    const urlParams = new URLSearchParams(window.location.search);
    const urlAccessToken = urlParams.get('accessToken');

    if (urlAccessToken && urlAccessToken.trim()) {
        console.log('从URL参数获取到AccessToken:', urlAccessToken.substring(0, 20) + '...');
        accessToken = urlAccessToken.trim();

        // 验证Token并获取准确的过期时间
        try {
            const response = await fetch(`${getApiBaseUrl()}/secure/token/validate?accessToken=${encodeURIComponent(accessToken)}`);
            const result = await response.json();

            if (result.code === 200 && result.data && result.data.valid) {
                // 根据服务器返回的剩余时间设置过期时间
                tokenExpireTime = Date.now() + (result.data.remainingTime * 1000);
                isAuthenticated = true;

                // 保存到sessionStorage
                sessionStorage.setItem('accessLogToken', accessToken);
                sessionStorage.setItem('accessLogTokenExpire', tokenExpireTime.toString());

                console.log('URL AccessToken验证成功，剩余时间:', result.data.remainingTime, '秒');
                showMainContent();
                // 默认加载访问日志数据
                loadAccessLogs();
                return;
            } else {
                console.log('URL AccessToken无效，显示认证界面');
                // Token无效，继续执行后续逻辑
            }
        } catch (error) {
            console.error('Token验证失败:', error);
            // 验证失败，继续执行后续逻辑
        }
    }

    // 2. 从服务器配置获取AccessToken（服务器端注入）
    if (window.SERVER_CONFIG && window.SERVER_CONFIG.accessToken) {
        console.log('从服务器配置获取到AccessToken');
        accessToken = window.SERVER_CONFIG.accessToken;
        tokenExpireTime = Date.now() + (60 * 60 * 1000);
        isAuthenticated = true;

        // 保存到sessionStorage
        sessionStorage.setItem('accessLogToken', accessToken);
        sessionStorage.setItem('accessLogTokenExpire', tokenExpireTime.toString());

        console.log('服务器配置AccessToken验证成功，直接显示主内容');
        showMainContent();
        loadAccessLogs();
        return;
    }

    // 3. 从sessionStorage获取保存的Token
    const savedToken = sessionStorage.getItem('accessLogToken');
    const tokenExpire = sessionStorage.getItem('accessLogTokenExpire');

    if (savedToken && tokenExpire && Date.now() < parseInt(tokenExpire)) {
        console.log('从sessionStorage获取到有效的AccessToken');
        accessToken = savedToken;
        tokenExpireTime = parseInt(tokenExpire);
        isAuthenticated = true;
        showMainContent();
        // 默认加载访问日志数据
        loadAccessLogs();
        return;
    }

    // 4. 如果没有找到有效的AccessToken，显示认证界面
    console.log('未找到有效的AccessToken，显示认证界面');

    // 绑定回车键事件
    document.getElementById('secretKey').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifySecretKey();
        }
    });

    const codeInput = document.getElementById('code');
    const refreshCodeInput = document.getElementById('refreshCode');

    if (codeInput) {
        codeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }

    if (refreshCodeInput) {
        refreshCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }
}

/**
 * 获取API基础URL
 * 动态获取当前页面的路径前缀，支持不同部署环境（api、releaseApi、BoRuiApi等）
 */
function getApiBaseUrl() {
    console.log('🔍 开始获取API基础URL...');

    // 1. 优先使用服务器配置
    if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
        console.log('✅ 使用服务器配置的requestUrl:', window.SERVER_CONFIG.requestUrl);
        return window.SERVER_CONFIG.requestUrl;
    }

    // 2. 动态从当前页面URL中提取路径前缀
    const currentPath = window.location.pathname;
    console.log('🌐 当前页面路径:', currentPath);

    // 当前页面路径示例：
    // - /api/access-log/management
    // - /releaseApi/access-log/management
    // - /BoRuiApi/access-log/management

    // 提取路径前缀（第一个路径段）
    const pathSegments = currentPath.split('/').filter(segment => segment.length > 0);
    const contextPath = pathSegments.length > 0 ? `/${pathSegments[0]}` : '';

    console.log('📂 提取的上下文路径:', contextPath);

    // 3. 构建完整的基础URL
    const protocol = window.location.protocol;
    const host = window.location.host;
    const baseUrl = `${protocol}//${host}${contextPath}`;

    console.log('🎯 最终API基础URL:', baseUrl);
    return baseUrl;
}

/**
 * 显示提示信息
 */
function showAlert(containerId, message, type = 'danger') {
    const alertContainer = document.getElementById(containerId);
    if (alertContainer) {
        alertContainer.className = `alert ${type}`;
        alertContainer.textContent = message;
        alertContainer.style.display = 'block';
        
        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                alertContainer.style.display = 'none';
            }, 3000);
        }
    }
}

/**
 * 验证秘钥
 */
async function verifySecretKey() {
    const secretKey = document.getElementById('secretKey').value.trim();
    
    if (!secretKey) {
        showAlert('secretAlert', '请输入秘钥');
        return;
    }
    
    try {
        const response = await fetch(`${getApiBaseUrl()}/access-log/auth/verify-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ secretKey })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('secretAlert', '秘钥验证成功，请获取访问令牌', 'success');
            document.getElementById('secretKeyForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        } else {
            showAlert('secretAlert', result.message || '秘钥验证失败');
        }
    } catch (error) {
        console.error('秘钥验证失败:', error);
        showAlert('secretAlert', '网络错误，请稍后重试');
    }
}

/**
 * 获取访问令牌
 */
async function getAccessToken() {
    const code = document.getElementById('code').value.trim();
    const refreshCode = document.getElementById('refreshCode').value.trim();

    if (!code || !refreshCode) {
        showAlert('tokenAlert', '请输入验证码和刷新码');
        return;
    }

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/getAccessToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: code,
                refreshCode: refreshCode
            })
        });

        const result = await response.json();

        if (result.code === 200 && result.data && result.data.accessToken) {
            accessToken = result.data.accessToken;
            console.log('AccessToken获取成功:', accessToken);

            // 保存token到sessionStorage
            sessionStorage.setItem('accessLogToken', accessToken);
            if (result.data.expiresIn) {
                tokenExpireTime = Date.now() + (result.data.expiresIn * 1000);
                sessionStorage.setItem('accessLogTokenExpire', tokenExpireTime);
            }

            showAlert('tokenAlert', '访问令牌获取成功！', 'success');

            // 显示第三步验证界面
            document.getElementById('tokenForm').style.display = 'none';
            document.getElementById('validateForm').style.display = 'block';

            // 自动验证token
            setTimeout(validateAccessToken, 1000);
        } else {
            showAlert('tokenAlert', result.message || '获取访问令牌失败');
        }
    } catch (error) {
        console.error('获取访问令牌失败:', error);
        showAlert('tokenAlert', '网络错误，请稍后重试');
    }
}

/**
 * 验证访问令牌
 */
async function validateAccessToken() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('success-message').style.display = 'none';

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/validate?accessToken=${encodeURIComponent(accessToken)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        document.getElementById('loading').style.display = 'none';

        if (result.code === 200 && result.data && result.data.valid) {
            console.log('Token验证成功，剩余时间:', result.data.remainingTime, '秒');

            // 显示成功消息
            document.getElementById('success-message').style.display = 'block';

            isAuthenticated = true;

            // 延迟跳转到管理页面
            setTimeout(() => {
                showMainContent();
                loadAccessLogs();
            }, 2000);
        } else {
            showAlert('tokenAlert', result.message || 'Token验证失败');
            // 重新显示token获取表单
            document.getElementById('validateForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        }
    } catch (error) {
        console.error('Token验证失败:', error);
        document.getElementById('loading').style.display = 'none';
        showAlert('tokenAlert', '网络错误，请稍后重试');
        // 重新显示token获取表单
        document.getElementById('validateForm').style.display = 'none';
        document.getElementById('tokenForm').style.display = 'block';
    }
}

/**
 * 显示主要内容
 */
function showMainContent() {
    console.log('🎯 showMainContent() 被调用');

    const authSection = document.getElementById('authSection');
    const mainContent = document.getElementById('mainContent');
    const tokenStatus = document.getElementById('tokenStatus');

    if (authSection) {
        authSection.style.display = 'none';
        console.log('✅ 隐藏认证区域');
    } else {
        console.error('❌ 找不到authSection元素');
    }

    if (mainContent) {
        mainContent.style.display = 'block';
        console.log('✅ 显示主内容区域');
    } else {
        console.error('❌ 找不到mainContent元素');
    }

    if (tokenStatus) {
        tokenStatus.style.display = 'flex';
        console.log('✅ 显示Token状态');
    } else {
        console.log('⚠️ 找不到tokenStatus元素（可能不存在）');
    }

    // 启动Token状态监控
    startTokenStatusMonitor();
    console.log('🚀 主内容显示完成');
}

/**
 * 显示认证区域（Token过期或无效时）
 */
function showAuthSection() {
    console.log('🔐 showAuthSection() 被调用');

    const authSection = document.getElementById('authSection');
    const mainContent = document.getElementById('mainContent');
    const tokenStatus = document.getElementById('tokenStatus');

    // 清除Token相关信息
    accessToken = '';
    isAuthenticated = false;
    tokenExpireTime = 0;
    sessionStorage.removeItem('accessLogToken');
    sessionStorage.removeItem('accessLogTokenExpire');

    // 停止Token状态监控
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
        tokenStatusTimer = null;
    }

    if (mainContent) {
        mainContent.style.display = 'none';
        console.log('✅ 隐藏主内容区域');
    }

    if (tokenStatus) {
        tokenStatus.style.display = 'none';
        console.log('✅ 隐藏Token状态');
    }

    if (authSection) {
        authSection.style.display = 'block';
        console.log('✅ 显示认证区域');

        // 重置认证表单状态
        document.getElementById('secretKeyForm').style.display = 'block';
        document.getElementById('tokenForm').style.display = 'none';
        document.getElementById('validateForm').style.display = 'none';

        // 清空表单
        document.getElementById('secretKey').value = '';
        document.getElementById('code').value = '';
        document.getElementById('refreshCode').value = '';

        // 清空警告信息
        const alerts = ['secretAlert', 'tokenAlert'];
        alerts.forEach(alertId => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.style.display = 'none';
                alert.textContent = '';
            }
        });
    } else {
        console.error('❌ 找不到authSection元素');
    }

    console.log('🔐 认证区域显示完成');
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');

    // 如果切换到非监控标签页，停止实时监控
    if (tabName !== 'monitoring' && isMonitoringActive) {
        stopRealTimeMonitoring();
    }

    // 根据标签页加载对应数据
    switch(tabName) {
        case 'logs':
            loadAccessLogs();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'monitoring':
            loadMonitoring();
            break;
    }
}

/**
 * 加载访问日志数据
 */
async function loadAccessLogs() {
    try {
        showLoading('logsContent');
        
        const params = {
            pageNum: currentPage,
            pageSize: currentPageSize,
            userId: document.getElementById('userId').value.trim(),
            userName: document.getElementById('userName').value.trim(),
            requestUrl: document.getElementById('requestUrl').value.trim(),
            requestMethod: document.getElementById('requestMethod').value,
            status: document.getElementById('status').value,
            startTime: document.getElementById('startTime').value,
            endTime: document.getElementById('endTime').value,
            requestIp: document.getElementById('requestIp').value.trim()
        };
        
        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        // 构建查询字符串
        const queryString = new URLSearchParams(params).toString();
        const url = `${getApiBaseUrl()}/access-log/management/list?${queryString}`;
        const response = await makeApiRequest(url, {
            method: 'GET'
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            displayAccessLogs(result.data);
            showAlert('logs-alert', '数据加载成功', 'success');
        } else {
            showAlert('logs-alert', result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载访问日志失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示加载状态
 */
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }
}

/**
 * 发起API请求
 */
async function makeApiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'X-Access-Token': accessToken
        }
    };
    
    if (options.method === 'GET' && options.data) {
        const params = new URLSearchParams(options.data);
        url += '?' + params.toString();
        delete options.data;
    } else if (options.data) {
        defaultOptions.body = JSON.stringify(options.data);
    }
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    return fetch(url, finalOptions);
}

/**
 * 显示访问日志数据
 */
function displayAccessLogs(data) {
    const container = document.getElementById('logsContent');
    const pagination = document.getElementById('logsPagination');
    
    if (!data || !data.records || data.records.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无数据</div>';
        pagination.style.display = 'none';
        return;
    }
    
    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>请求方法</th>
                    <th>请求URL</th>
                    <th>状态</th>
                    <th>响应时间</th>
                    <th>IP地址</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.records.forEach(record => {
        const statusClass = record.isSuccess ? 'status-success' : 'status-error';
        const statusText = record.isSuccess ? '成功' : '失败';
        const responseTime = record.responseTime ? record.responseTime + 'ms' : '-';
        const requestTime = record.requestStartTime ? new Date(record.requestStartTime).toLocaleString() : '-';
        
        html += `
            <tr>
                <td>${requestTime}</td>
                <td>${record.userName || '-'}</td>
                <td>${record.requestMethod || '-'}</td>
                <td title="${record.requestUrl || '-'}">${truncateText(record.requestUrl || '-', 50)}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${responseTime}</td>
                <td>${record.requestIp || '-'}</td>
                <td>
                    <button class="btn-search" onclick="viewLogDetail('${record.id}')">详情</button>
                </td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    container.innerHTML = html;
    
    // 显示分页控件
    if (data.total > 0) {
        displayPagination(data);
        pagination.style.display = 'flex';
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * 显示分页控件
 */
function displayPagination(data) {
    const pagination = document.getElementById('logsPagination');

    // 适配后端返回的数据结构
    const currentPage = data.pageNum || data.current || 1;
    const totalPages = data.pages || data.totalPages || 1;
    const total = data.total || 0;

    let html = `
        <div class="pagination-info">
            第 ${currentPage} 页，共 ${totalPages} 页，总计 ${total} 条记录
        </div>
        <div class="pagination-controls">
    `;

    // 首页
    if (currentPage > 1) {
        html += `
            <button class="pagination-btn prev" onclick="changePage(1)">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="11,17 6,12 11,7"></polyline>
                    <polyline points="18,17 13,12 18,7"></polyline>
                </svg>
                首页
            </button>
        `;
    }

    // 上一页
    if (currentPage > 1) {
        html += `
            <button class="pagination-btn prev" onclick="changePage(${currentPage - 1})">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
                上一页
            </button>
        `;
    } else {
        html += `<button class="pagination-btn prev disabled">上一页</button>`;
    }

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    // 如果起始页大于1，显示省略号
    if (startPage > 1) {
        html += `<button class="pagination-btn" onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
    }

    // 显示页码
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'pagination-btn active' : 'pagination-btn';
        html += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
    }

    // 如果结束页小于总页数，显示省略号
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<span class="pagination-ellipsis">...</span>`;
        }
        html += `<button class="pagination-btn" onclick="changePage(${totalPages})">${totalPages}</button>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        html += `
            <button class="pagination-btn next" onclick="changePage(${currentPage + 1})">
                下一页
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,18 15,12 9,6"></polyline>
                </svg>
            </button>
        `;
    } else {
        html += `<button class="pagination-btn next disabled">下一页</button>`;
    }

    // 尾页
    if (currentPage < totalPages) {
        html += `
            <button class="pagination-btn next" onclick="changePage(${totalPages})">
                尾页
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="13,17 18,12 13,7"></polyline>
                    <polyline points="6,17 11,12 6,7"></polyline>
                </svg>
            </button>
        `;
    }

    html += `
        </div>
        <div class="page-size-selector">
            <span>每页显示</span>
            <select onchange="changePageSize(this.value)">
                <option value="10" ${currentPageSize === 10 ? 'selected' : ''}>10</option>
                <option value="20" ${currentPageSize === 20 ? 'selected' : ''}>20</option>
                <option value="50" ${currentPageSize === 50 ? 'selected' : ''}>50</option>
                <option value="100" ${currentPageSize === 100 ? 'selected' : ''}>100</option>
            </select>
            <span>条</span>
        </div>
    `;

    pagination.innerHTML = html;
}

/**
 * 切换页面
 */
function changePage(page) {
    currentPage = page;
    loadAccessLogs();
}

/**
 * 改变每页显示数量
 */
function changePageSize(pageSize) {
    currentPageSize = parseInt(pageSize);
    currentPage = 1; // 重置到第一页
    loadAccessLogs();
}

/**
 * 查看日志详情
 */
async function viewLogDetail(logId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/detail/${logId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            showLogDetailModal(result.data);
        } else {
            showAlert('logs-alert', result.message || '获取详情失败');
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示日志详情模态框
 */
function showLogDetailModal(data) {
    const modal = document.getElementById('detailModal');
    const content = document.getElementById('detailContent');

    const html = `
        <div class="detail-grid">
            <div class="detail-item">
                <label>追踪ID:</label>
                <span>${data.traceId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户ID:</label>
                <span>${data.userId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户名:</label>
                <span>${data.userName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>真实姓名:</label>
                <span>${data.realName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>会话ID:</label>
                <span>${data.sessionId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>请求方法:</label>
                <span>${data.requestMethod || '-'}</span>
            </div>
            <div class="detail-item">
                <label>HTTP状态码:</label>
                <span>${data.httpStatus || '-'}</span>
            </div>
            <div class="detail-item">
                <label>响应大小:</label>
                <span>${data.responseSize ? formatBytes(data.responseSize) : '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>请求URL:</label>
                <span>${data.requestUrl || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>方法名:</label>
                <span>${data.methodName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>开始时间:</label>
                <span>${data.requestStartTimeFormatted || (data.requestStartTime ? new Date(data.requestStartTime).toLocaleString() : '-')}</span>
            </div>
            <div class="detail-item">
                <label>结束时间:</label>
                <span>${data.requestEndTimeFormatted || (data.requestEndTime ? new Date(data.requestEndTime).toLocaleString() : '-')}</span>
            </div>
            <div class="detail-item">
                <label>响应时间:</label>
                <span>${data.responseTime ? data.responseTime + 'ms' : '-'}</span>
            </div>
            <div class="detail-item">
                <label>请求持续时间:</label>
                <span>${data.requestDuration ? data.requestDuration + 'ms' : '-'}</span>
            </div>
            <div class="detail-item">
                <label>执行状态:</label>
                <span class="${data.isSuccess ? 'status-success' : 'status-error'}">${data.isSuccess ? '成功' : '失败'}</span>
            </div>
            <div class="detail-item">
                <label>IP地址:</label>
                <span>${data.requestIp || '-'}</span>
            </div>
            <div class="detail-item">
                <label>地理位置:</label>
                <span>${data.location || '-'}</span>
            </div>
            <div class="detail-item">
                <label>浏览器:</label>
                <span>${data.browser || '-'}</span>
            </div>
            <div class="detail-item">
                <label>操作系统:</label>
                <span>${data.operatingSystem || '-'}</span>
            </div>
            <div class="detail-item">
                <label>设备类型:</label>
                <span>${data.deviceType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>业务类型:</label>
                <span>${data.businessType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>操作类型:</label>
                <span>${data.operatorType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>数据来源:</label>
                <span>${data.dataFrom || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>User-Agent:</label>
                <span style="word-break: break-all; font-family: 'Courier New', monospace; font-size: 12px;">${data.userAgent || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>Referer:</label>
                <span style="word-break: break-all; font-family: 'Courier New', monospace; font-size: 12px;">${data.referer || '-'}</span>
            </div>
            ${data.requestParam && data.requestParam !== '-' ? `
            <div class="detail-item full-width">
                <label>请求参数:</label>
                ${formatJsonField(data.requestParam, 'requestParam')}
            </div>
            ` : ''}
            ${data.responseResult && data.responseResult !== '-' ? `
            <div class="detail-item full-width">
                <label>响应结果:</label>
                ${formatJsonField(data.responseResult, 'responseResult')}
            </div>
            ` : ''}
            ${data.errorMessage ? `
            <div class="detail-item full-width">
                <label>错误信息:</label>
                <div class="error-text">${data.errorMessage}</div>
            </div>
            ` : ''}
            ${data.exceptionType ? `
            <div class="detail-item full-width">
                <label>异常类型:</label>
                <span style="color: #dc3545; font-family: 'Courier New', monospace;">${data.exceptionType}</span>
            </div>
            ` : ''}
            ${data.exceptionStack ? `
            <div class="detail-item full-width">
                <label>异常堆栈:</label>
                <div class="json-container">
                    <button class="copy-btn" onclick="copyToClipboard('exceptionStack', this)">复制</button>
                    <div class="json-content" id="exceptionStack">${data.exceptionStack}</div>
                </div>
            </div>
            ` : ''}
        </div>
    `;

    content.innerHTML = html;
    modal.style.display = 'block';
}

/**
 * 格式化JSON字段
 */
function formatJsonField(jsonStr, fieldId) {
    if (!jsonStr || jsonStr === '-') {
        return '<span>-</span>';
    }

    try {
        // 尝试解析JSON
        const parsed = JSON.parse(jsonStr);
        const formatted = JSON.stringify(parsed, null, 2);
        return `
            <div class="json-container">
                <button class="copy-btn" onclick="copyToClipboard('${fieldId}', this)">复制</button>
                <div class="json-content" id="${fieldId}">${escapeHtml(formatted)}</div>
            </div>
        `;
    } catch (e) {
        // 如果不是JSON，直接显示
        return `
            <div class="json-container">
                <button class="copy-btn" onclick="copyToClipboard('${fieldId}', this)">复制</button>
                <div class="json-content" id="${fieldId}">${escapeHtml(jsonStr)}</div>
            </div>
        `;
    }
}

/**
 * 转义HTML字符
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 格式化字节大小
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(elementId, button) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const text = element.textContent;

    // 使用现代API
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess(button);
        }).catch(() => {
            fallbackCopyTextToClipboard(text, button);
        });
    } else {
        fallbackCopyTextToClipboard(text, button);
    }
}

/**
 * 备用复制方法
 */
function fallbackCopyTextToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('复制失败:', err);
    }

    document.body.removeChild(textArea);
}

/**
 * 显示复制成功
 */
function showCopySuccess(button) {
    const originalText = button.textContent;
    button.textContent = '已复制';
    button.classList.add('copied');

    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('copied');
    }, 2000);
}

/**
 * 关闭详情模态框
 */
function closeDetailModal() {
    document.getElementById('detailModal').style.display = 'none';
}

/**
 * 搜索日志
 */
function searchLogs() {
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('userId').value = '';
    document.getElementById('userName').value = '';
    document.getElementById('requestUrl').value = '';
    document.getElementById('requestMethod').value = '';
    document.getElementById('status').value = '';
    document.getElementById('startTime').value = '';
    document.getElementById('endTime').value = '';
    document.getElementById('requestIp').value = '';
    
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        showLoading('statisticsContent');
        
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`);
        const result = await response.json();
        
        if (result.code === 200) {
            displayStatistics(result.data);
        } else {
            showAlert('statistics-alert', result.message || '加载统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showAlert('statistics-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示统计数据
 */
function displayStatistics(data) {
    const container = document.getElementById('statisticsContent');

    console.log('📊 显示统计数据:', data);

    // 从后端返回的数据中提取正确的字段
    // 后端返回的字段名：total_records, success_records, error_records, avg_response_time
    const totalRequests = data.total_records || data.totalRequests || 0;
    const successRequests = data.success_records || data.successRequests || 0;
    const errorRequests = data.error_records || data.errorRequests || 0;
    const avgResponseTime = data.avg_response_time || data.avgResponseTime || 0;

    // 计算成功率和失败率
    const successRate = totalRequests > 0 ? ((successRequests / totalRequests) * 100).toFixed(2) : 0;
    const errorRate = totalRequests > 0 ? ((errorRequests / totalRequests) * 100).toFixed(2) : 0;

    // 获取慢请求数（从slowRequests数组的长度或直接从字段获取）
    let slowRequestsCount = 0;
    if (data.slowRequests) {
        slowRequestsCount = Array.isArray(data.slowRequests) ? data.slowRequests.length : data.slowRequests;
    }

    console.log('📈 计算后的指标:', {
        totalRequests,
        successRequests,
        errorRequests,
        successRate,
        errorRate,
        avgResponseTime,
        slowRequestsCount
    });

    const html = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="detail-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">总请求数</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${totalRequests.toLocaleString()}</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">成功请求数</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${successRequests.toLocaleString()}</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">失败请求数</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${errorRequests.toLocaleString()}</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #28a745 0%, #34ce57 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">成功率</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${successRate}%</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">失败率</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${errorRate}%</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">平均响应时间</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${Math.round(avgResponseTime)}ms</span>
            </div>
            <div class="detail-item" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%); color: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);">
                <label style="font-size: 14px; opacity: 0.9; margin-bottom: 8px; display: block;">慢请求数</label>
                <span style="font-size: 32px; font-weight: bold; display: block;">${slowRequestsCount.toLocaleString()}</span>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">📊 统计说明</h4>
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                统计时间范围：最近24小时 |
                慢请求阈值：响应时间 > 1000ms |
                数据更新时间：${new Date().toLocaleString()}
            </p>
        </div>
    `;

    container.innerHTML = html;
}

// 监控相关变量
let monitoringTimer = null;
let isMonitoringActive = false;
let forceRefreshTimer = null;
let refreshCount = 0;

/**
 * 加载监控数据
 */
async function loadMonitoring() {
    try {
        showLoading('monitoringContent');

        // 同时加载监控数据和统计数据
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('发现统计数据问题:', dataIssues);
                showDataIssuesNotification(dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            // 启动实时监控
            startRealTimeMonitoring();
        } else {
            showAlert('monitoring-alert', '加载监控数据失败');
        }
    } catch (error) {
        console.error('加载监控数据失败:', error);
        showAlert('monitoring-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示监控数据
 */
function displayMonitoring(monitoringData, statisticsData) {
    const container = document.getElementById('monitoringContent');

    // 添加调试信息
    console.log('🖥️ 显示监控数据:', { monitoringData, statisticsData });
    console.log('📊 统计数据详情:', {
        hotUrls: statisticsData.hotUrls,
        hourlyTrend: statisticsData.hourlyTrend,
        realTime: statisticsData.realTime
    });

    // 获取实时数据
    const realTime = statisticsData.realTime || {};
    const systemHealthy = monitoringData.systemHealthy !== false;
    const healthMessage = monitoringData.healthMessage || '系统运行正常';

    const html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div>
                <h3 style="margin: 0;">实时监控</h3>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    <span id="refreshStatus">监控状态</span>
                </div>
            </div>
            <div>
                <button class="refresh-btn" onclick="universalDataRefresh()" id="universalRefreshBtn">
                    <span id="refreshIcon">🔄</span> 数据刷新
                </button>
                <button class="refresh-btn" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                    ${isMonitoringActive ? '停止自动刷新' : '开启自动刷新'}
                </button>
            </div>
        </div>

        <!-- 系统健康状态 -->
        <div class="health-indicator ${systemHealthy ? 'healthy' : 'unhealthy'}">
            <span style="font-size: 20px;">${systemHealthy ? '✅' : '⚠️'}</span>
            <div>
                <strong>系统状态: ${systemHealthy ? '健康' : '异常'}</strong>
                <div style="font-size: 14px; margin-top: 5px;">${healthMessage}</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    更新时间: ${monitoringData.monitoringTime || new Date().toLocaleString()}
                </div>
            </div>
        </div>

        <!-- 实时指标 - 优化版本，删除无意义指标，添加重要指标 -->
        <div class="monitoring-grid">
            <div class="monitoring-card">
                <h4>📊 请求统计 (最近1小时)</h4>
                <div class="metric-value primary">${realTime.totalRequests || 0}</div>
                <div style="font-size: 14px; color: #666;">总请求数</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px; display: flex; justify-content: space-between;">
                        <span>成功: <span class="metric-value success" style="font-size: 16px;">${realTime.successRequests || 0}</span></span>
                        <span>失败: <span class="metric-value danger" style="font-size: 16px;">${realTime.errorRequests || 0}</span></span>
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        QPS: ${((realTime.totalRequests || 0) / 3600).toFixed(2)}/秒
                    </div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>⚡ 响应性能</h4>
                <div class="metric-value ${getPerformanceClass(realTime.avgResponseTime)}">${realTime.avgResponseTime || 0}ms</div>
                <div style="font-size: 14px; color: #666;">平均响应时间</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px; display: flex; justify-content: space-between;">
                        <span>最大: <span style="font-weight: bold; color: ${(realTime.maxResponseTime || 0) > 1000 ? '#dc3545' : '#28a745'};">${realTime.maxResponseTime || 0}ms</span></span>
                        <span>最小: <span style="font-weight: bold;">${realTime.minResponseTime || 0}ms</span></span>
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        ${(realTime.maxResponseTime || 0) > 1000 ? '⚠️ 存在慢请求' : '✅ 响应正常'}
                    </div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>📈 可用性指标</h4>
                <div class="metric-value ${getSuccessRateClass(realTime.successRate)}">${realTime.successRate || 0}%</div>
                <div style="font-size: 14px; color: #666;">系统可用性</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px; display: flex; justify-content: space-between;">
                        <span>错误率: <span class="metric-value ${(realTime.errorRate || 0) > 5 ? 'danger' : 'success'}" style="font-size: 16px;">${realTime.errorRate || 0}%</span></span>
                        <span>SLA: <span style="font-weight: bold; color: ${(realTime.successRate || 0) >= 99.9 ? '#28a745' : (realTime.successRate || 0) >= 99 ? '#ffc107' : '#dc3545'};">${(realTime.successRate || 0) >= 99.9 ? '优秀' : (realTime.successRate || 0) >= 99 ? '良好' : '需改进'}</span></span>
                    </div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>👥 用户活跃度</h4>
                <div class="metric-value primary">${realTime.activeUsers || 0}</div>
                <div style="font-size: 14px; color: #666;">活跃用户数</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px; display: flex; justify-content: space-between;">
                        <span>独立IP: <span style="font-weight: bold;">${realTime.uniqueIps || 0}</span></span>
                        <span>平均请求: <span style="font-weight: bold;">${realTime.activeUsers > 0 ? ((realTime.totalRequests || 0) / realTime.activeUsers).toFixed(1) : 0}</span></span>
                    </div>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        ${realTime.activeUsers > 0 ? '🟢 用户活跃' : '🟡 用户较少'}
                    </div>
                </div>
            </div>
        </div>

        <!-- 热点数据 -->
        <div class="monitoring-grid">
            <div class="monitoring-card">
                <h4>🔥 热点URL (最近1小时)</h4>
                <div id="hotUrls">
                    ${formatHotUrls(statisticsData.hotUrls || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🚨 最近错误</h4>
                <div id="recentErrors">
                    ${formatRecentErrors(statisticsData.recentErrors || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🐌 慢请求监控</h4>
                <div id="slowRequests">
                    ${formatSlowRequests(statisticsData.slowRequests || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🌍 访问来源</h4>
                <div id="topIps">
                    ${formatTopIps(statisticsData.topIps || [])}
                </div>
            </div>
        </div>

        <!-- 趋势图表区域 -->
        <div class="monitoring-card" style="margin-top: 20px;">
            <h4>📊 24小时访问趋势</h4>
            <div id="trendChart">
                ${formatTrendData(statisticsData.hourlyTrend || [])}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * 获取性能等级样式类
 */
function getPerformanceClass(responseTime) {
    if (!responseTime) return 'primary';
    if (responseTime < 200) return 'success';
    if (responseTime < 1000) return 'warning';
    return 'danger';
}

/**
 * 获取成功率样式类
 */
function getSuccessRateClass(successRate) {
    if (!successRate) return 'danger';
    if (successRate >= 95) return 'success';
    if (successRate >= 90) return 'warning';
    return 'danger';
}

/**
 * 格式化热点URL
 */
function formatHotUrls(hotUrls) {
    console.log('🔥 格式化热点URL数据:', hotUrls);

    if (!hotUrls || hotUrls.length === 0) {
        return '<div style="color: #666; font-size: 14px; text-align: center; padding: 10px;">暂无热点URL数据<br><small>可能需要执行数据刷新</small></div>';
    }

    return hotUrls.slice(0, 5).map((item, index) => {
        // 兼容不同的字段名
        const url = item.url || item.requestUrl || item.request_url || '-';
        const count = item.count || item.accessCount || item.access_count || item.visitCount || item.visit_count || 0;

        console.log(`热点URL ${index + 1}:`, { url, count, originalItem: item });

        return `
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                <span style="font-size: 14px; color: #333; flex: 1; margin-right: 10px;" title="${url}">${index + 1}. ${truncateText(url, 35)}</span>
                <span style="font-weight: bold; color: #667eea; min-width: 40px; text-align: right;">${count}</span>
            </div>
        `;
    }).join('');
}

/**
 * 格式化最近错误
 */
function formatRecentErrors(recentErrors) {
    if (!recentErrors || recentErrors.length === 0) {
        return '<div style="color: #28a745; font-size: 14px;">✅ 暂无错误</div>';
    }

    return recentErrors.slice(0, 3).map(error => `
        <div style="padding: 8px; margin: 5px 0; background: #f8d7da; border-radius: 5px; border-left: 3px solid #dc3545;">
            <div style="font-size: 12px; color: #721c24; font-weight: bold;">
                ${error.requestMethod || '-'} ${truncateText(error.requestUrl || '-', 25)}
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                ${error.requestStartTime ? new Date(error.requestStartTime).toLocaleTimeString() : '-'}
            </div>
            ${error.errorMessage ? `
            <div style="font-size: 11px; color: #721c24; margin-top: 3px;">
                ${truncateText(error.errorMessage, 40)}
            </div>
            ` : ''}
        </div>
    `).join('');
}

/**
 * 格式化慢请求
 */
function formatSlowRequests(slowRequests) {
    if (!slowRequests || slowRequests.length === 0) {
        return '<div style="color: #28a745; font-size: 14px;">✅ 暂无慢请求</div>';
    }

    return slowRequests.slice(0, 3).map(slow => `
        <div style="padding: 8px; margin: 5px 0; background: #fff3cd; border-radius: 5px; border-left: 3px solid #ffc107;">
            <div style="font-size: 12px; color: #856404; font-weight: bold;">
                ${slow.requestMethod || '-'} ${truncateText(slow.requestUrl || '-', 25)}
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                响应时间: <span style="font-weight: bold;">${slow.responseTime || 0}ms</span>
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                ${slow.requestStartTime ? new Date(slow.requestStartTime).toLocaleTimeString() : '-'}
            </div>
        </div>
    `).join('');
}

/**
 * 格式化访问来源IP
 */
function formatTopIps(topIps) {
    if (!topIps || topIps.length === 0) {
        return '<div style="color: #666; font-size: 14px;">暂无数据</div>';
    }

    return topIps.slice(0, 5).map((item, index) => `
        <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee;">
            <span style="font-size: 14px; color: #333;">${index + 1}. ${item.ip || item.requestIp || '-'}</span>
            <span style="font-weight: bold; color: #667eea;">${item.count || item.accessCount || 0}</span>
        </div>
    `).join('');
}

/**
 * 格式化趋势数据
 */
function formatTrendData(hourlyTrend) {
    console.log('📊 格式化24小时访问趋势数据:', hourlyTrend);

    if (!hourlyTrend || hourlyTrend.length === 0) {
        return '<div style="color: #666; font-size: 14px; text-align: center; padding: 20px;">暂无24小时访问趋势数据<br><small>可能需要执行数据刷新</small></div>';
    }

    // 兼容不同的字段名，简单的文本图表显示
    const counts = hourlyTrend.map(item => {
        return item.count || item.accessCount || item.access_count || item.visitCount || item.visit_count || 0;
    });
    const maxCount = Math.max(...counts);

    console.log('📊 趋势数据统计:', {
        totalItems: hourlyTrend.length,
        maxCount,
        sampleItem: hourlyTrend[0]
    });

    return `
        <div style="font-family: 'Courier New', monospace; font-size: 12px; background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 300px; overflow-y: auto;">
            <div style="margin-bottom: 10px; font-weight: bold; color: #495057;">时间段 │ 访问量</div>
            ${hourlyTrend.map((item, index) => {
                // 兼容不同的字段名
                const count = item.count || item.accessCount || item.access_count || item.visitCount || item.visit_count || 0;
                const percentage = maxCount > 0 ? (count / maxCount * 100) : 0;
                const barLength = Math.max(1, Math.floor(percentage / 3)); // 调整条形图长度
                const bar = '█'.repeat(barLength);

                // 兼容不同的时间字段名
                let timeDisplay = item.hour || item.time || item.stat_time || item.statTime;
                if (!timeDisplay) {
                    // 如果没有时间字段，生成一个基于索引的时间
                    const now = new Date();
                    now.setHours(now.getHours() - (hourlyTrend.length - 1 - index));
                    timeDisplay = now.getHours().toString().padStart(2, '0') + ':00';
                }

                return `<div style="margin: 2px 0; color: ${count > 0 ? '#495057' : '#6c757d'};">${timeDisplay.toString().padEnd(8)} │${bar.padEnd(20)} ${count}</div>`;
            }).join('')}
        </div>
    `;
}

/**
 * 启动实时监控
 */
function startRealTimeMonitoring() {
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
    }

    isMonitoringActive = true;
    refreshCount = 0;
    updateAutoRefreshButton();

    // 每30秒刷新一次监控数据
    monitoringTimer = setInterval(() => {
        refreshMonitoring();
    }, 30000);

    // 强制刷新机制：每10秒检查一次，如果超过2分钟没有刷新则强制刷新
    forceRefreshTimer = setInterval(() => {
        refreshCount++;

        // 每10秒更新一次刷新计数显示
        updateRefreshStatus();

        // 如果超过2分钟（12个10秒周期）没有正常刷新，强制刷新
        if (refreshCount >= 12) {
            console.warn('检测到监控数据可能停止更新，执行强制刷新');
            forceRefreshMonitoring();
            refreshCount = 0;
        }
    }, 10000);

    console.log('实时监控已启动，每30秒自动刷新，每10秒检查状态');
}

/**
 * 停止实时监控
 */
function stopRealTimeMonitoring() {
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
        monitoringTimer = null;
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
        forceRefreshTimer = null;
    }

    isMonitoringActive = false;
    refreshCount = 0;
    updateAutoRefreshButton();
    updateRefreshStatus();

    console.log('实时监控已停止');
}

/**
 * 切换自动刷新
 */
function toggleAutoRefresh() {
    if (isMonitoringActive) {
        stopRealTimeMonitoring();
    } else {
        startRealTimeMonitoring();
    }
}

/**
 * 更新自动刷新按钮状态
 */
function updateAutoRefreshButton() {
    const button = document.getElementById('autoRefreshBtn');
    if (button) {
        button.textContent = isMonitoringActive ? '停止自动刷新' : '开启自动刷新';
        button.style.background = isMonitoringActive ? '#dc3545' : '#17a2b8';
    }
}

/**
 * 通用数据刷新 - 整合所有刷新功能
 */
async function universalDataRefresh() {
    console.log('🔄 开始执行通用数据刷新');

    const refreshBtn = document.getElementById('universalRefreshBtn');
    const refreshIcon = document.getElementById('refreshIcon');

    // 更新按钮状态
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<span id="refreshIcon">⏳</span> 刷新中...';
    }

    if (refreshIcon) {
        refreshIcon.style.animation = 'spin 2s linear infinite';
    }

    try {
        // 第一步：执行统计数据刷新任务（清理过期数据、刷新缓存）
        console.log('📊 步骤1: 执行统计数据刷新任务');
        await executeStatisticsRefresh();

        // 等待1秒让后端处理完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 第二步：强制刷新监控和统计数据
        console.log('🔄 步骤2: 强制刷新监控数据');
        await forceRefreshData();

        // 第三步：重新加载页面数据
        console.log('📈 步骤3: 重新加载页面数据');
        await refreshPageData();

        showForceRefreshNotification('数据刷新成功！热点URL和访问趋势已更新', 'success');
        console.log('✅ 通用数据刷新完成');

    } catch (error) {
        console.error('❌ 通用数据刷新失败:', error);
        showForceRefreshNotification('数据刷新失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<span id="refreshIcon">🔄</span> 数据刷新';
        }
        if (refreshIcon) {
            refreshIcon.style.animation = '';
        }
    }
}

/**
 * 执行统计数据刷新任务
 */
async function executeStatisticsRefresh() {
    const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/refresh-statistics`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`统计数据刷新失败: HTTP ${response.status}`);
    }

    const result = await response.json();
    if (result.code !== 200) {
        throw new Error(result.message || '统计数据刷新失败');
    }

    const data = result.data;
    const cleanedRecords = data.cleanedRecords || 0;
    console.log('📊 统计数据刷新完成:', {
        cleanedRecords: cleanedRecords,
        cacheRefreshed: data.cacheRefreshed,
        refreshTime: data.refreshTime
    });

    return data;
}

/**
 * 强制刷新数据
 */
async function forceRefreshData() {
    const timestamp = Date.now();
    const [monitoringResponse, statisticsResponse] = await Promise.all([
        makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring?_t=${timestamp}`),
        makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics?_t=${timestamp}`)
    ]);

    const monitoringResult = await monitoringResponse.json();
    const statisticsResult = await statisticsResponse.json();

    if (monitoringResult.code !== 200 || statisticsResult.code !== 200) {
        throw new Error('强制刷新数据失败');
    }

    return { monitoringResult, statisticsResult };
}

/**
 * 重新加载页面数据
 */
async function refreshPageData() {
    // 重新加载监控数据
    await loadMonitoring();

    // 如果当前在统计页面，也刷新统计数据
    const activeTab = document.querySelector('.tab-btn.active');
    if (activeTab && activeTab.textContent.includes('统计分析')) {
        await loadStatistics();
    }
}

/**
 * 手动刷新监控数据
 */
async function refreshMonitoring() {
    const refreshIcon = document.getElementById('refreshIcon');
    if (refreshIcon) {
        refreshIcon.style.animation = 'spin 1s linear infinite';
    }

    try {
        // 重新加载监控数据
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('刷新时发现统计数据问题:', dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            refreshCount = 0; // 重置刷新计数
            updateRefreshStatus();
            console.log('监控数据刷新成功:', new Date().toLocaleTimeString());
        } else {
            console.error('监控数据刷新失败');
        }
    } catch (error) {
        console.error('刷新监控数据失败:', error);
    } finally {
        if (refreshIcon) {
            refreshIcon.style.animation = '';
        }
    }
}

/**
 * 强制刷新监控数据
 */
async function forceRefreshMonitoring() {
    console.log('执行强制刷新监控数据');

    // 显示强制刷新状态
    const refreshIcon = document.getElementById('refreshIcon');
    if (refreshIcon) {
        refreshIcon.style.animation = 'spin 2s linear infinite';
        refreshIcon.style.color = '#dc3545'; // 红色表示强制刷新
    }

    try {
        // 添加时间戳参数强制刷新
        const timestamp = Date.now();
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring?_t=${timestamp}`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics?_t=${timestamp}`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('强制刷新时发现统计数据问题:', dataIssues);
                showDataIssuesNotification(dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            refreshCount = 0; // 重置刷新计数
            updateRefreshStatus();
            console.log('强制刷新监控数据成功:', new Date().toLocaleTimeString());

            // 显示强制刷新成功提示
            showForceRefreshNotification('强制刷新成功');
        } else {
            console.error('强制刷新监控数据失败');
            showForceRefreshNotification('强制刷新失败', 'error');
        }
    } catch (error) {
        console.error('强制刷新监控数据失败:', error);
        showForceRefreshNotification('强制刷新失败: ' + error.message, 'error');
    } finally {
        if (refreshIcon) {
            refreshIcon.style.animation = '';
            refreshIcon.style.color = ''; // 恢复原色
        }
    }
}

/**
 * 更新刷新状态显示
 */
function updateRefreshStatus() {
    const statusElement = document.getElementById('refreshStatus');
    if (statusElement) {
        if (isMonitoringActive) {
            const nextRefresh = 30 - (refreshCount * 10) % 30;
            statusElement.textContent = `下次刷新: ${nextRefresh}秒`;
            statusElement.style.color = refreshCount >= 10 ? '#dc3545' : '#28a745';
        } else {
            statusElement.textContent = '监控已停止';
            statusElement.style.color = '#6c757d';
        }
    }
}

/**
 * 显示强制刷新通知
 */
function showForceRefreshNotification(message, type = 'success') {
    const notification = document.createElement('div');

    let backgroundColor, textColor;
    switch (type) {
        case 'error':
            backgroundColor = '#dc3545';
            textColor = 'white';
            break;
        case 'warning':
            backgroundColor = '#ffc107';
            textColor = '#212529';
            break;
        case 'success':
        default:
            backgroundColor = '#28a745';
            textColor = 'white';
            break;
    }

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 20px;
        border-radius: 5px;
        color: ${textColor};
        font-weight: bold;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
        background: ${backgroundColor};
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 执行统计数据刷新任务
 */
async function executeJobTask() {
    console.log('开始执行统计数据刷新任务');

    const jobTaskIcon = document.getElementById('jobTaskIcon');
    if (jobTaskIcon) {
        jobTaskIcon.style.animation = 'spin 2s linear infinite';
    }

    try {
        // 调用统计数据刷新API
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/refresh-statistics`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            const data = result.data;

            // 显示执行结果
            const cleanedRecords = data.cleanedRecords || 0;
            const cacheRefreshed = data.cacheRefreshed || false;

            let message = '统计数据刷新成功';
            if (cleanedRecords > 0) {
                message += ` (清理${cleanedRecords}条过期记录)`;
            }

            showForceRefreshNotification(message, 'success');

            console.log('统计数据刷新完成:', {
                cleanedRecords: cleanedRecords,
                cacheRefreshed: cacheRefreshed,
                refreshTime: data.refreshTime
            });

            // 等待一段时间后刷新监控数据
            setTimeout(() => {
                refreshMonitoring();
            }, 1000);

        } else {
            throw new Error(result.message || '统计数据刷新失败');
        }

    } catch (error) {
        console.error('执行统计数据刷新失败:', error);
        showForceRefreshNotification('统计数据刷新失败: ' + error.message, 'error');
    } finally {
        if (jobTaskIcon) {
            jobTaskIcon.style.animation = '';
        }
    }
}



/**
 * 验证统计数据是否存在问题
 */
function validateStatisticsData(statisticsData) {
    const issues = [];

    if (!statisticsData) {
        issues.push('统计数据为空');
        return issues;
    }

    // 检查基础统计数据
    const basicStats = statisticsData.basicStats || {};

    // 检查总记录数是否合理
    const totalRecords = basicStats.totalRecords || 0;
    if (totalRecords < 0) {
        issues.push('总记录数为负数');
    }

    // 检查成功率是否合理
    const successRate = basicStats.successRate || 0;
    if (successRate < 0 || successRate > 100) {
        issues.push(`成功率异常: ${successRate}%`);
    }

    // 检查错误率是否过高
    const errorRate = basicStats.errorRate || 0;
    if (errorRate > 50) {
        issues.push(`错误率过高: ${errorRate}%`);
    }

    // 检查平均响应时间是否异常
    const avgResponseTime = basicStats.avgResponseTime || 0;
    if (avgResponseTime < 0) {
        issues.push('平均响应时间为负数');
    } else if (avgResponseTime > 10000) {
        issues.push(`平均响应时间过长: ${avgResponseTime}ms`);
    }

    // 检查实时数据
    const realTime = statisticsData.realTime || {};
    const realtimeTotal = realTime.totalRequests || 0;
    const realtimeSuccess = realTime.successRequests || 0;
    const realtimeError = realTime.errorRequests || 0;

    // 检查实时数据一致性
    if (realtimeTotal !== (realtimeSuccess + realtimeError)) {
        issues.push('实时统计数据不一致');
    }

    // 检查实时成功率
    const realtimeSuccessRate = realTime.successRate || 0;
    if (realtimeTotal > 0) {
        const calculatedRate = (realtimeSuccess / realtimeTotal) * 100;
        if (Math.abs(calculatedRate - realtimeSuccessRate) > 1) {
            issues.push('实时成功率计算错误');
        }
    }

    // 检查热点数据
    const hotUrls = statisticsData.hotUrls || [];
    if (Array.isArray(hotUrls)) {
        hotUrls.forEach((item, index) => {
            if (!item.url && !item.requestUrl) {
                issues.push(`热点URL第${index + 1}项缺少URL字段`);
            }
            if (!item.count && !item.accessCount) {
                issues.push(`热点URL第${index + 1}项缺少计数字段`);
            }
        });
    }

    // 检查趋势数据
    const hourlyTrend = statisticsData.hourlyTrend || [];
    if (Array.isArray(hourlyTrend)) {
        hourlyTrend.forEach((item, index) => {
            const count = item.count || item.accessCount || 0;
            if (count < 0) {
                issues.push(`趋势数据第${index + 1}项计数为负数`);
            }
        });
    }

    // 检查IP统计
    const topIps = statisticsData.topIps || [];
    if (Array.isArray(topIps)) {
        topIps.forEach((item, index) => {
            if (!item.ip && !item.requestIp) {
                issues.push(`IP统计第${index + 1}项缺少IP字段`);
            }
        });
    }

    return issues;
}

/**
 * 显示数据问题通知
 */
function showDataIssuesNotification(issues) {
    if (!issues || issues.length === 0) return;

    const message = `发现${issues.length}个数据问题: ${issues.slice(0, 3).join(', ')}${issues.length > 3 ? '...' : ''}`;
    showForceRefreshNotification(message, 'warning');

    // 在控制台输出详细信息
    console.group('📊 统计数据问题详情');
    issues.forEach((issue, index) => {
        console.warn(`${index + 1}. ${issue}`);
    });
    console.groupEnd();
}

/**
 * 启动Token状态监控
 */
function startTokenStatusMonitor() {
    // 获取Token过期时间
    const expireTimeStr = sessionStorage.getItem('accessLogTokenExpire');
    if (expireTimeStr) {
        tokenExpireTime = parseInt(expireTimeStr);
    }

    // 更新状态显示
    updateTokenStatus();

    // 启动定时器，每秒更新一次
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
    }

    tokenStatusTimer = setInterval(() => {
        updateTokenStatus();
    }, 1000);
}

/**
 * 更新Token状态显示
 */
function updateTokenStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const expireTime = document.getElementById('tokenExpireTime');

    if (!tokenExpireTime) {
        statusText.textContent = '已认证';
        expireTime.textContent = '永久有效';
        statusDot.className = 'status-dot';
        return;
    }

    const now = Date.now();
    const remainingTime = tokenExpireTime - now;

    if (remainingTime <= 0) {
        // Token已过期
        statusText.textContent = '已过期';
        expireTime.textContent = '00:00:00';
        statusDot.className = 'status-dot danger';

        // Token过期时显示认证区域，而不是跳转到登录页面
        console.log('🔴 Token已过期，显示认证区域');
        showAuthSection();
        return;
    }

    // 计算剩余时间
    const hours = Math.floor(remainingTime / (1000 * 60 * 60));
    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    expireTime.textContent = timeStr;

    // 根据剩余时间设置状态
    if (remainingTime < 5 * 60 * 1000) { // 少于5分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot danger';
    } else if (remainingTime < 15 * 60 * 1000) { // 少于15分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot warning';
    } else {
        statusText.textContent = '已认证';
        statusDot.className = 'status-dot';
    }
}

/**
 * 退出登录（点击退出按钮时跳转到登录页面）
 */
function logout() {
    console.log('🚪 用户点击退出按钮，跳转到登录页面');

    // 清除Token状态监控
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
        tokenStatusTimer = null;
    }

    // 清除实时监控
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
        monitoringTimer = null;
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
        forceRefreshTimer = null;
    }
    isMonitoringActive = false;
    refreshCount = 0;

    // 清除存储的Token信息
    sessionStorage.removeItem('accessLogToken');
    sessionStorage.removeItem('accessLogTokenExpire');

    // 重置全局变量
    accessToken = '';
    isAuthenticated = false;
    tokenExpireTime = 0;

    // 跳转到登录页面
    window.location.href = '/api/access-log-management/login.html';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('detailModal');
    if (event.target === modal) {
        closeDetailModal();
    }
}
