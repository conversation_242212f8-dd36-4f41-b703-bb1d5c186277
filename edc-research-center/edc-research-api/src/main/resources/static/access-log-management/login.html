<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问日志管理 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .step-line {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 10px;
            transition: all 0.3s;
        }

        .step-line.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .info-text {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
            border-radius: 0 8px 8px 0;
        }

        .info-text code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background: #fdf2f2;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background: #f0f9f0;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
            display: none;
        }

        .loading {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .hidden {
            display: none !important;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-btn {
            background: #6c757d;
            margin-top: 10px;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .step-line {
                width: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>访问日志管理</h1>
            <p>安全登录 · 两步验证</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step-line" id="line1"></div>
            <div class="step" id="step2">2</div>
            <div class="step-line" id="line2"></div>
            <div class="step" id="step3">3</div>
        </div>

        <!-- 第一步：输入配置秘钥 -->
        <div id="step1-form" class="form-step active">
            <div class="info-text">
                <strong>步骤 1:</strong> 请输入系统配置秘钥
            </div>
            <div class="form-group">
                <label for="secretKey">配置秘钥</label>
                <input type="password" id="secretKey" placeholder="请输入配置秘钥" required>
            </div>
            <button class="btn" onclick="verifySecretKey()">
                <span id="step1-loading" class="loading hidden"></span>
                验证秘钥
            </button>
            <div id="step1-error" class="error-message"></div>
        </div>

        <!-- 第二步：Token获取 -->
        <div id="step2-form" class="form-step">
            <!-- 自动生成Token模式 -->
            <div id="auto-token-mode" style="display: none;">
                <div class="info-text">
                    <strong>步骤 2:</strong> 正在自动生成访问令牌，请稍候...
                </div>
                <div class="progress-container" style="margin: 20px 0;">
                    <div class="progress-bar" style="width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden;">
                        <div class="progress-fill" id="progress-fill" style="height: 100%; background-color: #007bff; width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                    <div class="progress-text" id="progress-text" style="text-align: center; margin-top: 10px; color: #666;">正在验证应用凭证...</div>
                </div>
                <button class="btn" onclick="verifyToken()">
                    <span id="step2-loading" class="loading hidden"></span>
                    开始验证
                </button>
            </div>

            <!-- 手动输入Token模式 -->
            <div id="manual-token-mode" style="display: none;">
                <div class="info-text">
                    <strong>步骤 2:</strong> 请输入验证码和刷新码获取访问令牌
                </div>
                <div class="form-group">
                    <label for="code">验证码 (Code)</label>
                    <input type="text" id="code" placeholder="请输入验证码" required>
                </div>
                <div class="form-group">
                    <label for="refreshCode">刷新码 (RefreshCode)</label>
                    <input type="text" id="refreshCode" placeholder="请输入刷新码" required>
                </div>
                <button class="btn" onclick="getAccessTokenManually()">
                    <span id="manual-loading" class="loading hidden"></span>
                    获取访问令牌
                </button>
            </div>

            <button class="btn back-btn" onclick="goBack()">返回上一步</button>
            <div id="step2-error" class="error-message"></div>
        </div>

        <!-- 第三步：登录成功 -->
        <div id="step3-form" class="form-step">
            <div class="success-message" style="display: block;">
                <strong>登录成功！</strong> 正在跳转到访问日志管理页面...
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let accessToken = '';
        let authConfig = null;

        // 页面加载时获取配置信息
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/access-log/auth/config');
                const result = await response.json();
                if (result.code === 200) {
                    authConfig = result.data;
                    console.log('获取到认证配置:', authConfig);
                } else {
                    console.warn('获取认证配置失败:', result.message);
                    // 使用默认配置
                    authConfig = { autoGenerateToken: false };
                }
            } catch (error) {
                console.error('获取认证配置失败:', error);
                // 使用默认配置
                authConfig = { autoGenerateToken: false };
            }
        });

        // 验证配置秘钥
        async function verifySecretKey() {
            const secretKey = document.getElementById('secretKey').value.trim();
            
            if (!secretKey) {
                showError('step1-error', '请输入配置秘钥');
                return;
            }

            showLoading('step1-loading', true);
            hideError('step1-error');

            try {
                const response = await fetch('/api/access-log/auth/verify-secret', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ secretKey: secretKey })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    nextStep();
                } else {
                    showError('step1-error', result.message || '秘钥验证失败');
                }
            } catch (error) {
                console.error('验证配置秘钥失败:', error);
                showError('step1-error', '网络错误，请稍后重试');
            } finally {
                showLoading('step1-loading', false);
            }
        }

        // 自动生成并验证Token
        async function verifyToken() {
            showLoading('step2-loading', true);
            hideError('step2-error');
            updateProgress(0, '正在生成验证码...');

            try {
                // 第一步：生成Code和RefreshCode
                const generateResponse = await fetch('/api/secure/token/generateCode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "appId": authConfig.devAppId || "edc_app_dev",
                        "appSecret": authConfig.devAppSecret || "edc_secret_dev_2025_abcdef123456",
                        "environment": authConfig.devEnvironment || "dev",
                        "extraInfo": "access-log-management",
                        "userId": "user123"
                    })
                });

                const generateResult = await generateResponse.json();
                if (generateResult.code !== 200 || !generateResult.data) {
                    throw new Error(generateResult.message || '生成验证码失败');
                }

                updateProgress(50, '正在获取访问令牌...');

                // 第二步：使用Code和RefreshCode获取AccessToken
                const tokenResponse = await fetch('/api/access-log/auth/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: generateResult.data.code,
                        refreshCode: generateResult.data.refreshCode
                    })
                });

                const tokenResult = await tokenResponse.json();

                if (tokenResult.code === 200 && tokenResult.data && tokenResult.data.accessToken) {
                    accessToken = tokenResult.data.accessToken;
                    // 存储 AccessToken 到 sessionStorage (使用与管理页面一致的键名)
                    sessionStorage.setItem('accessLogToken', tokenResult.data.accessToken);
                    // 设置过期时间
                    if (tokenResult.data.expiresIn) {
                        const expireTime = Date.now() + (tokenResult.data.expiresIn * 1000);
                        sessionStorage.setItem('accessLogTokenExpire', expireTime.toString());
                    }
                    console.log('AccessToken 已存储到 sessionStorage:', tokenResult.data.accessToken);

                    updateProgress(100, '验证成功！正在跳转...');
                    nextStep();

                    // 跳转到管理页面，在URL中传递AccessToken
                    setTimeout(() => {
                        window.location.href = `/api/access-log/management?accessToken=${encodeURIComponent(tokenResult.data.accessToken)}`;
                    }, 2000);
                } else {
                    throw new Error(tokenResult.message || 'Token验证失败');
                }
            } catch (error) {
                console.error('验证Token失败:', error);
                showError('step2-error', error.message || '验证失败，请重试');
            } finally {
                showLoading('step2-loading', false);
            }
        }

        // 手动获取访问令牌
        async function getAccessTokenManually() {
            const code = document.getElementById('code').value.trim();
            const refreshCode = document.getElementById('refreshCode').value.trim();

            if (!code || !refreshCode) {
                showError('step2-error', '请输入验证码和刷新码');
                return;
            }

            showLoading('manual-loading', true);
            hideError('step2-error');

            try {
                const response = await fetch('/api/access-log/auth/verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        refreshCode: refreshCode
                    })
                });

                const result = await response.json();

                if (result.code === 200 && result.data && result.data.accessToken) {
                    accessToken = result.data.accessToken;
                    // 存储 AccessToken 到 sessionStorage
                    sessionStorage.setItem('accessToken', result.data.accessToken);
                    console.log('AccessToken 已存储到 sessionStorage:', result.data.accessToken);

                    nextStep();

                    // 跳转到管理页面，在URL中传递AccessToken
                    setTimeout(() => {
                        window.location.href = `/api/access-log/management?accessToken=${encodeURIComponent(result.data.accessToken)}`;
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Token验证失败');
                }
            } catch (error) {
                console.error('手动获取访问令牌失败:', error);
                showError('step2-error', error.message || '获取访问令牌失败，请重试');
            } finally {
                showLoading('manual-loading', false);
            }
        }

        // 更新进度显示
        function updateProgress(percent, text) {
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            if (progressFill) {
                progressFill.style.width = percent + '%';
            }
            if (progressText) {
                progressText.textContent = text;
            }
        }

        // 下一步
        function nextStep() {
            if (currentStep < 3) {
                // 隐藏当前步骤
                document.getElementById(`step${currentStep}-form`).classList.remove('active');

                // 更新步骤指示器
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                if (currentStep < 3) {
                    document.getElementById(`line${currentStep}`).classList.add('active');
                }

                currentStep++;

                // 显示下一步骤
                document.getElementById(`step${currentStep}-form`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');

                // 如果是第二步，根据配置显示不同的模式
                if (currentStep === 2 && authConfig) {
                    if (authConfig.autoGenerateToken) {
                        // 显示自动生成模式
                        document.getElementById('auto-token-mode').style.display = 'block';
                        document.getElementById('manual-token-mode').style.display = 'none';
                    } else {
                        // 显示手动输入模式
                        document.getElementById('auto-token-mode').style.display = 'none';
                        document.getElementById('manual-token-mode').style.display = 'block';
                    }
                }
            }
        }

        // 返回上一步
        function goBack() {
            if (currentStep > 1) {
                // 隐藏当前步骤
                document.getElementById(`step${currentStep}-form`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                currentStep--;
                
                // 显示上一步骤
                document.getElementById(`step${currentStep}-form`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
                document.getElementById(`step${currentStep}`).classList.add('active');
                if (currentStep < 3) {
                    document.getElementById(`line${currentStep}`).classList.remove('active');
                }
            }
        }

        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError(elementId) {
            const errorElement = document.getElementById(elementId);
            errorElement.style.display = 'none';
        }

        // 显示/隐藏加载动画
        function showLoading(elementId, show) {
            const loadingElement = document.getElementById(elementId);
            if (show) {
                loadingElement.classList.remove('hidden');
            } else {
                loadingElement.classList.add('hidden');
            }
        }

        // 回车键提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (currentStep === 1) {
                    verifySecretKey();
                } else if (currentStep === 2) {
                    verifyToken();
                }
            }
        });
    </script>
</body>
</html>
