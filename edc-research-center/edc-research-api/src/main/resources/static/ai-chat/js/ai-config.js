// AI模型配置管理

// 配置axios请求拦截器，自动添加用户身份信息
axios.interceptors.request.use(function (config) {
    // 尝试从多个来源获取用户ID
    let userId = null;
    let userName = null;
    let realName = null;

    // 1. 从sessionStorage获取（如果有的话）
    try {
        const userInfo = sessionStorage.getItem('userInfo');
        if (userInfo) {
            const user = JSON.parse(userInfo);
            userId = user.userId || user.id;
            userName = user.userName || user.username;
            realName = user.realName || user.name;
        }
    } catch (e) {
        console.debug('从sessionStorage获取用户信息失败:', e);
    }

    // 2. 从localStorage获取（如果有的话）
    if (!userId) {
        try {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                const user = JSON.parse(userInfo);
                userId = user.userId || user.id;
                userName = user.userName || user.username;
                realName = user.realName || user.name;
            }
        } catch (e) {
            console.debug('从localStorage获取用户信息失败:', e);
        }
    }

    // 3. 从cookie获取（如果有的话）
    if (!userId) {
        try {
            const cookies = document.cookie.split(';');
            let accessToken = null;

            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'userId') {
                    userId = decodeURIComponent(value);
                } else if (name === 'userName') {
                    userName = decodeURIComponent(value);
                } else if (name === 'realName') {
                    realName = decodeURIComponent(value);
                } else if (name === 'access_token') {
                    accessToken = decodeURIComponent(value);
                }
            }

            // 如果有access_token但没有直接的用户信息，设置token到请求头
            if (accessToken && !userId) {
                console.debug('AI配置页面从cookie获取到access_token，将设置到请求头');
                config.headers['X-Access-Token'] = accessToken;
            }
        } catch (e) {
            console.debug('从cookie获取用户信息失败:', e);
        }
    }

    // 4. 从全局变量获取（如果页面设置了的话）
    if (!userId && typeof window.currentUser !== 'undefined') {
        userId = window.currentUser.userId || window.currentUser.id;
        userName = window.currentUser.userName || window.currentUser.username;
        realName = window.currentUser.realName || window.currentUser.name;
    }

    // 设置请求头
    if (userId) {
        config.headers['X-User-Id'] = userId;
        console.debug('AI配置页面设置请求头 X-User-Id:', userId);
    }
    if (userName) {
        config.headers['X-User-Name'] = userName;
        console.debug('AI配置页面设置请求头 X-User-Name:', userName);
    }
    if (realName) {
        config.headers['X-Real-Name'] = realName;
        console.debug('AI配置页面设置请求头 X-Real-Name:', realName);
    }

    return config;
}, function (error) {
    return Promise.reject(error);
});

new Vue({
    el: '#app',
    data() {
        return {
            loading: false,
            saving: false,
            modelConfigs: [],
            
            // 对话框相关
            dialogVisible: false,
            dialogTitle: '添加模型',
            isEdit: false,
            currentModelId: null,
            
            // 表单数据
            modelForm: {
                modelType: '',
                modelName: '',
                apiKey: '',
                apiUrl: '',
                maxTokens: 4000,
                temperature: 0.7,
                topP: 0.9,
                frequencyPenalty: 0.0,
                presencePenalty: 0.0,
                systemPrompt: 'You are a helpful assistant.',
                tokenPriceInput: 0.000002,
                tokenPriceOutput: 0.000006,
                rateLimitRpm: 60,
                rateLimitTpm: 60000,
                enabled: true,
                priority: 0,
                description: ''
            },
            
            // 表单验证规则
            formRules: {
                modelType: [
                    { required: true, message: '请选择模型类型', trigger: 'change' }
                ],
                modelName: [
                    { required: true, message: '请输入模型名称', trigger: 'blur' }
                ],
                apiKey: [
                    { required: true, message: '请输入API密钥', trigger: 'blur' }
                ],
                maxTokens: [
                    { required: true, message: '请输入最大Token数', trigger: 'blur' }
                ],
                temperature: [
                    { required: true, message: '请输入温度参数', trigger: 'blur' }
                ]
            }
        }
    },
    
    mounted() {
        this.loadModelConfigs();
    },
    
    methods: {
        // 加载模型配置列表
        async loadModelConfigs() {
            try {
                this.loading = true;
                const response = await axios.get('/api/ai/config/models');
                if (response.data.code === 200) {
                    this.modelConfigs = response.data.data.list || [];
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('加载模型配置失败:', error);
                this.$message.error('加载模型配置失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 显示添加对话框
        showAddDialog() {
            this.dialogTitle = '添加模型';
            this.isEdit = false;
            this.currentModelId = null;
            this.resetForm();
            this.dialogVisible = true;
        },
        
        // 编辑模型
        async editModel(model) {
            try {
                const response = await axios.get(`/api/ai/config/models/${model.id}`);
                if (response.data.code === 200) {
                    this.dialogTitle = '编辑模型';
                    this.isEdit = true;
                    this.currentModelId = model.id;
                    this.modelForm = { ...response.data.data };
                    this.dialogVisible = true;
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('获取模型详情失败:', error);
                this.$message.error('获取模型详情失败');
            }
        },
        
        // 保存模型
        async saveModel() {
            try {
                await this.$refs.modelForm.validate();
                
                this.saving = true;
                let response;
                
                if (this.isEdit) {
                    response = await axios.put(`/api/ai/config/models/${this.currentModelId}`, this.modelForm);
                } else {
                    response = await axios.post('/api/ai/config/models', this.modelForm);
                }
                
                if (response.data.code === 200) {
                    this.$message.success(this.isEdit ? '模型更新成功' : '模型创建成功');
                    this.dialogVisible = false;
                    this.loadModelConfigs();
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                if (error.message) {
                    // 表单验证错误
                    return;
                }
                console.error('保存模型失败:', error);
                this.$message.error('保存模型失败');
            } finally {
                this.saving = false;
            }
        },
        
        // 切换模型状态
        async toggleModel(model) {
            try {
                const response = await axios.put(`/api/ai/config/models/${model.id}/toggle`, null, {
                    params: { enabled: model.enabled }
                });
                
                if (response.data.code === 200) {
                    this.$message.success(model.enabled ? '模型已启用' : '模型已禁用');
                } else {
                    // 恢复原状态
                    model.enabled = !model.enabled;
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                // 恢复原状态
                model.enabled = !model.enabled;
                console.error('切换模型状态失败:', error);
                this.$message.error('切换模型状态失败');
            }
        },
        
        // 测试模型连接
        async testModel(model) {
            try {
                this.$message.info('正在测试模型连接...');
                const response = await axios.post(`/api/ai/config/models/${model.id}/test`);
                
                if (response.data.code === 200) {
                    this.$message.success('模型连接测试成功');
                } else {
                    this.$message.error('模型连接测试失败: ' + response.data.message);
                }
            } catch (error) {
                console.error('测试模型连接失败:', error);
                this.$message.error('测试模型连接失败');
            }
        },
        
        // 删除模型
        deleteModel(model) {
            this.$confirm(`确定要删除模型 "${model.modelName}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.delete(`/api/ai/config/models/${model.id}`);
                    if (response.data.code === 200) {
                        this.$message.success('模型删除成功');
                        this.loadModelConfigs();
                    } else {
                        this.$message.error(response.data.message);
                    }
                } catch (error) {
                    console.error('删除模型失败:', error);
                    this.$message.error('删除模型失败');
                }
            }).catch(() => {
                // 用户取消删除
            });
        },
        
        // 重置表单
        resetForm() {
            this.modelForm = {
                modelType: '',
                modelName: '',
                apiKey: '',
                apiUrl: '',
                maxTokens: 4000,
                temperature: 0.7,
                topP: 0.9,
                frequencyPenalty: 0.0,
                presencePenalty: 0.0,
                systemPrompt: 'You are a helpful assistant.',
                tokenPriceInput: 0.000002,
                tokenPriceOutput: 0.000006,
                rateLimitRpm: 60,
                rateLimitTpm: 60000,
                enabled: true,
                priority: 0,
                description: ''
            };
            
            if (this.$refs.modelForm) {
                this.$refs.modelForm.clearValidate();
            }
        }
    }
});
