// Token使用统计

// 配置axios请求拦截器，自动添加用户身份信息
axios.interceptors.request.use(function (config) {
    // 尝试从多个来源获取用户ID
    let userId = null;
    let userName = null;
    let realName = null;

    // 1. 从sessionStorage获取（如果有的话）
    try {
        const userInfo = sessionStorage.getItem('userInfo');
        if (userInfo) {
            const user = JSON.parse(userInfo);
            userId = user.userId || user.id;
            userName = user.userName || user.username;
            realName = user.realName || user.name;
        }
    } catch (e) {
        console.debug('从sessionStorage获取用户信息失败:', e);
    }

    // 2. 从localStorage获取（如果有的话）
    if (!userId) {
        try {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                const user = JSON.parse(userInfo);
                userId = user.userId || user.id;
                userName = user.userName || user.username;
                realName = user.realName || user.name;
            }
        } catch (e) {
            console.debug('从localStorage获取用户信息失败:', e);
        }
    }

    // 3. 从cookie获取（如果有的话）
    if (!userId) {
        try {
            const cookies = document.cookie.split(';');
            let accessToken = null;

            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'userId') {
                    userId = decodeURIComponent(value);
                } else if (name === 'userName') {
                    userName = decodeURIComponent(value);
                } else if (name === 'realName') {
                    realName = decodeURIComponent(value);
                } else if (name === 'access_token') {
                    accessToken = decodeURIComponent(value);
                }
            }

            // 如果有access_token但没有直接的用户信息，设置token到请求头
            if (accessToken && !userId) {
                console.debug('AI统计页面从cookie获取到access_token，将设置到请求头');
                config.headers['X-Access-Token'] = accessToken;
            }
        } catch (e) {
            console.debug('从cookie获取用户信息失败:', e);
        }
    }

    // 4. 从全局变量获取（如果页面设置了的话）
    if (!userId && typeof window.currentUser !== 'undefined') {
        userId = window.currentUser.userId || window.currentUser.id;
        userName = window.currentUser.userName || window.currentUser.username;
        realName = window.currentUser.realName || window.currentUser.name;
    }

    // 设置请求头
    if (userId) {
        config.headers['X-User-Id'] = userId;
        console.debug('AI统计页面设置请求头 X-User-Id:', userId);
    }
    if (userName) {
        config.headers['X-User-Name'] = userName;
        console.debug('AI统计页面设置请求头 X-User-Name:', userName);
    }
    if (realName) {
        config.headers['X-Real-Name'] = realName;
        console.debug('AI统计页面设置请求头 X-Real-Name:', realName);
    }

    return config;
}, function (error) {
    return Promise.reject(error);
});

new Vue({
    el: '#app',
    data() {
        return {
            // 统计数据
            todayStats: null,
            weekStats: null,
            monthStats: null,
            
            // 模型使用分布
            modelUsage: [],
            modelUsagePeriod: 'week',
            maxTokens: 0,
            
            // Top用户排行
            topUsers: [],
            topUsersPeriod: 'week'
        }
    },
    
    mounted() {
        this.init();
    },
    
    methods: {
        // 初始化
        async init() {
            await Promise.all([
                this.loadUserStats(),
                this.loadModelUsage(),
                this.loadTopUsers()
            ]);
        },
        
        // 加载用户统计数据
        async loadUserStats() {
            try {
                // 加载今日统计
                const todayResponse = await axios.get('/api/ai/chat/usage/user', {
                    params: { period: 'today' }
                });
                if (todayResponse.data.code === 200) {
                    this.todayStats = todayResponse.data.data;
                }

                // 加载本周统计
                const weekResponse = await axios.get('/api/ai/chat/usage/user', {
                    params: { period: 'week' }
                });
                if (weekResponse.data.code === 200) {
                    this.weekStats = weekResponse.data.data;
                }

                // 加载本月统计
                const monthResponse = await axios.get('/api/ai/chat/usage/user', {
                    params: { period: 'month' }
                });
                if (monthResponse.data.code === 200) {
                    this.monthStats = monthResponse.data.data;
                }
            } catch (error) {
                console.error('加载用户统计失败:', error);
                this.$message.error('加载用户统计失败');
            }
        },
        
        // 加载模型使用分布
        async loadModelUsage() {
            try {
                const response = await axios.get('/api/ai/chat/usage/system', {
                    params: { period: this.modelUsagePeriod }
                });
                
                if (response.data.code === 200) {
                    const data = response.data.data;
                    this.modelUsage = data.modelUsage || [];
                    
                    // 计算最大Token数，用于计算百分比
                    this.maxTokens = Math.max(...this.modelUsage.map(m => m.totalTokens), 1);
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('加载模型使用分布失败:', error);
                this.$message.error('加载模型使用分布失败');
            }
        },
        
        // 加载Top用户排行
        async loadTopUsers() {
            try {
                const response = await axios.get('/api/ai/chat/usage/system', {
                    params: { period: this.topUsersPeriod }
                });
                
                if (response.data.code === 200) {
                    const data = response.data.data;
                    this.topUsers = data.topUsers || [];
                } else {
                    this.$message.error(response.data.message);
                }
            } catch (error) {
                console.error('加载Top用户排行失败:', error);
                this.$message.error('加载Top用户排行失败');
            }
        },
        
        // 计算使用百分比
        getUsagePercentage(tokens) {
            if (this.maxTokens === 0) return 0;
            return Math.round((tokens / this.maxTokens) * 100);
        },
        
        // 格式化数字
        formatNumber(num) {
            if (!num) return '0';
            
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            } else {
                return num.toString();
            }
        },
        
        // 格式化成本
        formatCost(cost) {
            if (!cost) return '0.00';
            return parseFloat(cost).toFixed(4);
        }
    }
});
