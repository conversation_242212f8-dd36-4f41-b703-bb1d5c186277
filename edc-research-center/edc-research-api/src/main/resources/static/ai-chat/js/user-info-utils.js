/**
 * 用户信息获取工具类
 * 提供统一的用户信息获取和设置功能
 */
(function(window) {
    'use strict';
    
    // 用户信息工具对象
    const UserInfoUtils = {
        
        /**
         * 从多个来源获取用户信息
         * @returns {Object} 用户信息对象 {userId, userName, realName}
         */
        getUserInfo: function() {
            let userId = null;
            let userName = null;
            let realName = null;
            
            // 1. 从sessionStorage获取（优先级最高）
            try {
                const userInfo = sessionStorage.getItem('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    userId = user.userId || user.id;
                    userName = user.userName || user.username;
                    realName = user.realName || user.name;
                    
                    if (userId) {
                        console.debug('从sessionStorage获取到用户信息:', { userId, userName, realName });
                        return { userId, userName, realName };
                    }
                }
            } catch (e) {
                console.debug('从sessionStorage获取用户信息失败:', e);
            }
            
            // 2. 从localStorage获取
            try {
                const userInfo = localStorage.getItem('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    userId = user.userId || user.id;
                    userName = user.userName || user.username;
                    realName = user.realName || user.name;
                    
                    if (userId) {
                        console.debug('从localStorage获取到用户信息:', { userId, userName, realName });
                        return { userId, userName, realName };
                    }
                }
            } catch (e) {
                console.debug('从localStorage获取用户信息失败:', e);
            }
            
            // 3. 从cookie获取
            try {
                const cookies = document.cookie.split(';');
                const cookieData = {};

                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'userId') {
                        cookieData.userId = decodeURIComponent(value);
                    } else if (name === 'userName') {
                        cookieData.userName = decodeURIComponent(value);
                    } else if (name === 'realName') {
                        cookieData.realName = decodeURIComponent(value);
                    } else if (name === 'access_token') {
                        cookieData.accessToken = decodeURIComponent(value);
                    }
                }

                if (cookieData.userId) {
                    userId = cookieData.userId;
                    userName = cookieData.userName || '用户';
                    realName = cookieData.realName || cookieData.userName || '用户';

                    console.debug('从cookie获取到用户信息:', { userId, userName, realName });
                    return { userId, userName, realName };
                }

                // 如果没有直接的用户信息，但有access_token，尝试通过API获取用户信息
                if (cookieData.accessToken && !cookieData.userId) {
                    console.debug('从cookie获取到access_token，尝试获取用户信息:', cookieData.accessToken);
                    // 这里可以通过API调用获取用户信息，但为了避免同步调用，我们先返回token信息
                    // 实际的用户信息获取会在后端的getUserIdFromRequest方法中处理
                    return { userId: null, userName: null, realName: null, accessToken: cookieData.accessToken };
                }
            } catch (e) {
                console.debug('从cookie获取用户信息失败:', e);
            }
            
            // 4. 从全局变量获取
            if (typeof window.currentUser !== 'undefined' && window.currentUser) {
                userId = window.currentUser.userId || window.currentUser.id;
                userName = window.currentUser.userName || window.currentUser.username;
                realName = window.currentUser.realName || window.currentUser.name;
                
                if (userId) {
                    console.debug('从全局变量获取到用户信息:', { userId, userName, realName });
                    return { userId, userName, realName };
                }
            }
            
            // 5. 从服务器端注入的变量获取
            if (typeof window.serverUserInfo !== 'undefined' && window.serverUserInfo) {
                userId = window.serverUserInfo.userId || window.serverUserInfo.id;
                userName = window.serverUserInfo.userName || window.serverUserInfo.username;
                realName = window.serverUserInfo.realName || window.serverUserInfo.name;
                
                if (userId) {
                    console.debug('从服务器端注入变量获取到用户信息:', { userId, userName, realName });
                    return { userId, userName, realName };
                }
            }
            
            console.debug('未获取到用户信息，返回匿名用户');
            return { userId: null, userName: null, realName: null };
        },
        
        /**
         * 设置用户信息到各种存储中
         * @param {Object} userInfo 用户信息对象
         */
        setUserInfo: function(userInfo) {
            if (!userInfo || !userInfo.userId) {
                console.warn('用户信息无效，无法设置');
                return false;
            }
            
            try {
                // 存储到sessionStorage
                sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
                console.debug('用户信息已存储到sessionStorage');
                
                // 设置到全局变量
                window.currentUser = userInfo;
                console.debug('用户信息已设置到全局变量');
                
                return true;
            } catch (e) {
                console.error('设置用户信息失败:', e);
                return false;
            }
        },
        
        /**
         * 清除用户信息
         */
        clearUserInfo: function() {
            try {
                sessionStorage.removeItem('userInfo');
                localStorage.removeItem('userInfo');
                delete window.currentUser;
                console.debug('用户信息已清除');
                return true;
            } catch (e) {
                console.error('清除用户信息失败:', e);
                return false;
            }
        },
        
        /**
         * 检查用户是否已登录
         * @returns {boolean} 是否已登录
         */
        isLoggedIn: function() {
            const userInfo = this.getUserInfo();
            return !!(userInfo.userId && userInfo.userId !== 'anonymousUser');
        },
        
        /**
         * 获取当前用户ID
         * @returns {string} 用户ID，未登录返回'anonymousUser'
         */
        getCurrentUserId: function() {
            const userInfo = this.getUserInfo();
            return userInfo.userId || 'anonymousUser';
        },
        
        /**
         * 获取当前用户名
         * @returns {string} 用户名，未登录返回'匿名用户'
         */
        getCurrentUserName: function() {
            const userInfo = this.getUserInfo();
            return userInfo.userName || '匿名用户';
        },
        
        /**
         * 初始化用户信息（页面加载时调用）
         */
        init: function() {
            console.log('初始化用户信息工具...');
            
            // 尝试从各种来源获取用户信息
            const userInfo = this.getUserInfo();
            
            if (userInfo.userId) {
                console.log('用户信息初始化完成:', userInfo);
                
                // 确保信息存储到sessionStorage
                this.setUserInfo(userInfo);
            } else {
                console.log('未检测到用户登录信息，当前为匿名用户');
            }
            
            return userInfo;
        }
    };
    
    // 将工具对象暴露到全局
    window.UserInfoUtils = UserInfoUtils;
    
    // 页面加载完成后自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            UserInfoUtils.init();
        });
    } else {
        UserInfoUtils.init();
    }
    
})(window);
