<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDC 集群监控系统登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #667eea;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .logo p {
            color: #666;
            font-size: 1.1em;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-header {
            margin-bottom: 25px;
        }

        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            line-height: 40px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .step-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 10px;
        }

        .step-description {
            color: #666;
            font-size: 0.95em;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 10px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 15px;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e1e5e9;
            border-radius: 2px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            transition: width 0.5s ease;
        }

        .step-1 .progress-fill { width: 33%; }
        .step-2 .progress-fill { width: 66%; }
        .step-3 .progress-fill { width: 100%; }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #999;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🖥️ EDC</h1>
            <p>集群监控系统</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>

        <!-- 步骤1：验证秘钥 -->
        <div class="step step-1 active">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">验证访问秘钥</div>
                <div class="step-description">请输入集群监控访问秘钥</div>
            </div>

            <div class="form-group">
                <label for="secretKey">集群监控秘钥</label>
                <input type="password" id="secretKey" placeholder="请输入集群监控秘钥">
            </div>

            <button class="btn" onclick="verifySecretKey()">验证秘钥</button>
            
            <div class="loading" id="step1-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="error-message" id="step1-error"></div>
        </div>

        <!-- 步骤2：验证Token -->
        <div class="step step-2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">验证访问令牌</div>
                <div class="step-description">请输入系统提供的验证码和刷新码</div>
            </div>

            <div class="form-group">
                <label for="code">验证码 (Code)</label>
                <input type="text" id="code" placeholder="请输入验证码">
            </div>

            <div class="form-group">
                <label for="refreshCode">刷新码 (RefreshCode)</label>
                <input type="text" id="refreshCode" placeholder="请输入刷新码">
            </div>

            <button class="btn" onclick="verifyToken()">验证并登录</button>
            
            <div class="loading" id="step2-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="error-message" id="step2-error"></div>
        </div>

        <!-- 步骤3：登录成功 -->
        <div class="step step-3">
            <div class="step-header">
                <div class="step-number">✓</div>
                <div class="step-title">登录成功</div>
                <div class="step-description">正在跳转到集群监控管理系统...</div>
            </div>

            <div class="success-message" style="display: block;">
                ✅ 验证成功！正在跳转到集群监控系统...
            </div>
        </div>

        <div class="footer">
            <p>EDC 集群监控系统 v2.0</p>
            <p>© 2025 EDC Development Team</p>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let validatedAccessToken = '';

        // 构建API URL（支持代理路径）
        function buildApiUrl(path) {
            let baseUrl = window.location.origin;

            if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
                const serverConfig = window.SERVER_CONFIG;
                const protocol = window.location.protocol;
                const host = serverConfig.address;
                const port = serverConfig.port;
                const contextPath = serverConfig.contextPath || '';

                baseUrl = `${protocol}//${host}`;
                // 修复：确保端口号正确添加
                if (port && port !== '80' && port !== '443') {
                    // 检查host是否已经包含端口号
                    if (!host.includes(':')) {
                        baseUrl += `:${port}`;
                    }
                }
                baseUrl += contextPath;
            } else {
                // 修复：确保包含端口号
                const protocol = window.location.protocol;
                const hostname = window.location.hostname;
                const port = window.location.port;

                baseUrl = `${protocol}//${hostname}`;
                // 确保端口号被正确添加
                if (port && port !== '80' && port !== '443') {
                    baseUrl += `:${port}`;
                }

                // 检查当前路径是否已经包含上下文路径
                const currentPath = window.location.pathname;
                const pathSegments = currentPath.split('/').filter(segment => segment);

                if (pathSegments.length > 0) {
                    const firstSegment = pathSegments[0];
                    // 如果当前已经在/api上下文中，则添加到baseUrl
                    if (firstSegment === 'api') {
                        baseUrl += '/api';
                    } else if (firstSegment !== 'api' &&
                        (firstSegment.toLowerCase().includes('api') ||
                         firstSegment.toLowerCase().includes('release') ||
                         firstSegment === 'releaseApi' ||
                         firstSegment === 'BoRuiApi')) {
                        baseUrl += `/${firstSegment}`;
                    }
                }
            }

            // 修复：根据baseUrl是否已包含/api来决定是否添加/api前缀
            let apiPath;
            if (baseUrl.endsWith('/api')) {
                // 如果baseUrl已经包含/api，直接添加cluster/monitor路径
                apiPath = '/cluster/monitor' + path;
            } else {
                // 如果baseUrl不包含/api，需要添加完整的/api/cluster/monitor路径
                apiPath = '/api/cluster/monitor' + path;
            }

            const fullUrl = baseUrl + apiPath;
            console.log('🔗 构建API URL:', fullUrl);
            console.log('🔧 URL构建详情 - 协议:', window.location.protocol, '主机:', window.location.hostname, '端口:', window.location.port);
            console.log('🔧 当前路径:', window.location.pathname);
            console.log('🔧 baseUrl:', baseUrl, 'apiPath:', apiPath);
            return fullUrl;
        }

        // 验证配置秘钥
        async function verifySecretKey() {
            const secretKey = document.getElementById('secretKey').value.trim();
            if (!secretKey) {
                showError('step1-error', '请输入集群监控秘钥');
                return;
            }

            showLoading('step1-loading', true);
            hideError('step1-error');

            try {
                const response = await fetch(buildApiUrl('/auth/verify-secret'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ secretKey: secretKey })
                });

                const result = await response.json();

                if (result.code === 200) {
                    nextStep();
                } else {
                    showError('step1-error', result.message || '秘钥验证失败');
                }
            } catch (error) {
                console.error('验证秘钥失败:', error);
                showError('step1-error', '网络错误，请稍后重试');
            } finally {
                showLoading('step1-loading', false);
            }
        }

        // 验证Token
        async function verifyToken() {
            const code = document.getElementById('code').value.trim();
            const refreshCode = document.getElementById('refreshCode').value.trim();

            if (!code) {
                showError('step2-error', '请输入验证码');
                return;
            }

            if (!refreshCode) {
                showError('step2-error', '请输入刷新码');
                return;
            }

            showLoading('step2-loading', true);
            hideError('step2-error');

            try {
                const response = await fetch(buildApiUrl('/auth/verify-token'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        refreshCode: refreshCode
                    })
                });

                const result = await response.json();

                if (result.code === 200 && result.data && result.data.accessToken) {
                    validatedAccessToken = result.data.accessToken;
                    sessionStorage.setItem('clusterAccessToken', validatedAccessToken);
                    console.log('AccessToken 已存储 (长度:', validatedAccessToken.length, '字符)');

                    nextStep();
                    // 跳转到集群监控管理页面
                    setTimeout(() => {
                        window.location.href = buildApiUrl(`/page?accessToken=${encodeURIComponent(validatedAccessToken)}`);
                    }, 2000);
                } else {
                    showError('step2-error', result.message || 'Token验证失败');
                }
            } catch (error) {
                console.error('验证Token失败:', error);
                showError('step2-error', '网络错误，请稍后重试');
            } finally {
                showLoading('step2-loading', false);
            }
        }

        // 下一步
        function nextStep() {
            // 隐藏当前步骤
            document.querySelector(`.step-${currentStep}`).classList.remove('active');

            // 如果是第一步完成，隐藏配置秘钥输入框
            if (currentStep === 1) {
                const secretKeyInput = document.getElementById('secretKey');
                if (secretKeyInput) {
                    secretKeyInput.style.display = 'none';
                }
                const secretKeyGroup = secretKeyInput.closest('.form-group');
                if (secretKeyGroup) {
                    secretKeyGroup.style.display = 'none';
                }
            }

            currentStep++;
            document.querySelector(`.step-${currentStep}`).classList.add('active');
            document.body.className = `step-${currentStep}`;
        }

        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        // 显示/隐藏加载状态
        function showLoading(elementId, show) {
            document.getElementById(elementId).style.display = show ? 'block' : 'none';
        }

        // 初始化
        document.body.className = 'step-1';
    </script>
</body>
</html>
