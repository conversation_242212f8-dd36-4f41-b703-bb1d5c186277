/**
 * 动态域名主题样式
 * 根据不同域名应用不同的主题和布局
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */

/* ========================================
   基础主题变量
   ======================================== */

:root {
    /* 默认主题 */
    --primary-color: #1890ff;
    --secondary-color: #f0f2f5;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-color: #262626;
    --bg-color: #ffffff;
    --border-color: #d9d9d9;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ========================================
   PC端主题 (dev-1.info.com)
   ======================================== */

[data-theme="desktop"] {
    --primary-color: #1890ff;
    --secondary-color: #f0f2f5;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-color: #262626;
    --bg-color: #ffffff;
    --border-color: #d9d9d9;
    --header-height: 64px;
    --sidebar-width: 256px;
    --content-padding: 24px;
}

.theme-desktop {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5715;
}

.theme-desktop .header {
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.theme-desktop .sidebar {
    background: #001529;
    color: rgba(255, 255, 255, 0.85);
}

.theme-desktop .content {
    padding: var(--content-padding);
    background: var(--secondary-color);
    min-height: calc(100vh - var(--header-height));
}

/* ========================================
   移动端主题 (dev-2.info.com)
   ======================================== */

[data-theme="mobile"] {
    --primary-color: #1890ff;
    --secondary-color: #f5f5f5;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-color: #333333;
    --bg-color: #ffffff;
    --border-color: #e8e8e8;
    --header-height: 48px;
    --content-padding: 16px;
    --touch-target: 44px;
}

.theme-mobile {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 16px; /* 移动端字体稍大 */
    line-height: 1.4;
    -webkit-text-size-adjust: 100%;
}

.theme-mobile .header {
    background: var(--primary-color);
    color: white;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.theme-mobile .content {
    padding: var(--content-padding);
    margin-top: var(--header-height);
    background: var(--secondary-color);
    min-height: calc(100vh - var(--header-height));
}

.theme-mobile button,
.theme-mobile .btn {
    min-height: var(--touch-target);
    padding: 8px 16px;
    border-radius: 6px;
}

/* ========================================
   开发调试主题
   ======================================== */

[data-theme="debug"] {
    --primary-color: #722ed1;
    --secondary-color: #f9f0ff;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-color: #262626;
    --bg-color: #ffffff;
    --border-color: #d3adf7;
    --debug-color: #722ed1;
}

.theme-debug {
    border: 2px dashed var(--debug-color);
}

.theme-debug::before {
    content: "🐛 DEBUG MODE";
    position: fixed;
    top: 0;
    right: 0;
    background: var(--debug-color);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    z-index: 9999;
}

/* ========================================
   布局样式
   ======================================== */

/* 完整布局 */
.layout-full {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar content";
    grid-template-columns: var(--sidebar-width, 256px) 1fr;
    grid-template-rows: var(--header-height, 64px) 1fr;
    height: 100vh;
}

.layout-full .header { grid-area: header; }
.layout-full .sidebar { grid-area: sidebar; }
.layout-full .content { grid-area: content; }

/* 紧凑布局 */
.layout-compact {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.layout-compact .header {
    flex: 0 0 var(--header-height, 48px);
}

.layout-compact .content {
    flex: 1;
    overflow-y: auto;
}

.layout-compact .sidebar {
    display: none; /* 移动端隐藏侧边栏 */
}

/* 标准布局 */
.layout-standard {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--content-padding, 24px);
}

/* ========================================
   客户端类型样式
   ======================================== */

/* PC端样式 */
.client-pc {
    cursor: default;
}

.client-pc .scrollbar::-webkit-scrollbar {
    width: 8px;
}

.client-pc .scrollbar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

/* 移动端样式 */
.client-mobile {
    -webkit-overflow-scrolling: touch;
    touch-action: manipulation;
}

.client-mobile * {
    -webkit-tap-highlight-color: transparent;
}

.client-mobile input,
.client-mobile textarea,
.client-mobile select {
    font-size: 16px; /* 防止iOS缩放 */
}

/* 开发环境样式 */
.client-dev {
    position: relative;
}

.client-dev::after {
    content: "DEV";
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: var(--warning-color);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    z-index: 9999;
}

/* ========================================
   响应式设计
   ======================================== */

/* 移动端媒体查询 */
@media (max-width: 768px) {
    .layout-full {
        grid-template-areas: 
            "header"
            "content";
        grid-template-columns: 1fr;
    }
    
    .layout-full .sidebar {
        display: none;
    }
    
    :root {
        --content-padding: 12px;
        --header-height: 48px;
    }
}

/* 平板媒体查询 */
@media (min-width: 769px) and (max-width: 1024px) {
    :root {
        --sidebar-width: 200px;
        --content-padding: 20px;
    }
}

/* 大屏幕媒体查询 */
@media (min-width: 1200px) {
    :root {
        --sidebar-width: 280px;
        --content-padding: 32px;
    }
}

/* ========================================
   动画和过渡效果
   ======================================== */

.theme-desktop * {
    transition: all 0.3s ease;
}

.theme-mobile * {
    transition: all 0.2s ease; /* 移动端动画更快 */
}

/* ========================================
   打印样式
   ======================================== */

@media print {
    .theme-desktop,
    .theme-mobile {
        color: black !important;
        background: white !important;
    }
    
    .sidebar,
    .header,
    .debug-info {
        display: none !important;
    }
}
