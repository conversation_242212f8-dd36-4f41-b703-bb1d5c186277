/**
 * 动态域名适配器
 * 根据当前域名自动获取对应的配置信息
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */

class DynamicDomainAdapter {
    constructor() {
        this.config = null;
        this.apiBaseUrl = null;
        this.isInitialized = false;
        this.retryCount = 0;
        this.maxRetries = 3;
    }

    /**
     * 初始化动态域名配置
     */
    async initialize() {
        try {
            console.log('🌐 初始化动态域名配置...');
            console.log('📍 当前域名:', window.location.hostname);
            
            // 从后端获取当前域名的配置
            const response = await this.fetchWithRetry('/api/common/domain/config');
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.code === 200 && result.data) {
                    this.config = result.data;
                    this.apiBaseUrl = result.data.apiBaseUrl;
                    this.isInitialized = true;
                    
                    console.log('✅ 动态域名配置初始化成功');
                    console.log('📋 配置详情:', this.config);
                    console.log('🔗 API基础URL:', this.apiBaseUrl);
                    console.log('🏷️ 客户端类型:', this.config.clientType);
                    console.log('🎨 主题:', this.config.theme);
                    console.log('📐 布局:', this.config.layout);
                    
                    // 应用客户端配置
                    this.applyClientConfig();
                    
                    // 触发配置就绪事件
                    this.dispatchConfigReadyEvent();
                    return true;
                } else {
                    console.warn('⚠️ 后端返回错误:', result.message);
                }
            } else {
                console.warn('⚠️ 获取域名配置失败:', response.status, response.statusText);
            }
            
        } catch (error) {
            console.error('❌ 初始化动态域名配置失败:', error);
        }
        
        // 回退到默认配置
        this.initializeDefaultConfig();
        return false;
    }

    /**
     * 带重试的fetch请求
     */
    async fetchWithRetry(url, options = {}, retries = this.maxRetries) {
        for (let i = 0; i <= retries; i++) {
            try {
                const response = await fetch(url, {
                    ...options,
                    timeout: 5000 // 5秒超时
                });
                
                if (response.ok || i === retries) {
                    return response;
                }
                
                console.warn(`⚠️ 请求失败，重试 ${i + 1}/${retries}:`, response.status);
                await this.sleep(1000 * (i + 1)); // 递增延迟
                
            } catch (error) {
                if (i === retries) {
                    throw error;
                }
                console.warn(`⚠️ 请求异常，重试 ${i + 1}/${retries}:`, error.message);
                await this.sleep(1000 * (i + 1));
            }
        }
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 初始化默认配置
     */
    initializeDefaultConfig() {
        console.log('🔄 使用默认配置...');
        
        const currentOrigin = window.location.origin;
        this.apiBaseUrl = currentOrigin + '/api/';
        
        this.config = {
            apiBaseUrl: this.apiBaseUrl,
            domainName: window.location.hostname,
            configName: 'default',
            clientType: 'web',
            platform: 'web',
            theme: 'default',
            layout: 'standard',
            features: 'basic',
            apiPath: '/api/',
            includePort: true,
            customConfig: {},
            configSource: 'fallback'
        };
        
        this.isInitialized = true;
        console.log('✅ 默认配置初始化完成:', this.config);
        
        // 应用默认配置
        this.applyClientConfig();
        
        // 触发配置就绪事件
        this.dispatchConfigReadyEvent();
    }

    /**
     * 应用客户端配置
     */
    applyClientConfig() {
        if (!this.config) return;
        
        try {
            // 应用主题
            this.applyTheme(this.config.theme);
            
            // 应用布局
            this.applyLayout(this.config.layout);
            
            // 应用客户端类型样式
            this.applyClientTypeStyles(this.config.clientType);
            
            // 应用自定义配置
            this.applyCustomConfig(this.config.customConfig);
            
            console.log('🎨 客户端配置已应用');
            
        } catch (error) {
            console.error('❌ 应用客户端配置失败:', error);
        }
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${theme}`);
        console.log('🎨 主题已应用:', theme);
    }

    /**
     * 应用布局
     */
    applyLayout(layout) {
        document.documentElement.setAttribute('data-layout', layout);
        document.body.className = document.body.className.replace(/layout-\w+/g, '');
        document.body.classList.add(`layout-${layout}`);
        console.log('📐 布局已应用:', layout);
    }

    /**
     * 应用客户端类型样式
     */
    applyClientTypeStyles(clientType) {
        document.documentElement.setAttribute('data-client-type', clientType);
        document.body.className = document.body.className.replace(/client-\w+/g, '');
        document.body.classList.add(`client-${clientType}`);
        
        // 根据客户端类型添加特殊样式
        if (clientType === 'mobile') {
            document.body.classList.add('mobile-optimized');
            // 添加移动端特殊处理
            this.enableMobileOptimizations();
        } else if (clientType === 'pc') {
            document.body.classList.add('desktop-optimized');
            // 添加PC端特殊处理
            this.enableDesktopOptimizations();
        }
        
        console.log('📱 客户端类型样式已应用:', clientType);
    }

    /**
     * 应用自定义配置
     */
    applyCustomConfig(customConfig) {
        if (!customConfig || typeof customConfig !== 'object') return;
        
        // 将自定义配置存储到全局变量
        window.DOMAIN_CUSTOM_CONFIG = customConfig;
        
        // 应用调试模式
        if (customConfig.debug) {
            console.log('🐛 调试模式已启用');
            window.DEBUG_MODE = true;
        }
        
        // 应用其他自定义配置
        Object.keys(customConfig).forEach(key => {
            document.documentElement.setAttribute(`data-${key}`, customConfig[key]);
        });
        
        console.log('⚙️ 自定义配置已应用:', customConfig);
    }

    /**
     * 启用移动端优化
     */
    enableMobileOptimizations() {
        // 禁用双击缩放
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            viewport.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }
        
        // 添加触摸优化
        document.body.style.touchAction = 'manipulation';
        
        console.log('📱 移动端优化已启用');
    }

    /**
     * 启用桌面端优化
     */
    enableDesktopOptimizations() {
        // 桌面端特殊优化
        console.log('🖥️ 桌面端优化已启用');
    }

    /**
     * 构建API URL
     */
    buildApiUrl(path) {
        if (!this.isInitialized) {
            console.warn('⚠️ 动态域名配置未初始化，使用当前域名构建URL');
            return window.location.origin + '/api' + (path.startsWith('/') ? path : '/' + path);
        }

        // 确保path格式正确
        if (!path.startsWith('/')) {
            path = '/' + path;
        }

        // 构建完整URL
        let fullUrl = this.apiBaseUrl;
        if (fullUrl.endsWith('/') && path.startsWith('/')) {
            fullUrl = fullUrl.slice(0, -1);
        } else if (!fullUrl.endsWith('/') && !path.startsWith('/')) {
            fullUrl += '/';
        }
        
        fullUrl += path;

        console.log('🔗 构建API URL:', fullUrl);
        return fullUrl;
    }

    /**
     * 获取配置值
     */
    getConfig(key) {
        return this.config ? this.config[key] : null;
    }

    /**
     * 获取自定义配置值
     */
    getCustomConfig(key) {
        return this.config && this.config.customConfig ? this.config.customConfig[key] : null;
    }

    /**
     * 检查客户端类型
     */
    isClientType(type) {
        return this.getConfig('clientType') === type;
    }

    /**
     * 检查是否为移动端
     */
    isMobile() {
        return this.isClientType('mobile');
    }

    /**
     * 检查是否为PC端
     */
    isPC() {
        return this.isClientType('pc');
    }

    /**
     * 检查是否为开发环境
     */
    isDev() {
        return this.isClientType('dev');
    }

    /**
     * 获取主题
     */
    getTheme() {
        return this.getConfig('theme') || 'default';
    }

    /**
     * 获取布局
     */
    getLayout() {
        return this.getConfig('layout') || 'standard';
    }

    /**
     * 获取功能特性
     */
    getFeatures() {
        return this.getConfig('features') || 'basic';
    }

    /**
     * 触发配置就绪事件
     */
    dispatchConfigReadyEvent() {
        const event = new CustomEvent('dynamicDomainConfigReady', {
            detail: {
                config: this.config,
                apiBaseUrl: this.apiBaseUrl,
                clientType: this.getConfig('clientType'),
                adapter: this
            }
        });
        
        window.dispatchEvent(event);
        console.log('📡 动态域名配置就绪事件已触发');
    }

    /**
     * 等待配置初始化完成
     */
    async waitForInitialization(timeout = 10000) {
        if (this.isInitialized) {
            return Promise.resolve(this.config);
        }

        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error('动态域名配置初始化超时'));
            }, timeout);

            window.addEventListener('dynamicDomainConfigReady', (event) => {
                clearTimeout(timer);
                resolve(event.detail.config);
            }, { once: true });
        });
    }

    /**
     * 刷新配置
     */
    async refresh() {
        console.log('🔄 刷新动态域名配置...');
        this.isInitialized = false;
        this.config = null;
        this.apiBaseUrl = null;
        return await this.initialize();
    }
}

// 创建全局实例
window.DynamicDomainAdapter = new DynamicDomainAdapter();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 开始初始化动态域名适配器...');
    await window.DynamicDomainAdapter.initialize();
});

// 兼容性：为旧代码提供buildApiUrl全局函数
window.buildApiUrl = function(path) {
    if (window.DynamicDomainAdapter && window.DynamicDomainAdapter.isInitialized) {
        return window.DynamicDomainAdapter.buildApiUrl(path);
    } else {
        // 回退方案
        const origin = window.location.origin;
        const cleanPath = path.startsWith('/') ? path : '/' + path;
        return origin + '/api' + cleanPath;
    }
};

// 提供配置访问的全局函数
window.getDomainConfig = function(key) {
    return window.DynamicDomainAdapter ? window.DynamicDomainAdapter.getConfig(key) : null;
};

window.getCustomConfig = function(key) {
    return window.DynamicDomainAdapter ? window.DynamicDomainAdapter.getCustomConfig(key) : null;
};
