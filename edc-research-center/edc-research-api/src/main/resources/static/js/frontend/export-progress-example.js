/**
 * PDF导出进度查询前端示例代码
 * 用于5个以上参与者的PDF导出进度显示
 * <AUTHOR>
 */

class ExportProgressMonitor {
    constructor() {
        this.intervalId = null;
        this.exportFileId = null;
        this.progressCallback = null;
    }

    /**
     * 开始监控导出进度
     * @param {number} exportFileId 导出文件ID
     * @param {function} progressCallback 进度回调函数
     * @param {number} interval 查询间隔（毫秒），默认2秒
     */
    startMonitoring(exportFileId, progressCallback, interval = 2000) {
        this.exportFileId = exportFileId;
        this.progressCallback = progressCallback;
        
        // 立即查询一次
        this.checkProgress();
        
        // 设置定时查询
        this.intervalId = setInterval(() => {
            this.checkProgress();
        }, interval);
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    /**
     * 查询导出进度
     */
    async checkProgress() {
        try {
            const response = await fetch(`/api/testee-export/getExportProgress?exportFileId=${this.exportFileId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    // 添加认证头等
                }
            });

            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                const progressData = result.data;
                
                // 调用进度回调
                if (this.progressCallback) {
                    this.progressCallback(progressData);
                }
                
                // 如果导出完成或失败，停止监控
                if (progressData.exportStatus === 0 || progressData.exportStatus === 2) {
                    this.stopMonitoring();
                }
            } else {
                console.error('获取导出进度失败:', result.message);
            }
        } catch (error) {
            console.error('查询导出进度异常:', error);
        }
    }
}

/**
 * 进度显示组件示例
 */
class ProgressDisplay {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.createProgressUI();
    }

    /**
     * 创建进度显示UI
     */
    createProgressUI() {
        this.container.innerHTML = `
            <div class="export-progress-container">
                <div class="progress-header">
                    <h4>PDF导出进度</h4>
                    <span class="status-text" id="statusText">准备中...</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <span class="progress-percent" id="progressPercent">0%</span>
                </div>
                <div class="progress-details">
                    <span id="progressDetails">等待开始...</span>
                </div>
                <div class="progress-actions">
                    <button id="cancelBtn" class="btn-cancel" style="display: none;">取消导出</button>
                    <button id="downloadBtn" class="btn-download" style="display: none;">下载文件</button>
                </div>
            </div>
        `;
        
        this.addStyles();
    }

    /**
     * 添加样式
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .export-progress-container {
                max-width: 500px;
                margin: 20px auto;
                padding: 20px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #fafafa;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            
            .progress-header h4 {
                margin: 0;
                color: #333;
            }
            
            .status-text {
                color: #666;
                font-size: 14px;
            }
            
            .progress-bar-container {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }
            
            .progress-bar {
                flex: 1;
                height: 20px;
                background-color: #e0e0e0;
                border-radius: 10px;
                overflow: hidden;
                margin-right: 10px;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #45a049);
                transition: width 0.3s ease;
            }
            
            .progress-percent {
                font-weight: bold;
                color: #333;
                min-width: 40px;
            }
            
            .progress-details {
                color: #666;
                font-size: 13px;
                margin-bottom: 15px;
            }
            
            .progress-actions {
                text-align: center;
            }
            
            .btn-cancel, .btn-download {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin: 0 5px;
            }
            
            .btn-cancel {
                background-color: #f44336;
                color: white;
            }
            
            .btn-download {
                background-color: #4CAF50;
                color: white;
            }
            
            .btn-cancel:hover {
                background-color: #d32f2f;
            }
            
            .btn-download:hover {
                background-color: #45a049;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 更新进度显示
     * @param {object} progressData 进度数据
     */
    updateProgress(progressData) {
        const progressFill = document.getElementById('progressFill');
        const progressPercent = document.getElementById('progressPercent');
        const statusText = document.getElementById('statusText');
        const progressDetails = document.getElementById('progressDetails');
        const cancelBtn = document.getElementById('cancelBtn');
        const downloadBtn = document.getElementById('downloadBtn');

        // 更新进度条
        const percent = progressData.progressPercent || 0;
        progressFill.style.width = `${percent}%`;
        progressPercent.textContent = `${percent}%`;

        // 更新状态文本
        let status = '';
        switch (progressData.exportStatus) {
            case 0:
                status = '导出完成';
                break;
            case 1:
                status = '正在导出';
                break;
            case 2:
                status = '导出失败';
                break;
            default:
                status = '未知状态';
        }
        statusText.textContent = status;

        // 更新详细信息
        const details = progressData.currentStatus || 
            `处理进度: ${progressData.processedCount || 0}/${progressData.totalCount || 0}`;
        progressDetails.textContent = details;

        // 更新按钮显示
        if (progressData.exportStatus === 0 && progressData.downloadUrl) {
            // 导出完成，显示下载按钮
            cancelBtn.style.display = 'none';
            downloadBtn.style.display = 'inline-block';
            downloadBtn.onclick = () => {
                window.open(progressData.downloadUrl, '_blank');
            };
        } else if (progressData.exportStatus === 1) {
            // 正在导出，显示取消按钮
            cancelBtn.style.display = 'inline-block';
            downloadBtn.style.display = 'none';
        } else {
            // 其他状态，隐藏所有按钮
            cancelBtn.style.display = 'none';
            downloadBtn.style.display = 'none';
        }
    }
}

/**
 * 使用示例
 */
function startExportWithProgress(exportFileId) {
    // 创建进度显示组件
    const progressDisplay = new ProgressDisplay('progressContainer');
    
    // 创建进度监控器
    const monitor = new ExportProgressMonitor();
    
    // 开始监控
    monitor.startMonitoring(exportFileId, (progressData) => {
        progressDisplay.updateProgress(progressData);
        
        // 可以在这里添加其他处理逻辑
        console.log('导出进度更新:', progressData);
    });
    
    // 返回监控器实例，以便外部控制
    return monitor;
}

// 导出供外部使用
window.ExportProgressMonitor = ExportProgressMonitor;
window.ProgressDisplay = ProgressDisplay;
window.startExportWithProgress = startExportWithProgress;
