<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis数据管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 50px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            font-weight: bold;
        }

        h1 {
            color: #333;
            font-size: 32px;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #666;
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .features {
            text-align: left;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
            font-size: 14px;
        }

        .security-notice strong {
            color: #d63384;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">R</div>
        <h1>Redis数据管理系统</h1>
        <p class="subtitle">安全、高效的Redis数据管理工具</p>

        <div class="features">
            <h3>功能特性</h3>
            <ul>
                <li>支持指定Key查询和删除</li>
                <li>支持模糊匹配和前缀匹配</li>
                <li>批量数据操作功能</li>
                <li>双重安全认证机制</li>
                <li>操作前确认和日志记录</li>
                <li>直观的Web管理界面</li>
            </ul>
        </div>

        <div class="security-notice">
            <strong>安全提示：</strong> 
            本系统采用双重认证机制，需要配置秘钥和AccessToken验证。
            请确保只有授权人员使用此系统，所有操作都会被记录。
        </div>

        <div>
            <a href="javascript:void(0)" class="btn" onclick="goToLogin()">开始使用</a>
        </div>

        <div class="footer">
            <p>EDC Research Center - Redis Management System</p>
            <p>Version 1.0.0 | 2025</p>
        </div>
    </div>

    <script>
        // 动态获取页面基础路径
        function getPageBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}`;
        }

        // 跳转到登录页面
        function goToLogin() {
            window.location.href = '/api/redis-management/login.html';
        }

        // 跳转到管理页面
        function goToManagement() {
            window.location.href = '/api/redis-management/management.html';
        }

        // 检查是否已有有效token，如果有则直接跳转到管理页面
        window.addEventListener('load', () => {
            const token = localStorage.getItem('redis-management-token');
            const expire = localStorage.getItem('redis-management-token-expire');

            if (token && expire && Date.now() < parseInt(expire)) {
                // 显示快速进入按钮
                const container = document.querySelector('.welcome-container');
                const quickAccessBtn = document.createElement('a');
                quickAccessBtn.href = 'javascript:void(0)';
                quickAccessBtn.onclick = goToManagement;
                quickAccessBtn.className = 'btn';
                quickAccessBtn.textContent = '快速进入管理页面';
                quickAccessBtn.style.background = '#28a745';

                const btnContainer = container.querySelector('div:last-of-type');
                btnContainer.appendChild(quickAccessBtn);
            }
        });
    </script>
</body>
</html>
