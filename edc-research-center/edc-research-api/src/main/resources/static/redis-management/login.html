<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis数据管理 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            padding: 50px;
            width: 100%;
            max-width: 480px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 10px;
            position: relative;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e0e0e0;
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        .step.completed::after {
            background: #4caf50;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .alert {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }


    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Redis数据管理</h1>
            <p>安全认证登录 - 三步验证流程</p>
            <div style="font-size: 12px; color: #888; margin-top: 10px;">
                第一步：配置秘钥验证 → 第二步：获取访问令牌 → 第三步：进入管理页面
            </div>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">3</div>
        </div>

        <div class="alert" id="alert"></div>

        <!-- 第一步：验证配置秘钥 -->
        <div class="step-content active" id="step1-content">
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <strong>🔐 第一步：配置秘钥验证</strong><br>
                请输入Redis管理系统的配置秘钥进行身份验证
            </div>
            <form id="secret-form">
                <div class="form-group">
                    <label for="secretKey">配置秘钥</label>
                    <input type="password" id="secretKey" name="secretKey" required
                           placeholder="请输入Redis管理配置秘钥">
                </div>
                <button type="submit" class="btn">验证秘钥</button>
            </form>
        </div>

        <!-- 第二步：输入Code和RefreshCode -->
        <div class="step-content" id="step2-content">
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <strong>🔑 第二步：访问令牌获取</strong><br>
                请联系系统管理员获取有效的验证码和刷新码
            </div>
            <form id="token-form">
                <div class="form-group">
                    <label for="code">验证码 (Code)</label>
                    <input type="text" id="code" name="code" required
                           placeholder="请输入验证码">
                </div>
                <div class="form-group">
                    <label for="refreshCode">刷新码 (RefreshCode)</label>
                    <input type="text" id="refreshCode" name="refreshCode" required
                           placeholder="请输入刷新码">
                </div>
                <button type="submit" class="btn">获取访问令牌</button>
            </form>
        </div>

        <!-- 第三步：验证AccessToken -->
        <div class="step-content" id="step3-content">
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <strong>🚀 第三步：访问令牌验证</strong><br>
                系统正在验证您的访问令牌，验证成功后将自动跳转到Redis管理页面
            </div>
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在验证访问令牌...</p>
            </div>
            <div id="success-message" style="display: none; text-align: center;">
                <h3 style="color: #4caf50; margin-bottom: 20px;">🎉 认证成功</h3>
                <p style="margin-bottom: 20px;">即将跳转到Redis管理页面...</p>
                <button class="btn" onclick="goToManagement()">立即进入管理页面</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let accessToken = '';

        // 动态获取API基础路径
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }

        // 动态获取页面基础路径
        function getPageBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}`;
        }

        // 显示提示信息
        function showAlert(message, type = 'error') {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // 更新步骤指示器
        function updateStepIndicator(step) {
            for (let i = 1; i <= 3; i++) {
                const stepEl = document.getElementById(`step${i}`);
                const contentEl = document.getElementById(`step${i}-content`);
                
                if (i < step) {
                    stepEl.className = 'step completed';
                } else if (i === step) {
                    stepEl.className = 'step active';
                } else {
                    stepEl.className = 'step';
                }
                
                contentEl.className = i === step ? 'step-content active' : 'step-content';
            }
        }

        // 第一步：验证配置秘钥
        document.getElementById('secret-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const secretKey = document.getElementById('secretKey').value;
            
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/auth/verify-secret`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ secretKey })
                });
                
                const result = await response.json();
                console.log('验证秘钥API响应:', result);

                // 修复：使用code字段判断成功状态，而不是success字段
                if (result.code === 200 && result.data && result.data.valid) {
                    showAlert('秘钥验证成功！', 'success');
                    currentStep = 2;
                    updateStepIndicator(currentStep);
                } else {
                    showAlert(result.message || '秘钥验证失败');
                }
            } catch (error) {
                showAlert('网络请求失败：' + error.message);
            }
        });



        // 第二步：获取AccessToken
        document.getElementById('token-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const code = document.getElementById('code').value;
            const refreshCode = document.getElementById('refreshCode').value;
            
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/auth/get-access-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code, refreshCode })
                });
                
                const result = await response.json();
                console.log('获取AccessToken API响应:', result);

                // 修复：使用code字段判断成功状态
                if (result.code === 200 && result.data && result.data.accessToken) {
                    accessToken = result.data.accessToken;
                    console.log('AccessToken获取成功:', accessToken);
                    console.log('AccessToken详细信息:', result.data);

                    // 立即保存token到localStorage，使用expiresIn计算过期时间
                    localStorage.setItem('redis-management-token', accessToken);
                    if (result.data.expiresIn) {
                        const expireTime = Date.now() + (result.data.expiresIn * 1000);
                        localStorage.setItem('redis-management-token-expire', expireTime);
                        console.log('Token已保存到localStorage，过期时间:', new Date(expireTime));
                    }

                    showAlert('访问令牌获取成功！', 'success');
                    currentStep = 3;
                    updateStepIndicator(currentStep);

                    // 自动验证token
                    console.log('准备自动验证token...');
                    setTimeout(validateToken, 1000);
                } else {
                    console.log('获取AccessToken失败:', result);
                    showAlert(result.message || '获取访问令牌失败');
                }
            } catch (error) {
                showAlert('网络请求失败：' + error.message);
            }
        });

        // 验证AccessToken
        async function validateToken() {
            document.getElementById('loading').style.display = 'block';
            
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/auth/validate-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    }
                });
                
                const result = await response.json();
                console.log('Token验证API响应:', result);
                console.log('响应状态:', response.status);
                console.log('result.code:', result.code);
                console.log('result.data:', result.data);
                if (result.data) {
                    console.log('result.data.valid:', result.data.valid);
                    console.log('result.data.remainingTime:', result.data.remainingTime);
                }

                document.getElementById('loading').style.display = 'none';

                // 修复：使用code字段判断成功状态
                if (result.code === 200 && result.data && result.data.valid) {
                    console.log('Token验证成功，开始处理跳转逻辑');
                    // Token已在第二步保存，这里只需要显示成功消息和跳转
                    console.log('Token验证通过，剩余时间:', result.data.remainingTime, '秒');

                    // 显示成功消息
                    document.getElementById('success-message').style.display = 'block';
                    console.log('成功消息已显示');

                    // 延迟跳转到管理页面
                    setTimeout(() => {
                        console.log('准备跳转到管理页面...');
                        // 直接跳转，不使用函数包装
                        setTimeout(() => {
                            try {
                                const targetUrl = '/api/redis-management/management.html';
                                console.log('开始跳转到:', targetUrl);
                                console.log('当前location:', window.location.href);

                                // 尝试多种跳转方式确保成功
                                window.location.href = targetUrl;

                                // 备用跳转方式
                                setTimeout(() => {
                                    if (window.location.href.indexOf('management.html') === -1) {
                                        console.log('第一次跳转失败，尝试replace方式');
                                        window.location.replace(targetUrl);
                                    }
                                }, 500);
                            } catch (error) {
                                console.error('跳转过程中发生错误:', error);
                                alert('跳转失败，请手动点击"立即进入"按钮');
                            }
                        }, 2000);
                    }, 1000);
                } else {
                    console.log('Token验证失败，条件检查:');
                    console.log('- result.code:', result.code);
                    console.log('- result.data存在:', !!result.data);
                    console.log('- result.data.valid:', result.data ? result.data.valid : 'data不存在');
                    showAlert('访问令牌验证失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showAlert('网络请求失败：' + error.message);
            }
        }

        // 跳转到管理页面
        function goToManagement() {
            try {
                const targetUrl = '/api/redis-management/management.html';
                console.log('goToManagement函数被调用');
                console.log('跳转目标URL:', targetUrl);
                console.log('当前页面URL:', window.location.href);

                // 直接跳转
                window.location.href = targetUrl;

                // 备用跳转检查
                setTimeout(() => {
                    if (window.location.href.indexOf('management.html') === -1) {
                        console.log('goToManagement跳转失败，尝试replace方式');
                        window.location.replace(targetUrl);
                    }
                }, 1000);
            } catch (error) {
                console.error('goToManagement跳转失败:', error);
                alert('跳转失败: ' + error.message);
                // 如果跳转失败，显示手动跳转链接
                const successDiv = document.getElementById('success-message');
                if (successDiv) {
                    successDiv.innerHTML = `
                        <h3 style="color: #4caf50; margin-bottom: 20px;">✓ 认证成功</h3>
                        <p style="margin-bottom: 20px;">自动跳转失败，请点击下方链接手动进入：</p>
                        <a href="/api/redis-management/management.html" class="btn" style="display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;">进入Redis管理页面</a>
                    `;
                }
            }
        }

        // 页面加载时检查是否已有有效token
        window.addEventListener('load', () => {
            const token = localStorage.getItem('redis-management-token');
            const expire = localStorage.getItem('redis-management-token-expire');

            console.log('页面加载检查Token:', { token: token ? '存在' : '不存在', expire, now: Date.now() });

            if (token && expire && Date.now() < parseInt(expire)) {
                console.log('发现有效Token，准备自动跳转');
                // 直接跳转到管理页面
                setTimeout(() => {
                    goToManagement();
                }, 1000);
            } else {
                console.log('没有有效Token，显示登录页面');
            }
        });
    </script>
</body>
</html>
