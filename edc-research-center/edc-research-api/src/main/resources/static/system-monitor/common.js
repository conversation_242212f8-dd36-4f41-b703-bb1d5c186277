/**
 * 系统监控通用工具函数
 * <AUTHOR>
 * @since 2025-01-15
 */

// 动态获取API基础路径
function getApiBaseUrl() {
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api`;
}

// 动态获取页面基础路径
function getPageBaseUrl() {
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api/system-monitor`;
}

// Token管理工具
const TokenManager = {
    // 存储Token
    setToken: function(token) {
        localStorage.setItem('system-monitor-token', token);
        localStorage.setItem('system-monitor-token-time', Date.now().toString());
    },
    
    // 获取Token
    getToken: function() {
        return localStorage.getItem('system-monitor-token');
    },
    
    // 获取Token存储时间
    getTokenTime: function() {
        const time = localStorage.getItem('system-monitor-token-time');
        return time ? parseInt(time) : null;
    },
    
    // 清除Token
    clearToken: function() {
        localStorage.removeItem('system-monitor-token');
        localStorage.removeItem('system-monitor-token-time');
        localStorage.removeItem('system-monitor-token-expire');
    },
    
    // 检查Token是否过期（假设1小时过期）
    isTokenExpired: function() {
        const tokenTime = this.getTokenTime();
        if (!tokenTime) return true;
        
        const now = Date.now();
        const expireTime = tokenTime + (60 * 60 * 1000); // 1小时
        return now > expireTime;
    },
    
    // 获取Token剩余时间（秒）
    getTokenRemainingTime: function() {
        const tokenTime = this.getTokenTime();
        if (!tokenTime) return 0;

        const now = Date.now();
        const expireTime = tokenTime + (60 * 60 * 1000); // 1小时
        const remaining = Math.max(0, expireTime - now);
        return Math.floor(remaining / 1000);
    },

    // 获取Token剩余时间（秒）- 别名方法
    getRemainingTime: function() {
        return this.getTokenRemainingTime();
    },
    
    // 格式化剩余时间
    formatRemainingTime: function() {
        const seconds = this.getTokenRemainingTime();
        if (seconds <= 0) return '已过期';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }
};

// 通用AJAX请求函数
async function makeApiRequest(url, options = {}) {
    const token = TokenManager.getToken();

    // 检查token是否过期
    if (!token || TokenManager.isTokenExpired()) {
        console.log('Token已过期或不存在，跳转到登录页面');
        logout();
        throw new Error('访问令牌已过期，请重新登录');
    }

    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    // 处理GET请求的data参数
    if (options.data && (options.method === 'GET' || !options.method)) {
        const params = new URLSearchParams(options.data);
        const separator = url.includes('?') ? '&' : '?';
        url += separator + params.toString();
        delete options.data;
    }

    // 添加Token到请求头或URL参数
    if (token) {
        if (options.method === 'GET' || !options.method) {
            // GET请求添加到URL参数
            const separator = url.includes('?') ? '&' : '?';
            url += `${separator}accessToken=${encodeURIComponent(token)}`;
        } else {
            // POST请求添加到请求头
            defaultOptions.headers['X-Access-Token'] = token;
        }
    }

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, finalOptions);

        // 检查响应状态
        if (response.status === 401) {
            console.log('收到401响应，Token可能已过期，跳转到登录页面');
            logout();
            throw new Error('访问令牌无效或已过期，请重新登录');
        }

        // 检查响应内容中的token过期信息
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const clonedResponse = response.clone();
                try {
                    const result = await clonedResponse.json();
                    if (result.code === 401 ||
                        (result.message && (result.message.includes('访问令牌无效') ||
                         result.message.includes('已过期') ||
                         result.message.includes('token') && result.message.includes('过期')))) {
                        console.log('API返回token过期信息，跳转到登录页面');
                        logout();
                        throw new Error('访问令牌已过期，请重新登录');
                    }
                } catch (parseError) {
                    // 如果解析失败，继续返回原始响应
                    console.warn('解析响应JSON失败:', parseError);
                }
            }
        }

        return response;
    } catch (error) {
        // 网络错误或其他错误
        if (error.message.includes('访问令牌') || error.message.includes('token')) {
            // 已经是token相关错误，直接抛出
            throw error;
        }

        console.error('API请求失败:', error);
        throw new Error('网络请求失败: ' + error.message);
    }
}

// 验证Token有效性
async function validateToken(token) {
    try {
        const url = `${getApiBaseUrl()}/monitor/validate-token?accessToken=${encodeURIComponent(token)}`;
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        return result.code === 200 && result.data === true;
    } catch (error) {
        console.error('Token验证失败:', error);
        return false;
    }
}

// 通用提示函数
function showAlert(containerId, message, type = 'danger') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    container.innerHTML = alertHtml;
    
    // 自动隐藏成功提示
    if (type === 'success') {
        setTimeout(() => {
            container.innerHTML = '';
        }, 3000);
    }
}

// 清除提示
function clearAlert(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '';
    }
}

// 页面跳转函数
function navigateToPage(page, token = null) {
    const accessToken = token || TokenManager.getToken();
    if (!accessToken) {
        window.location.href = `${getPageBaseUrl()}/login.html`;
        return;
    }
    
    const targetUrl = `${getPageBaseUrl()}/${page}?accessToken=${encodeURIComponent(accessToken)}`;
    window.location.href = targetUrl;
}

// 退出登录
function logout() {
    TokenManager.clearToken();
    window.location.href = `${getPageBaseUrl()}/login.html`;
}

// 检查登录状态
function checkLoginStatus() {
    const token = TokenManager.getToken();
    if (!token || TokenManager.isTokenExpired()) {
        logout();
        return false;
    }
    return true;
}

// 格式化时间
function formatDateTime(dateTime) {
    if (!dateTime) return '-';

    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 格式化持续时间
function formatDuration(milliseconds) {
    if (!milliseconds || milliseconds <= 0) return '--';

    const seconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
    } else {
        return `${secs}秒`;
    }
}

// 初始化页面通用功能
function initCommonFeatures() {
    // 检查登录状态
    if (!checkLoginStatus()) {
        return;
    }
    
    // 定时更新Token状态显示
    updateTokenStatus();
    setInterval(updateTokenStatus, 1000);
}

// 更新Token状态显示
function updateTokenStatus() {
    const statusElement = document.getElementById('token-status');
    const timeElement = document.getElementById('token-remaining-time');
    
    if (statusElement) {
        const isExpired = TokenManager.isTokenExpired();
        statusElement.textContent = isExpired ? '已过期' : '有效';
        statusElement.className = `badge ${isExpired ? 'badge-danger' : 'badge-success'}`;
    }
    
    if (timeElement) {
        timeElement.textContent = TokenManager.formatRemainingTime();
    }
}

// 获取URL参数
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 处理URL中的accessToken参数
function handleUrlAccessToken() {
    const accessToken = getUrlParameter('accessToken');
    if (accessToken) {
        console.log('从URL参数中获取到accessToken，正在保存...');
        TokenManager.setToken(accessToken);

        // 清理URL中的accessToken参数，避免在浏览器历史记录中暴露
        const url = new URL(window.location);
        url.searchParams.delete('accessToken');
        window.history.replaceState({}, document.title, url.pathname + url.search);

        console.log('accessToken已保存，URL已清理');
        return true;
    }
    return false;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 不在登录页面时才初始化通用功能
    if (!window.location.pathname.includes('login.html')) {
        // 首先处理URL中的accessToken参数
        handleUrlAccessToken();

        // 然后初始化通用功能
        initCommonFeatures();
    }
});
