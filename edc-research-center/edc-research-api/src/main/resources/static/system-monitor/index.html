<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控中心</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        .dashboard-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            margin-bottom: 20px;
        }
        .dashboard-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            border: none;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #eaeaea;
            font-weight: 600;
            padding: 15px 20px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        .stat-primary { color: #3498db; }
        .stat-success { color: #2ecc71; }
        .stat-warning { color: #f39c12; }
        .stat-danger { color: #e74c3c; }
        .nav-card {
            transition: all 0.3s ease;
            cursor: pointer;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: inherit;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }
        .nav-icon {
            font-size: 36px;
            margin-bottom: 10px;
        }
        .nav-title {
            font-size: 16px;
            font-weight: 600;
        }
        .header-status {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .system-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .token-status {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        .token-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }
        .token-label {
            color: #6c757d;
            font-weight: 500;
        }
        .token-value {
            font-weight: 600;
        }
        .logout-btn {
            padding: 4px 12px;
            font-size: 12px;
            border-radius: 15px;
        }
        .refresh-stats-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .refresh-stats-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .refresh-stats-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        .system-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        .system-info h4 {
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .system-info .row div {
            margin-bottom: 12px;
            font-size: 14px;
        }
        .system-info .row div strong {
            font-weight: 600;
            opacity: 0.9;
        }
        .system-info .row div span {
            font-weight: 500;
        }
        .stat-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            border: 1px solid #f0f0f0;
            background: white;
        }
        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #e0e0e0;
        }
        .nav-card {
            transition: all 0.3s ease;
            cursor: pointer;
            height: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: inherit;
            border-radius: 12px;
            border: 1px solid #f0f0f0;
            background: white;
            position: relative;
            overflow: hidden;
        }
        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
            border-color: #e0e0e0;
        }
        .nav-card:hover::before {
            transform: scaleX(1);
        }
        .nav-icon {
            font-size: 42px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }
        .nav-card:hover .nav-icon {
            transform: scale(1.1);
        }
        .nav-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .nav-card small {
            color: #6c757d;
            font-size: 12px;
        }
        .recent-activity-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }
        .recent-activity-item:hover {
            background-color: #f8f9fa;
            margin: 0 -15px;
            padding-left: 15px;
            padding-right: 15px;
            border-radius: 6px;
        }
        .recent-activity-item:last-child {
            border-bottom: none;
        }

        /* 驾驶舱仪表盘样式 */
        .dashboard-container {
            font-family: 'Arial', sans-serif;
        }

        .dashboard-gauge {
            margin-bottom: 20px;
        }

        .gauge-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .gauge-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .gauge-inner {
            width: 75px;
            height: 75px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #333;
            box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .gauge-value {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }

        .gauge-label {
            font-size: 9px;
            color: #7f8c8d;
            text-align: center;
            margin-top: 2px;
        }

        .gauge-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
            text-align: center;
        }

        .dashboard-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dashboard-panel h6 {
            color: white;
            margin-bottom: 12px;
            font-weight: 600;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 6px;
            font-size: 0.9rem;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .metric-row span:first-child {
            color: rgba(255, 255, 255, 0.8);
        }

        .metric-value {
            color: white;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        /* 统计卡片样式 - 优化尺寸 */
        .stat-card {
            position: relative;
            overflow: hidden;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 15px;
            height: 120px;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .stat-card .card-body {
            position: relative;
            z-index: 2;
            padding: 1rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .stat-icon {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1.8rem;
            opacity: 0.3;
        }

        /* 导航卡片样式优化 */
        .nav-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 8px;
            text-decoration: none;
            color: inherit;
            height: 100px;
        }

        .nav-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }

        .nav-card .card-body,
        .nav-card > div {
            padding: 0.75rem;
        }

        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .nav-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .nav-card small {
            font-size: 0.75rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .gauge-circle {
                width: 100px;
                height: 100px;
            }

            .gauge-inner {
                width: 75px;
                height: 75px;
            }

            .gauge-value {
                font-size: 16px;
            }

            .gauge-label {
                font-size: 9px;
            }

            .stat-value {
                font-size: 2rem;
            }

            .stat-icon {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="dashboard-title mb-0">
                        <i class="fa fa-tachometer text-primary"></i> 系统监控中心
                    </h1>
                    <p class="text-muted mb-0">实时监控系统运行状态和用户活动</p>
                </div>
                <div class="col-auto">
                    <div class="header-status">
                        <div class="system-status">
                            <button class="btn refresh-stats-btn" onclick="refreshStatistics()" id="refreshStatsBtn">
                                <i class="fa fa-refresh"></i> 刷新统计
                            </button>
                            <span class="badge badge-success">系统正常运行</span>
                        </div>
                        <!-- Token状态显示 -->
                        <div class="token-status">
                            <div class="token-info">
                                <span class="token-label">令牌状态:</span>
                                <span id="token-status" class="badge badge-success token-value">有效</span>
                            </div>
                            <div class="token-info">
                                <span class="token-label">剩余时间:</span>
                                <span id="token-remaining-time" class="token-value">--</span>
                            </div>
                            <button class="btn btn-outline-danger logout-btn" onclick="logout()">
                                <i class="fa fa-sign-out"></i> 退出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 系统性能监控驾驶舱 - 适度宽度展示 -->
        <div class="row mb-4 justify-content-center">
            <div class="col-11">
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white;">
                        <span><i class="fa fa-tachometer"></i> 系统性能监控驾驶舱</span>
                        <button class="btn btn-sm btn-outline-light" onclick="loadPerformanceOverview()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body p-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div id="performanceDashboard">
                            <div class="text-center py-4 text-white">
                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                <div class="mt-2">正在加载驾驶舱数据...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能导航 - 调整为左侧单列 -->
        <div class="row">
            <div class="col-md-3">
                <div class="row">
                    <div class="col-12 mb-3">
                        <a href="javascript:void(0)" class="nav-card card shadow-sm" onclick="navigateToPage('access-logs.html')" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: none;">
                            <div class="text-center py-3">
                                <div class="nav-icon text-primary">
                                    <i class="fa fa-list-alt"></i>
                                </div>
                                <div class="nav-title">访问日志</div>
                                <small class="text-muted">查看系统访问记录</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="javascript:void(0)" class="nav-card card shadow-sm" onclick="navigateToPage('online-users.html')" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: none;">
                            <div class="text-center py-3">
                                <div class="nav-icon text-success">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="nav-title">在线用户</div>
                                <small class="text-muted">监控在线用户状态</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="javascript:void(0)" class="nav-card card shadow-sm" onclick="navigateToPage('exception-logs.html')" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: none;">
                            <div class="text-center py-3">
                                <div class="nav-icon text-warning">
                                    <i class="fa fa-exclamation-triangle"></i>
                                </div>
                                <div class="nav-title">异常日志</div>
                                <small class="text-muted">查看系统异常记录</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="javascript:void(0)" class="nav-card card shadow-sm" onclick="showPerformanceModal()" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: none;">
                            <div class="text-center py-3">
                                <div class="nav-icon text-danger">
                                    <i class="fa fa-tachometer"></i>
                                </div>
                                <div class="nav-title">性能监控</div>
                                <small class="text-muted">查看系统性能指标</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="javascript:void(0)" class="nav-card card shadow-sm" onclick="navigateToPage('test.html')" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: none;">
                            <div class="text-center py-3">
                                <div class="nav-icon text-info">
                                    <i class="fa fa-cogs"></i>
                                </div>
                                <div class="nav-title">功能测试</div>
                                <small class="text-muted">系统功能测试工具</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <!-- 统计卡片 - 调整宽度和样式 -->
                <div class="row justify-content-center">
                    <div class="col-11">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card stat-card text-white shadow-sm" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-value" id="todayVisits">--</div>
                                        <div class="stat-label">今日访问量</div>
                                        <i class="fa fa-eye stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card stat-card text-white shadow-sm" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-value" id="onlineUsers">--</div>
                                        <div class="stat-label">在线用户</div>
                                        <i class="fa fa-users stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card stat-card text-white shadow-sm" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-value" id="todayExceptions">--</div>
                                        <div class="stat-label">今日异常</div>
                                        <i class="fa fa-exclamation-triangle stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card stat-card text-white shadow-sm" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-value" id="systemLoad">--</div>
                                        <div class="stat-label">系统负载</div>
                                        <i class="fa fa-server stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 - 居中上下展示，与上方保持协调 -->
        <div class="row justify-content-center">
            <div class="col-11">
                <!-- 最近访问记录 -->
                <div class="card mb-4 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white;">
                        <span><i class="fa fa-clock-o"></i> 最近访问记录</span>
                        <button class="btn btn-sm btn-outline-light" onclick="loadRecentAccess()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body p-0" style="background-color: #f8f9fa;">
                        <div id="recentAccess">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-primary"></i>
                                <div class="mt-2">正在加载访问记录...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近异常记录 -->
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white;">
                        <span><i class="fa fa-exclamation-circle"></i> 最近异常记录</span>
                        <button class="btn btn-sm btn-outline-light" onclick="loadRecentExceptions()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body p-0" style="background-color: #f8f9fa;">
                        <div id="recentExceptions">
                            <div class="text-center py-4">
                                <i class="fa fa-spinner fa-spin fa-2x text-danger"></i>
                                <div class="mt-2">正在加载异常记录...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        $(document).ready(function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            // 初始化Token状态显示
            updateTokenStatus();
            // 每秒更新Token状态
            setInterval(updateTokenStatus, 1000);

            // 加载仪表板数据
            loadDashboardData();

            // 定时刷新数据
            setInterval(loadDashboardData, 30000); // 30秒刷新一次

            // 更新服务器时间
            updateServerTime();
            setInterval(updateServerTime, 1000);
        });

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                // 加载统计概览
                await loadStatisticsOverview();

                // 加载最近活动
                await loadRecentActivity();

                // 加载系统信息
                await loadSystemInfo();

                // 加载性能监控概览
                await loadPerformanceOverview();
            } catch (error) {
                console.error('加载仪表板数据失败:', error);

                // 检查是否是token过期错误
                if (error.message && (error.message.includes('访问令牌无效') || error.message.includes('已过期'))) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                }
            }
        }

        // 加载统计概览
        async function loadStatisticsOverview() {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/statistics/overview`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200 && result.data) {
                    const data = result.data;
                    $('#todayVisits').text(data.todayVisits || 0);
                    $('#onlineUsers').text(data.onlineUsers || 0);
                    $('#todayExceptions').text(data.todayExceptions || 0);
                    $('#systemLoad').text((data.systemLoad || 0).toFixed(2) + '%');
                } else {
                    console.warn('统计概览数据格式异常:', result);
                }
            } catch (error) {
                console.error('加载统计概览失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    throw error; // 向上抛出，由loadDashboardData处理
                }
            }
        }

        // 加载最近活动
        async function loadRecentActivity() {
            try {
                // 加载最近访问记录
                const accessResponse = await makeApiRequest(`${getApiBaseUrl()}/monitor/access-logs/list?pageNum=1&pageSize=5`);
                const accessResult = await accessResponse.json();

                if (accessResult.code === 200) {
                    displayRecentAccess(accessResult.data?.list || []);
                }

                // 加载最近异常记录
                const exceptionResponse = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/list?pageNum=1&pageSize=5`);
                const exceptionResult = await exceptionResponse.json();

                if (exceptionResult.code === 200) {
                    displayRecentExceptions(exceptionResult.data?.list || []);
                }
            } catch (error) {
                console.error('加载最近活动失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    throw error; // 向上抛出，由loadDashboardData处理
                }

                $('#recentAccess').html('<div class="text-center text-muted py-3">加载失败</div>');
                $('#recentExceptions').html('<div class="text-center text-muted py-3">加载失败</div>');
            }
        }

        // 显示最近访问记录
        function displayRecentAccess(data) {
            const container = $('#recentAccess');

            if (!data || data.length === 0) {
                container.html('<div class="text-center text-muted py-4"><i class="fa fa-inbox" style="font-size: 24px; opacity: 0.5;"></i><br><small>暂无访问记录</small></div>');
                return;
            }

            // 创建表格形式的访问记录
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fa fa-link"></i> 请求URL</th>
                                <th><i class="fa fa-code"></i> 方法</th>
                                <th><i class="fa fa-map-marker"></i> IP地址</th>
                                <th><i class="fa fa-user"></i> 用户</th>
                                <th><i class="fa fa-globe"></i> 浏览器</th>
                                <th><i class="fa fa-clock-o"></i> 响应时间</th>
                                <th><i class="fa fa-check-circle"></i> 状态</th>
                                <th><i class="fa fa-calendar"></i> 访问时间</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.slice(0, 10).forEach(item => {
                const statusClass = item.statusCode >= 400 ? 'danger' : (item.statusCode >= 300 ? 'warning' : 'success');
                const responseTime = item.responseTime ? `${item.responseTime}ms` : '--';
                const userAgent = item.userAgent ? (item.userAgent.length > 30 ? item.userAgent.substring(0, 30) + '...' : item.userAgent) : '--';
                const requestUrl = item.requestUrl ? (item.requestUrl.length > 40 ? item.requestUrl.substring(0, 40) + '...' : item.requestUrl) : '--';

                html += `
                    <tr>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="${item.requestUrl || '-'}">
                                ${requestUrl}
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-info">${item.requestMethod || 'GET'}</span>
                        </td>
                        <td>
                            <span class="text-monospace">${item.requestIp || '-'}</span>
                        </td>
                        <td>
                            <span class="text-primary">${item.userId || '匿名'}</span>
                        </td>
                        <td>
                            <span class="text-muted" title="${item.userAgent || '-'}">${userAgent}</span>
                        </td>
                        <td>
                            <span class="badge badge-${item.responseTime > 1000 ? 'warning' : 'secondary'}">${responseTime}</span>
                        </td>
                        <td>
                            <span class="badge badge-${statusClass}">${item.statusCode || '-'}</span>
                        </td>
                        <td>
                            <small class="text-muted">${formatDateTime(item.accessTime)}</small>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.html(html);
        }

        // 显示最近异常记录
        function displayRecentExceptions(data) {
            const container = $('#recentExceptions');

            if (!data || data.length === 0) {
                container.html('<div class="text-center text-muted py-4"><i class="fa fa-check-circle text-success" style="font-size: 24px;"></i><br><small>暂无异常记录</small></div>');
                return;
            }

            // 创建表格形式的异常记录
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th><i class="fa fa-exclamation-triangle"></i> 异常类型</th>
                                <th><i class="fa fa-info-circle"></i> 异常信息</th>
                                <th><i class="fa fa-link"></i> 请求URL</th>
                                <th><i class="fa fa-code"></i> 方法</th>
                                <th><i class="fa fa-map-marker"></i> IP地址</th>
                                <th><i class="fa fa-user"></i> 用户</th>
                                <th><i class="fa fa-calendar"></i> 发生时间</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.slice(0, 10).forEach(item => {
                const exceptionType = item.exceptionType ? (item.exceptionType.length > 25 ? item.exceptionType.substring(0, 25) + '...' : item.exceptionType) : '未知异常';
                const exceptionMessage = item.exceptionMessage ? (item.exceptionMessage.length > 40 ? item.exceptionMessage.substring(0, 40) + '...' : item.exceptionMessage) : '--';
                const requestUrl = item.requestUrl ? (item.requestUrl.length > 30 ? item.requestUrl.substring(0, 30) + '...' : item.requestUrl) : '--';

                html += `
                    <tr>
                        <td>
                            <span class="badge badge-danger" title="${item.exceptionType || '未知异常'}">
                                ${exceptionType}
                            </span>
                        </td>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="${item.exceptionMessage || '-'}">
                                ${exceptionMessage}
                            </span>
                        </td>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 150px;" title="${item.requestUrl || '-'}">
                                ${requestUrl}
                            </span>
                        </td>
                        <td>
                            <span class="badge badge-secondary">${item.requestMethod || '--'}</span>
                        </td>
                        <td>
                            <span class="text-monospace">${item.requestIp || '--'}</span>
                        </td>
                        <td>
                            <span class="text-primary">${item.userId || '匿名'}</span>
                        </td>
                        <td>
                            <small class="text-muted">${formatDateTime(item.createTime)}</small>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.html(html);
        }

        // 单独加载最近访问记录
        async function loadRecentAccess() {
            try {
                $('#recentAccess').html(`
                    <div class="text-center py-4">
                        <i class="fa fa-spinner fa-spin fa-2x text-primary"></i>
                        <div class="mt-2">正在刷新访问记录...</div>
                    </div>
                `);

                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/access-logs/list?pageNum=1&pageSize=10`);
                const result = await response.json();

                if (result.code === 200) {
                    displayRecentAccess(result.data?.list || []);
                } else {
                    $('#recentAccess').html('<div class="text-center text-muted py-3">加载失败</div>');
                }
            } catch (error) {
                console.error('加载访问记录失败:', error);
                $('#recentAccess').html('<div class="text-center text-muted py-3">加载失败</div>');
            }
        }

        // 单独加载最近异常记录
        async function loadRecentExceptions() {
            try {
                $('#recentExceptions').html(`
                    <div class="text-center py-4">
                        <i class="fa fa-spinner fa-spin fa-2x text-warning"></i>
                        <div class="mt-2">正在刷新异常记录...</div>
                    </div>
                `);

                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/list?pageNum=1&pageSize=10`);
                const result = await response.json();

                if (result.code === 200) {
                    displayRecentExceptions(result.data?.list || []);
                } else {
                    $('#recentExceptions').html('<div class="text-center text-muted py-3">加载失败</div>');
                }
            } catch (error) {
                console.error('加载异常记录失败:', error);
                $('#recentExceptions').html('<div class="text-center text-muted py-3">加载失败</div>');
            }
        }

        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/overview`);
                const result = await response.json();

                if (result.code === 200 && result.data) {
                    const data = result.data;
                    if (data.systemInfo) {
                        $('#javaVersion').text(data.systemInfo.javaVersion || '--');
                        $('#osVersion').text(data.systemInfo.osName || '--');
                    }
                    if (data.jvmInfo) {
                        $('#uptime').text(formatUptime(data.jvmInfo.uptime * 1000) || '--'); // 转换为毫秒
                    }
                }
            } catch (error) {
                console.error('加载系统信息失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    throw error; // 向上抛出，由loadDashboardData处理
                }

                // 降级处理，显示基本信息
                $('#javaVersion').text('Java 8+');
                $('#osVersion').text('Linux/Windows');
                $('#uptime').text('运行中');
            }
        }

        // 更新服务器时间
        function updateServerTime() {
            const now = new Date();
            $('#serverTime').text(formatDateTime(now));
        }

        // 格式化运行时间
        function formatUptime(milliseconds) {
            if (!milliseconds) return '--';

            const seconds = Math.floor(milliseconds / 1000);
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}天${hours}小时${minutes}分钟`;
            } else if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else {
                return `${minutes}分钟`;
            }
        }

        // 刷新统计数据
        async function refreshStatistics() {
            const btn = $('#refreshStatsBtn');
            const originalText = btn.html();

            try {
                // 检查token状态
                if (!checkLoginStatus()) {
                    return;
                }

                // 显示加载状态
                btn.prop('disabled', true);
                btn.html('<i class="fa fa-spinner fa-spin"></i> 刷新中...');

                // 调用后端强制刷新统计API
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/statistics/refresh`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200) {
                    // 刷新前端显示的数据
                    await Promise.all([
                        loadStatisticsOverview(),
                        loadRecentActivity(),
                        loadSystemInfo()
                    ]);

                    // 显示成功提示
                    btn.html('<i class="fa fa-check"></i> 刷新完成');
                    btn.removeClass('refresh-stats-btn').addClass('btn-success');

                    // 显示刷新结果信息
                    if (result.data && result.data.cleanedExpiredUsers > 0) {
                        console.log(`清理了 ${result.data.cleanedExpiredUsers} 个过期用户`);
                    }

                    // 2秒后恢复原状
                    setTimeout(() => {
                        btn.html(originalText);
                        btn.removeClass('btn-success').addClass('refresh-stats-btn');
                        btn.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error(result.message || '刷新失败');
                }

            } catch (error) {
                console.error('刷新统计失败:', error);

                // 显示错误状态
                btn.html('<i class="fa fa-exclamation-triangle"></i> 刷新失败');
                btn.removeClass('refresh-stats-btn').addClass('btn-danger');

                // 显示错误详情
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                // 3秒后恢复原状
                setTimeout(() => {
                    btn.html(originalText);
                    btn.removeClass('btn-danger').addClass('refresh-stats-btn');
                    btn.prop('disabled', false);
                }, 3000);
            }
        }

        // 加载性能监控概览
        async function loadPerformanceOverview() {
            try {
                // 检查token状态
                if (!checkLoginStatus()) {
                    return;
                }

                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/overview`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200) {
                    displayPerformanceOverview(result.data);
                } else {
                    $('#performanceDashboard').html(`
                        <div class="text-center text-white py-4">
                            <i class="fa fa-exclamation-triangle fa-2x"></i>
                            <div class="mt-2">性能监控数据加载失败: ${result.message || '未知错误'}</div>
                        </div>
                    `);
                }
            } catch (error) {
                console.error('加载性能监控概览失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                $('#performanceDashboard').html(`
                    <div class="text-center text-white py-4">
                        <i class="fa fa-times-circle fa-2x"></i>
                        <div class="mt-2">网络请求失败: ${error.message}</div>
                    </div>
                `);
            }
        }

        // 显示性能监控驾驶舱仪表盘
        function displayPerformanceOverview(data) {
            if (!data) {
                $('#performanceDashboard').html('<div class="text-center text-white py-4">暂无性能数据</div>');
                return;
            }

            // 格式化数值，确保显示正确，小数保留两位
            const cpuUsage = data.cpuInfo?.cpuUsage ? parseFloat(data.cpuInfo.cpuUsage).toFixed(2) : 0;
            const availableProcessors = data.cpuInfo?.availableProcessors || 0;
            const systemLoadAverage = data.cpuInfo?.systemLoadAverage ? parseFloat(data.cpuInfo.systemLoadAverage).toFixed(2) : 0;
            const processCpuUsage = data.cpuInfo?.processCpuUsage ? parseFloat(data.cpuInfo.processCpuUsage).toFixed(2) : 0;

            const memoryUsage = data.memoryInfo?.memoryUsage ? parseFloat(data.memoryInfo.memoryUsage).toFixed(2) : 0;
            const totalMemory = data.memoryInfo?.totalMemory ? (data.memoryInfo.totalMemory / 1024).toFixed(2) : 0;
            const usedMemory = data.memoryInfo?.usedMemory ? (data.memoryInfo.usedMemory / 1024).toFixed(2) : 0;
            const freeMemory = data.memoryInfo?.freeMemory ? (data.memoryInfo.freeMemory / 1024).toFixed(2) : 0;
            const totalSwap = data.memoryInfo?.totalSwap ? (data.memoryInfo.totalSwap / 1024).toFixed(2) : 0;
            const usedSwap = data.memoryInfo?.usedSwap ? (data.memoryInfo.usedSwap / 1024).toFixed(2) : 0;

            const heapUsed = data.jvmInfo?.heapUsed ? (data.jvmInfo.heapUsed).toFixed(2) : 0;
            const heapMax = data.jvmInfo?.heapMax ? (data.jvmInfo.heapMax).toFixed(2) : 0;
            const heapTotal = data.jvmInfo?.heapTotal ? (data.jvmInfo.heapTotal).toFixed(2) : 0;
            const heapUsagePercent = heapMax > 0 ? ((heapUsed / heapMax) * 100).toFixed(2) : 0;
            const nonHeapUsed = data.jvmInfo?.nonHeapUsed ? (data.jvmInfo.nonHeapUsed).toFixed(2) : 0;
            const nonHeapTotal = data.jvmInfo?.nonHeapTotal ? (data.jvmInfo.nonHeapTotal).toFixed(2) : 0;
            const totalThreads = data.jvmInfo?.totalThreads || 0;
            const activeThreads = data.jvmInfo?.activeThreads || 0;
            const daemonThreads = data.jvmInfo?.daemonThreads || 0;

            // 磁盘信息处理
            let diskUsage = 0;
            let totalDiskSpace = 0;
            let usedDiskSpace = 0;
            let freeDiskSpace = 0;
            if (data.diskInfo?.partitions && data.diskInfo.partitions.length > 0) {
                const mainPartition = data.diskInfo.partitions[0];
                diskUsage = mainPartition.usage ? parseFloat(mainPartition.usage).toFixed(2) : 0;
                totalDiskSpace = mainPartition.totalSpace ? parseFloat(mainPartition.totalSpace).toFixed(2) : 0;
                usedDiskSpace = mainPartition.usedSpace ? parseFloat(mainPartition.usedSpace).toFixed(2) : 0;
                freeDiskSpace = mainPartition.freeSpace ? parseFloat(mainPartition.freeSpace).toFixed(2) : 0;
            }

            // 数据库连接池信息
            let dbActiveConnections = 0;
            let dbMaxConnections = 0;
            let dbIdleConnections = 0;
            let dbTotalConnections = 0;
            let dbUsage = 0;
            let dbWaitingConnections = 0;
            if (data.databaseInfo?.connectionPools) {
                const poolKeys = Object.keys(data.databaseInfo.connectionPools);
                if (poolKeys.length > 0) {
                    const pool = data.databaseInfo.connectionPools[poolKeys[0]];
                    dbActiveConnections = pool.activeConnections || 0;
                    dbMaxConnections = pool.maxConnections || 0;
                    dbIdleConnections = pool.idleConnections || 0;
                    dbTotalConnections = pool.totalConnections || 0;
                    dbUsage = pool.usage ? parseFloat(pool.usage).toFixed(2) : 0;
                    dbWaitingConnections = pool.waitingConnections || 0;
                }
            }

            // 缓存信息
            const redisStatus = data.cacheInfo?.redisStatus || '未知';
            let cacheHitRate = 0;
            let cacheSize = 0;
            if (data.cacheInfo?.cacheStats) {
                const cacheKeys = Object.keys(data.cacheInfo.cacheStats);
                if (cacheKeys.length > 0) {
                    const cache = data.cacheInfo.cacheStats[cacheKeys[0]];
                    cacheHitRate = cache.hitRate ? parseFloat(cache.hitRate).toFixed(2) : 0;
                    cacheSize = cache.size || 0;
                }
            }

            // 进程信息
            const currentPid = data.processInfo?.currentPid || '--';
            const totalProcesses = data.processInfo?.totalProcesses || 0;
            const javaProcessCount = data.processInfo?.javaProcesses ? data.processInfo.javaProcesses.length : 0;

            // 创建驾驶舱风格的仪表盘HTML
            const html = `
                <div class="dashboard-container p-4" style="color: white;">
                    <!-- 主要性能指标仪表盘 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="dashboard-gauge text-center">
                                <div class="gauge-container position-relative">
                                    <div class="gauge-circle" style="background: conic-gradient(#ff6b6b 0deg, #ff6b6b ${cpuUsage * 3.6}deg, rgba(255,255,255,0.2) ${cpuUsage * 3.6}deg);">
                                        <div class="gauge-inner">
                                            <div class="gauge-value">${cpuUsage}%</div>
                                            <div class="gauge-label">CPU使用率</div>
                                        </div>
                                    </div>
                                    <div class="gauge-info mt-2">
                                        <small>核心数: ${availableProcessors}</small><br>
                                        <small>进程CPU: ${processCpuUsage}%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-gauge text-center">
                                <div class="gauge-container position-relative">
                                    <div class="gauge-circle" style="background: conic-gradient(#4ecdc4 0deg, #4ecdc4 ${memoryUsage * 3.6}deg, rgba(255,255,255,0.2) ${memoryUsage * 3.6}deg);">
                                        <div class="gauge-inner">
                                            <div class="gauge-value">${memoryUsage}%</div>
                                            <div class="gauge-label">内存使用率</div>
                                        </div>
                                    </div>
                                    <div class="gauge-info mt-2">
                                        <small>已用: ${usedMemory}GB</small><br>
                                        <small>总计: ${totalMemory}GB</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-gauge text-center">
                                <div class="gauge-container position-relative">
                                    <div class="gauge-circle" style="background: conic-gradient(#45b7d1 0deg, #45b7d1 ${heapUsagePercent * 3.6}deg, rgba(255,255,255,0.2) ${heapUsagePercent * 3.6}deg);">
                                        <div class="gauge-inner">
                                            <div class="gauge-value">${heapUsagePercent}%</div>
                                            <div class="gauge-label">JVM堆内存</div>
                                        </div>
                                    </div>
                                    <div class="gauge-info mt-2">
                                        <small>已用: ${heapUsed}MB</small><br>
                                        <small>最大: ${heapMax}MB</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="dashboard-gauge text-center">
                                <div class="gauge-container position-relative">
                                    <div class="gauge-circle" style="background: conic-gradient(#f7b731 0deg, #f7b731 ${diskUsage * 3.6}deg, rgba(255,255,255,0.2) ${diskUsage * 3.6}deg);">
                                        <div class="gauge-inner">
                                            <div class="gauge-value">${diskUsage}%</div>
                                            <div class="gauge-label">磁盘使用率</div>
                                        </div>
                                    </div>
                                    <div class="gauge-info mt-2">
                                        <small>已用: ${usedDiskSpace}GB</small><br>
                                        <small>总计: ${totalDiskSpace}GB</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细监控指标 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="dashboard-panel">
                                <h6><i class="fa fa-database"></i> 数据库连接池</h6>
                                <div class="metric-row">
                                    <span>活跃连接:</span>
                                    <span class="metric-value">${dbActiveConnections}/${dbMaxConnections}</span>
                                </div>
                                <div class="metric-row">
                                    <span>空闲连接:</span>
                                    <span class="metric-value">${dbIdleConnections}</span>
                                </div>
                                <div class="metric-row">
                                    <span>使用率:</span>
                                    <span class="metric-value">${dbUsage}%</span>
                                </div>
                                <div class="metric-row">
                                    <span>等待连接:</span>
                                    <span class="metric-value">${dbWaitingConnections}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="dashboard-panel">
                                <h6><i class="fa fa-cogs"></i> 线程信息</h6>
                                <div class="metric-row">
                                    <span>总线程数:</span>
                                    <span class="metric-value">${totalThreads}</span>
                                </div>
                                <div class="metric-row">
                                    <span>活跃线程:</span>
                                    <span class="metric-value">${activeThreads}</span>
                                </div>
                                <div class="metric-row">
                                    <span>守护线程:</span>
                                    <span class="metric-value">${daemonThreads}</span>
                                </div>
                                <div class="metric-row">
                                    <span>系统负载:</span>
                                    <span class="metric-value">${systemLoadAverage}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="dashboard-panel">
                                <h6><i class="fa fa-server"></i> 系统信息</h6>
                                <div class="metric-row">
                                    <span>当前进程:</span>
                                    <span class="metric-value">${currentPid}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Java进程:</span>
                                    <span class="metric-value">${javaProcessCount}</span>
                                </div>
                                <div class="metric-row">
                                    <span>总进程数:</span>
                                    <span class="metric-value">${totalProcesses}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Redis状态:</span>
                                    <span class="metric-value">${redisStatus}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 更新时间 -->
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <small style="opacity: 0.8;">
                                <i class="fa fa-clock-o"></i> 最后更新: ${formatDateTime(new Date())}
                            </small>
                        </div>
                    </div>
                </div>
            `;

            $('#performanceDashboard').html(html);
        }

        // 显示性能监控详情模态框
        async function showPerformanceModal() {
            try {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/overview`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200) {
                    showPerformanceDetailModal(result.data);
                } else {
                    console.error('获取性能监控详情失败:', result.message);
                    alert('获取性能监控详情失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('性能监控模态框显示失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                alert('网络请求失败: ' + error.message);
            }
        }

        // 显示性能监控详情模态框
        function showPerformanceDetailModal(data) {
            // 格式化数据，确保小数保留两位
            const formatValue = (value, unit = '') => {
                if (value === null || value === undefined) return '--';
                if (typeof value === 'number') {
                    return value.toFixed(2) + unit;
                }
                return value;
            };

            const modalHtml = `
                <div class="modal fade" id="performanceDetailModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fa fa-tachometer"></i> 系统性能监控详情
                                </h5>
                                <button type="button" class="close text-white" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                                <!-- 系统基本信息 -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <i class="fa fa-desktop"></i> 系统基本信息
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>操作系统:</strong> ${data.systemInfo?.osName || '--'}</p>
                                                        <p><strong>系统版本:</strong> ${data.systemInfo?.osVersion || '--'}</p>
                                                        <p><strong>系统架构:</strong> ${data.systemInfo?.osArch || '--'}</p>
                                                        <p><strong>Java版本:</strong> ${data.systemInfo?.javaVersion || '--'}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>应用启动时间:</strong> ${data.systemInfo?.startTime ? formatDateTime(data.systemInfo.startTime) : '--'}</p>
                                                        <p><strong>运行时长:</strong> ${data.systemInfo?.uptime ? formatDuration(data.systemInfo.uptime) : '--'}</p>
                                                        <p><strong>处理器核心数:</strong> ${data.systemInfo?.processorCount || '--'}</p>
                                                        <p><strong>监控时间:</strong> ${data.monitorTime ? formatDateTime(data.monitorTime) : '--'}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- CPU信息 -->
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header bg-danger text-white">
                                                <i class="fa fa-microchip"></i> CPU信息
                                            </div>
                                            <div class="card-body">
                                                <p><strong>CPU使用率:</strong> ${formatValue(data.cpuInfo?.cpuUsage, '%')}</p>
                                                <p><strong>进程CPU使用率:</strong> ${formatValue(data.cpuInfo?.processCpuUsage, '%')}</p>
                                                <p><strong>系统负载平均值:</strong> ${formatValue(data.cpuInfo?.systemLoadAverage)}</p>
                                                <p><strong>可用处理器数:</strong> ${data.cpuInfo?.availableProcessors || '--'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 内存信息 -->
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header bg-success text-white">
                                                <i class="fa fa-memory"></i> 内存信息
                                            </div>
                                            <div class="card-body">
                                                <p><strong>总内存:</strong> ${formatValue(data.memoryInfo?.totalMemory, 'MB')}</p>
                                                <p><strong>已用内存:</strong> ${formatValue(data.memoryInfo?.usedMemory, 'MB')}</p>
                                                <p><strong>可用内存:</strong> ${formatValue(data.memoryInfo?.freeMemory, 'MB')}</p>
                                                <p><strong>内存使用率:</strong> ${formatValue(data.memoryInfo?.memoryUsage, '%')}</p>
                                                <p><strong>交换区总大小:</strong> ${formatValue(data.memoryInfo?.totalSwap, 'MB')}</p>
                                                <p><strong>交换区已用:</strong> ${formatValue(data.memoryInfo?.usedSwap, 'MB')}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- JVM信息 -->
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header bg-warning text-white">
                                                <i class="fa fa-coffee"></i> JVM信息
                                            </div>
                                            <div class="card-body">
                                                <p><strong>堆内存总大小:</strong> ${formatValue(data.jvmInfo?.heapTotal, 'MB')}</p>
                                                <p><strong>堆内存已用:</strong> ${formatValue(data.jvmInfo?.heapUsed, 'MB')}</p>
                                                <p><strong>堆内存最大值:</strong> ${formatValue(data.jvmInfo?.heapMax, 'MB')}</p>
                                                <p><strong>非堆内存总大小:</strong> ${formatValue(data.jvmInfo?.nonHeapTotal, 'MB')}</p>
                                                <p><strong>非堆内存已用:</strong> ${formatValue(data.jvmInfo?.nonHeapUsed, 'MB')}</p>
                                                <p><strong>线程总数:</strong> ${data.jvmInfo?.totalThreads || '--'}</p>
                                                <p><strong>活跃线程数:</strong> ${data.jvmInfo?.activeThreads || '--'}</p>
                                                <p><strong>守护线程数:</strong> ${data.jvmInfo?.daemonThreads || '--'}</p>
                                                ${data.jvmInfo?.gcInfo && data.jvmInfo.gcInfo.length > 0 ?
                                                    '<p><strong>垃圾回收信息:</strong></p>' +
                                                    data.jvmInfo.gcInfo.map(gc =>
                                                        `<small>• ${gc.name}: ${gc.collectionCount}次, ${gc.collectionTime}ms</small>`
                                                    ).join('<br>') : ''}
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 磁盘信息 -->
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-header bg-secondary text-white">
                                                <i class="fa fa-hdd-o"></i> 磁盘信息
                                            </div>
                                            <div class="card-body">
                                                ${data.diskInfo?.partitions && data.diskInfo.partitions.length > 0 ?
                                                    data.diskInfo.partitions.map(partition => `
                                                        <div class="mb-3 p-2 border rounded">
                                                            <p><strong>分区路径:</strong> ${partition.path}</p>
                                                            <p><strong>总空间:</strong> ${formatValue(partition.totalSpace, 'GB')}</p>
                                                            <p><strong>已用空间:</strong> ${formatValue(partition.usedSpace, 'GB')}</p>
                                                            <p><strong>可用空间:</strong> ${formatValue(partition.freeSpace, 'GB')}</p>
                                                            <p><strong>使用率:</strong> ${formatValue(partition.usage, '%')}</p>
                                                        </div>
                                                    `).join('') : '<p>暂无磁盘信息</p>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 数据库连接池信息 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card mb-3">
                                            <div class="card-header bg-primary text-white">
                                                <i class="fa fa-database"></i> 数据库连接池信息
                                            </div>
                                            <div class="card-body">
                                                ${data.databaseInfo?.connectionPools && Object.keys(data.databaseInfo.connectionPools).length > 0 ?
                                                    Object.entries(data.databaseInfo.connectionPools).map(([poolName, pool]) => `
                                                        <div class="mb-3 p-3 border rounded">
                                                            <h6><strong>${pool.poolName || poolName}</strong></h6>
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p><strong>活跃连接数:</strong> ${pool.activeConnections || 0}</p>
                                                                    <p><strong>空闲连接数:</strong> ${pool.idleConnections || 0}</p>
                                                                    <p><strong>总连接数:</strong> ${pool.totalConnections || 0}</p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p><strong>最大连接数:</strong> ${pool.maxConnections || 0}</p>
                                                                    <p><strong>等待连接数:</strong> ${pool.waitingConnections || 0}</p>
                                                                    <p><strong>连接使用率:</strong> ${formatValue(pool.usage, '%')}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `).join('') : '<p>暂无连接池信息</p>'}

                                                ${data.databaseInfo?.deadlocks && data.databaseInfo.deadlocks.length > 0 ?
                                                    '<h6 class="mt-3"><strong>数据库死锁信息:</strong></h6>' +
                                                    data.databaseInfo.deadlocks.map(deadlock => `
                                                        <div class="alert alert-warning">
                                                            <p><strong>死锁ID:</strong> ${deadlock.deadlockId}</p>
                                                            <p><strong>死锁时间:</strong> ${formatDateTime(deadlock.deadlockTime)}</p>
                                                            <p><strong>涉及线程:</strong> ${deadlock.threads ? deadlock.threads.join(', ') : '--'}</p>
                                                            <p><strong>描述:</strong> ${deadlock.description || '--'}</p>
                                                        </div>
                                                    `).join('') : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 线程池信息 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card mb-3">
                                            <div class="card-header bg-info text-white">
                                                <i class="fa fa-cogs"></i> 线程池信息
                                            </div>
                                            <div class="card-body">
                                                ${data.threadPoolInfo?.threadPools && Object.keys(data.threadPoolInfo.threadPools).length > 0 ?
                                                    Object.entries(data.threadPoolInfo.threadPools).map(([poolName, pool]) => `
                                                        <div class="mb-3 p-3 border rounded">
                                                            <h6><strong>${pool.poolName || poolName}</strong></h6>
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p><strong>核心线程数:</strong> ${pool.corePoolSize || 0}</p>
                                                                    <p><strong>最大线程数:</strong> ${pool.maximumPoolSize || 0}</p>
                                                                    <p><strong>当前线程数:</strong> ${pool.poolSize || 0}</p>
                                                                    <p><strong>活跃线程数:</strong> ${pool.activeCount || 0}</p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p><strong>队列大小:</strong> ${pool.queueSize || 0}</p>
                                                                    <p><strong>已完成任务数:</strong> ${pool.completedTaskCount || 0}</p>
                                                                    <p><strong>总任务数:</strong> ${pool.taskCount || 0}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `).join('') : '<p>暂无线程池信息</p>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 缓存信息 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card mb-3">
                                            <div class="card-header bg-dark text-white">
                                                <i class="fa fa-server"></i> 缓存信息
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Redis连接状态:</strong>
                                                    <span class="badge ${data.cacheInfo?.redisStatus === '连接正常' ? 'badge-success' : 'badge-warning'}">
                                                        ${data.cacheInfo?.redisStatus || '未知'}
                                                    </span>
                                                </p>
                                                ${data.cacheInfo?.cacheStats && Object.keys(data.cacheInfo.cacheStats).length > 0 ?
                                                    '<h6 class="mt-3"><strong>缓存统计:</strong></h6>' +
                                                    Object.entries(data.cacheInfo.cacheStats).map(([cacheName, cache]) => `
                                                        <div class="mb-3 p-3 border rounded">
                                                            <h6><strong>${cache.cacheName || cacheName}</strong></h6>
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p><strong>缓存大小:</strong> ${cache.size || 0}</p>
                                                                    <p><strong>命中次数:</strong> ${cache.hitCount || 0}</p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p><strong>未命中次数:</strong> ${cache.missCount || 0}</p>
                                                                    <p><strong>命中率:</strong> ${formatValue(cache.hitRate, '%')}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `).join('') : '<p>暂无缓存统计信息</p>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 进程信息 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card mb-3">
                                            <div class="card-header bg-success text-white">
                                                <i class="fa fa-tasks"></i> 进程信息
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>当前进程ID:</strong> ${data.processInfo?.currentPid || '--'}</p>
                                                        <p><strong>系统进程数:</strong> ${data.processInfo?.totalProcesses || 0}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>Java进程数:</strong> ${data.processInfo?.javaProcesses ? data.processInfo.javaProcesses.length : 0}</p>
                                                    </div>
                                                </div>

                                                ${data.processInfo?.javaProcesses && data.processInfo.javaProcesses.length > 0 ?
                                                    '<h6 class="mt-3"><strong>Java进程列表:</strong></h6>' +
                                                    '<div style="max-height: 200px; overflow-y: auto;">' +
                                                    data.processInfo.javaProcesses.map(process => `
                                                        <div class="mb-2 p-2 border rounded">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p class="mb-1"><strong>PID:</strong> ${process.pid}</p>
                                                                    <p class="mb-1"><strong>进程名:</strong> ${process.name}</p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p class="mb-1"><strong>CPU使用率:</strong> ${formatValue(process.cpuUsage, '%')}</p>
                                                                    <p class="mb-1"><strong>内存使用:</strong> ${formatValue(process.memoryUsage, 'MB')}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `).join('') + '</div>' : '<p>暂无Java进程信息</p>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" onclick="refreshPerformanceModal()">
                                    <i class="fa fa-refresh"></i> 刷新数据
                                </button>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#performanceDetailModal').remove();
            // 添加新的模态框
            $('body').append(modalHtml);
            // 显示模态框
            $('#performanceDetailModal').modal('show');
        }

        // 刷新性能监控模态框数据
        async function refreshPerformanceModal() {
            try {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/overview`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.code === 200) {
                    // 重新生成模态框内容
                    showPerformanceDetailModal(result.data);
                } else {
                    console.error('刷新性能监控数据失败:', result.message);
                    alert('刷新性能监控数据失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('刷新性能监控数据失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                alert('网络请求失败: ' + error.message);
            }
        }

        // 更新Token状态显示
        function updateTokenStatus() {
            const statusElement = $('#token-status');
            const timeElement = $('#token-remaining-time');

            if (statusElement.length) {
                const isExpired = TokenManager.isTokenExpired();
                statusElement.text(isExpired ? '已过期' : '有效');
                statusElement.removeClass('badge-success badge-danger').addClass(isExpired ? 'badge-danger' : 'badge-success');
            }

            if (timeElement.length) {
                timeElement.text(TokenManager.formatRemainingTime());
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
