<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - 访问验证</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        .form-group label {
            font-weight: 600;
            color: #333;
        }
        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        .alert {
            border-radius: 8px;
            border: none;
            font-size: 14px;
        }
        .loading-spinner {
            display: none;
            margin-right: 10px;
        }
        .help-text {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 13px;
            color: #666;
        }
        .help-text h6 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .help-text ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
        .help-text li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fa fa-shield"></i> 系统监控中心</h2>
            <p>请输入有效的AccessToken以访问监控功能</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="accessToken">AccessToken</label>
                <input type="password" class="form-control" id="accessToken" name="accessToken" 
                       placeholder="请输入您的AccessToken" required>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-login" id="loginBtn">
                    <span class="loading-spinner">
                        <i class="fa fa-spinner fa-spin"></i>
                    </span>
                    <span class="btn-text">验证并进入</span>
                </button>
            </div>

            <div id="alertContainer"></div>
        </form>

        <div class="help-text">
            <h6><i class="fa fa-info-circle"></i> 访问说明</h6>
            <ul>
                <li>请联系系统管理员获取有效的访问令牌</li>
                <li>访问令牌具有时效性，过期后需要重新获取</li>
                <li>请妥善保管您的访问令牌，不要泄露给他人</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        $(document).ready(function() {
            // 检查URL参数中是否有redirect
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirect') || '/api/monitor/';

            // 检查本地存储的Token
            const storedToken = TokenManager.getToken();
            if (storedToken && !TokenManager.isTokenExpired()) {
                $('#accessToken').val(storedToken);
            }

            // 表单提交处理
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();
                
                const accessToken = $('#accessToken').val().trim();
                if (!accessToken) {
                    showAlert('alertContainer', '请输入AccessToken', 'danger');
                    return;
                }

                // 显示加载状态
                setLoading(true);
                clearAlert('alertContainer');

                // 验证Token
                $.ajax({
                    url: getApiBaseUrl() + '/monitor/validate-token',
                    method: 'GET',
                    data: { accessToken: accessToken },
                    success: function(response) {
                        setLoading(false);
                        
                        if (response.code === 200 && response.data === true) {
                            // 保存Token到本地存储
                            TokenManager.setToken(accessToken);
                            
                            // 跳转到目标页面，参考redis-management的实现方式
                            console.log('Token验证成功，开始处理跳转逻辑');
                            console.log('Token验证通过，准备跳转到管理页面');

                            // 延迟跳转到管理页面，参考redis-management的实现
                            setTimeout(() => {
                                console.log('准备跳转到管理页面...');
                                setTimeout(() => {
                                    try {
                                        const targetUrl = '/api/system-monitor/index.html?accessToken=' + encodeURIComponent(accessToken);
                                        console.log('开始跳转到:', targetUrl);
                                        console.log('当前location:', window.location.href);

                                        // 尝试多种跳转方式确保成功
                                        window.location.href = targetUrl;

                                        // 备用跳转方式
                                        setTimeout(() => {
                                            if (window.location.href.indexOf('index.html') === -1) {
                                                console.log('第一次跳转失败，尝试replace方式');
                                                window.location.replace(targetUrl);
                                            }
                                        }, 500);
                                    } catch (error) {
                                        console.error('跳转过程中发生错误:', error);
                                        alert('跳转失败，请手动访问管理页面');
                                    }
                                }, 2000);
                            }, 1000);
                        } else {
                            showAlert('alertContainer', 'AccessToken验证失败，请检查Token是否正确或已过期', 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        setLoading(false);
                        
                        let errorMessage = 'Token验证失败，请稍后重试';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        
                        showAlert('alertContainer', errorMessage, 'danger');
                    }
                });
            });

            // 回车键提交
            $('#accessToken').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#loginForm').submit();
                }
            });
        });

        function setLoading(loading) {
            const btn = $('#loginBtn');
            const spinner = $('.loading-spinner');
            const btnText = $('.btn-text');
            
            if (loading) {
                btn.prop('disabled', true);
                spinner.show();
                btnText.text('验证中...');
            } else {
                btn.prop('disabled', false);
                spinner.hide();
                btnText.text('验证并进入');
            }
        }
    </script>
</body>
</html>
