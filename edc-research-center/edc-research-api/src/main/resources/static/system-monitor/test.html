<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控功能测试</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .page-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .test-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
        }
        .token-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .token-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px;
            font-size: 12px;
        }
        .test-btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <!-- Token状态显示 -->
    <div class="token-status">
        <div class="token-info">
            <span>状态:</span>
            <span id="token-status" class="badge badge-success">有效</span>
        </div>
        <div class="token-info">
            <span>剩余:</span>
            <span id="token-remaining-time">--</span>
        </div>
        <button class="btn btn-outline-danger logout-btn" onclick="logout()">
            <i class="fa fa-sign-out"></i> 退出
        </button>
    </div>

    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fa fa-cogs text-info"></i> 系统监控功能测试
                    </h1>
                    <p class="text-muted mb-0">测试系统监控各项功能的可用性和性能</p>
                </div>
                <div class="col-auto">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="navigateToPage('index.html')">
                            <i class="fa fa-home"></i> 首页
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('access-logs.html')">
                            <i class="fa fa-list-alt"></i> 访问日志
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('online-users.html')">
                            <i class="fa fa-users"></i> 在线用户
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('exception-logs.html')">
                            <i class="fa fa-exclamation-triangle"></i> 异常日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Token验证测试 -->
        <div class="card test-card">
            <div class="card-header">
                <h5><i class="fa fa-key"></i> Token验证测试</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">测试AccessToken的验证功能</p>
                <button class="btn btn-primary test-btn" onclick="testTokenValidation()">
                    <i class="fa fa-check"></i> 验证当前Token
                </button>
                <button class="btn btn-warning test-btn" onclick="testInvalidToken()">
                    <i class="fa fa-times"></i> 测试无效Token
                </button>
                <div class="result-box" id="tokenTestResult">
                    <span class="text-muted">点击按钮开始测试...</span>
                </div>
            </div>
        </div>

        <!-- API接口测试 -->
        <div class="card test-card">
            <div class="card-header">
                <h5><i class="fa fa-plug"></i> API接口测试</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">测试系统监控相关API接口的可用性</p>
                <button class="btn btn-success test-btn" onclick="testStatisticsAPI()">
                    <i class="fa fa-bar-chart"></i> 统计概览API
                </button>
                <button class="btn btn-info test-btn" onclick="testAccessLogsAPI()">
                    <i class="fa fa-list"></i> 访问日志API
                </button>
                <button class="btn btn-warning test-btn" onclick="testOnlineUsersAPI()">
                    <i class="fa fa-users"></i> 在线用户API
                </button>
                <button class="btn btn-danger test-btn" onclick="testExceptionLogsAPI()">
                    <i class="fa fa-exclamation"></i> 异常日志API
                </button>
                <div class="result-box" id="apiTestResult">
                    <span class="text-muted">点击按钮开始测试...</span>
                </div>
            </div>
        </div>

        <!-- 性能监控测试 -->
        <div class="card test-card">
            <div class="card-header">
                <h5><i class="fa fa-tachometer"></i> 性能监控测试</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">测试系统性能监控功能</p>
                <button class="btn btn-primary test-btn" onclick="testPerformanceAPI()">
                    <i class="fa fa-server"></i> 系统性能API
                </button>
                <button class="btn btn-info test-btn" onclick="testCPUMemoryAPI()">
                    <i class="fa fa-microchip"></i> CPU内存API
                </button>
                <button class="btn btn-success test-btn" onclick="testDatabaseAPI()">
                    <i class="fa fa-database"></i> 数据库连接API
                </button>
                <div class="result-box" id="performanceTestResult">
                    <span class="text-muted">点击按钮开始测试...</span>
                </div>
            </div>
        </div>

        <!-- 批量测试 -->
        <div class="card test-card">
            <div class="card-header">
                <h5><i class="fa fa-tasks"></i> 批量测试</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">一键测试所有功能模块</p>
                <button class="btn btn-lg btn-primary" onclick="runAllTests()">
                    <i class="fa fa-play"></i> 运行所有测试
                </button>
                <button class="btn btn-lg btn-outline-secondary ml-2" onclick="clearAllResults()">
                    <i class="fa fa-trash"></i> 清空结果
                </button>
                <div class="mt-3">
                    <div class="progress" style="display: none;" id="testProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="result-box" id="batchTestResult">
                    <span class="text-muted">点击按钮开始批量测试...</span>
                </div>
            </div>
        </div>

        <!-- 测试报告 -->
        <div class="card test-card">
            <div class="card-header">
                <h5><i class="fa fa-file-text"></i> 测试报告</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success" id="successCount">0</h4>
                            <p class="text-muted">成功</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger" id="errorCount">0</h4>
                            <p class="text-muted">失败</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning" id="warningCount">0</h4>
                            <p class="text-muted">警告</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info" id="totalTests">0</h4>
                            <p class="text-muted">总计</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary" onclick="exportTestReport()">
                        <i class="fa fa-download"></i> 导出报告
                    </button>
                    <button class="btn btn-outline-info ml-2" onclick="shareTestReport()">
                        <i class="fa fa-share"></i> 分享报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        let testResults = [];
        let testStats = { success: 0, error: 0, warning: 0, total: 0 };

        $(document).ready(function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            updateTestStats();
        });

        // 添加测试结果
        function addTestResult(container, message, type = 'info', details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${type}`;
            const statusIndicator = `<span class="status-indicator ${statusClass}"></span>`;
            
            let resultHtml = `
                <div class="${type}">
                    ${statusIndicator}[${timestamp}] ${message}
                </div>
            `;
            
            if (details) {
                resultHtml += `<pre class="mt-2">${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            $(`#${container}`).append(resultHtml);
            $(`#${container}`).scrollTop($(`#${container}`)[0].scrollHeight);
            
            // 更新统计
            testStats[type]++;
            testStats.total++;
            updateTestStats();
            
            // 保存测试结果
            testResults.push({
                timestamp: new Date(),
                container,
                message,
                type,
                details
            });
        }

        // 更新测试统计
        function updateTestStats() {
            $('#successCount').text(testStats.success);
            $('#errorCount').text(testStats.error);
            $('#warningCount').text(testStats.warning);
            $('#totalTests').text(testStats.total);
        }

        // Token验证测试
        async function testTokenValidation() {
            const container = 'tokenTestResult';
            addTestResult(container, '开始验证当前Token...', 'info');
            
            try {
                const token = TokenManager.getToken();
                if (!token) {
                    addTestResult(container, 'Token不存在', 'error');
                    return;
                }
                
                const response = await fetch(`${getApiBaseUrl()}/monitor/validate-token?accessToken=${encodeURIComponent(token)}`);
                const result = await response.json();
                
                if (result.code === 200 && result.data === true) {
                    addTestResult(container, 'Token验证成功', 'success', result);
                } else {
                    addTestResult(container, 'Token验证失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, 'Token验证异常', 'error', error.message);
            }
        }

        // 测试无效Token
        async function testInvalidToken() {
            const container = 'tokenTestResult';
            addTestResult(container, '测试无效Token...', 'info');
            
            try {
                const response = await fetch(`${getApiBaseUrl()}/monitor/validate-token?accessToken=invalid_token`);
                const result = await response.json();
                
                if (result.code !== 200 || result.data !== true) {
                    addTestResult(container, '无效Token正确被拒绝', 'success', result);
                } else {
                    addTestResult(container, '无效Token被错误接受', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '无效Token测试异常', 'warning', error.message);
            }
        }

        // 统计概览API测试
        async function testStatisticsAPI() {
            const container = 'apiTestResult';
            addTestResult(container, '测试统计概览API...', 'info');
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/statistics/overview`);
                const result = await response.json();
                
                if (result.code === 200) {
                    addTestResult(container, '统计概览API测试成功', 'success', result.data);
                } else {
                    addTestResult(container, '统计概览API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '统计概览API测试异常', 'error', error.message);
            }
        }

        // 访问日志API测试
        async function testAccessLogsAPI() {
            const container = 'apiTestResult';
            addTestResult(container, '测试访问日志API...', 'info');
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/access-logs/list?pageNum=1&pageSize=5`);
                const result = await response.json();
                
                if (result.code === 200) {
                    addTestResult(container, '访问日志API测试成功', 'success', {
                        totalRecords: result.data?.total || 0,
                        currentPage: result.data?.pageNum || 1
                    });
                } else {
                    addTestResult(container, '访问日志API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '访问日志API测试异常', 'error', error.message);
            }
        }

        // 在线用户API测试
        async function testOnlineUsersAPI() {
            const container = 'apiTestResult';
            addTestResult(container, '测试在线用户API...', 'info');
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/online-users/list?pageNum=1&pageSize=5`);
                const result = await response.json();
                
                if (result.code === 200) {
                    addTestResult(container, '在线用户API测试成功', 'success', {
                        totalRecords: result.data?.total || 0,
                        currentPage: result.data?.pageNum || 1
                    });
                } else {
                    addTestResult(container, '在线用户API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '在线用户API测试异常', 'error', error.message);
            }
        }

        // 异常日志API测试
        async function testExceptionLogsAPI() {
            const container = 'apiTestResult';
            addTestResult(container, '测试异常日志API...', 'info');
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/list?pageNum=1&pageSize=5`);
                const result = await response.json();
                
                if (result.code === 200) {
                    addTestResult(container, '异常日志API测试成功', 'success', {
                        totalRecords: result.data?.total || 0,
                        currentPage: result.data?.pageNum || 1
                    });
                } else {
                    addTestResult(container, '异常日志API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '异常日志API测试异常', 'error', error.message);
            }
        }

        // 性能监控API测试
        async function testPerformanceAPI() {
            const container = 'performanceTestResult';
            addTestResult(container, '测试系统性能API...', 'info');

            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/overview`);
                const result = await response.json();

                if (result.code === 200) {
                    addTestResult(container, '系统性能API测试成功', 'success', {
                        hasSystemInfo: !!result.data?.systemInfo,
                        hasJvmInfo: !!result.data?.jvmInfo,
                        hasCpuInfo: !!result.data?.cpuInfo
                    });
                } else {
                    addTestResult(container, '系统性能API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '系统性能API测试异常', 'error', error.message);
            }
        }

        // CPU内存API测试
        async function testCPUMemoryAPI() {
            const container = 'performanceTestResult';
            addTestResult(container, '测试CPU内存API...', 'info');

            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/cpu`);
                const result = await response.json();

                if (result.code === 200) {
                    addTestResult(container, 'CPU内存API测试成功', 'success', result.data);
                } else {
                    addTestResult(container, 'CPU内存API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, 'CPU内存API测试异常', 'error', error.message);
            }
        }

        // 数据库连接API测试
        async function testDatabaseAPI() {
            const container = 'performanceTestResult';
            addTestResult(container, '测试数据库连接API...', 'info');

            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/performance/database`);
                const result = await response.json();

                if (result.code === 200) {
                    addTestResult(container, '数据库连接API测试成功', 'success', result.data);
                } else {
                    addTestResult(container, '数据库连接API测试失败', 'error', result);
                }
            } catch (error) {
                addTestResult(container, '数据库连接API测试异常', 'error', error.message);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            const container = 'batchTestResult';
            const progress = $('#testProgress');
            const progressBar = progress.find('.progress-bar');
            
            // 清空之前的结果
            $(`#${container}`).empty();
            testStats = { success: 0, error: 0, warning: 0, total: 0 };
            updateTestStats();
            
            // 显示进度条
            progress.show();
            progressBar.css('width', '0%');
            
            addTestResult(container, '开始批量测试...', 'info');
            
            const tests = [
                { name: 'Token验证', func: testTokenValidation },
                { name: '统计概览API', func: testStatisticsAPI },
                { name: '访问日志API', func: testAccessLogsAPI },
                { name: '在线用户API', func: testOnlineUsersAPI },
                { name: '异常日志API', func: testExceptionLogsAPI },
                { name: '系统性能API', func: testPerformanceAPI },
                { name: 'CPU内存API', func: testCPUMemoryAPI },
                { name: '数据库连接API', func: testDatabaseAPI }
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                addTestResult(container, `正在执行: ${test.name}`, 'info');
                
                try {
                    await test.func();
                    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
                } catch (error) {
                    addTestResult(container, `${test.name} 执行异常: ${error.message}`, 'error');
                }
                
                // 更新进度
                const progress_percent = ((i + 1) / tests.length) * 100;
                progressBar.css('width', `${progress_percent}%`);
            }
            
            addTestResult(container, '批量测试完成', 'success');
            
            // 隐藏进度条
            setTimeout(() => {
                progress.hide();
            }, 1000);
        }

        // 清空所有结果
        function clearAllResults() {
            $('.result-box').html('<span class="text-muted">已清空结果...</span>');
            testResults = [];
            testStats = { success: 0, error: 0, warning: 0, total: 0 };
            updateTestStats();
        }

        // 导出测试报告
        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                stats: testStats,
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-monitor-test-report-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            addTestResult('batchTestResult', '测试报告已导出', 'success');
        }

        // 分享测试报告
        function shareTestReport() {
            const report = `
系统监控测试报告
生成时间: ${new Date().toLocaleString()}
测试统计: 成功 ${testStats.success}, 失败 ${testStats.error}, 警告 ${testStats.warning}, 总计 ${testStats.total}
            `.trim();
            
            if (navigator.share) {
                navigator.share({
                    title: '系统监控测试报告',
                    text: report
                });
            } else {
                navigator.clipboard.writeText(report).then(() => {
                    addTestResult('batchTestResult', '测试报告已复制到剪贴板', 'success');
                });
            }
        }
    </script>
</body>
</html>
