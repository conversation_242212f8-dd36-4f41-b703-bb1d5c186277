<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复验证测试页面</h1>
        <p>本页面用于验证日志管理和集群监控的修复效果</p>

        <!-- 问题1：日志管理页面重定向问题测试 -->
        <div class="test-section">
            <h2>🔍 问题1：日志管理页面重定向问题测试</h2>
            <p>测试用户通过秘钥和AccessToken验证后是否会重新跳转到登录页面</p>
            
            <button class="test-button" onclick="testLogManagementLogin()">测试日志管理登录流程</button>
            <button class="test-button" onclick="openLogManagementPage()">打开日志管理登录页面</button>
            <button class="test-button" onclick="testTokenRefresh()">测试Token刷新机制</button>
            
            <div id="logManagementResult" class="result" style="display:none;"></div>
            <div id="logManagementLog" class="log" style="display:none;"></div>
            
            <div style="margin-top: 15px;">
                <iframe id="logManagementFrame" style="display:none;" src="about:blank"></iframe>
            </div>
        </div>

        <!-- 问题2：集群监控登录接口路径问题测试 -->
        <div class="test-section">
            <h2>🔍 问题2：集群监控登录接口路径问题测试</h2>
            <p>测试集群监控登录接口是否包含正确的端口号</p>
            
            <button class="test-button" onclick="testClusterMonitorLogin()">测试集群监控登录流程</button>
            <button class="test-button" onclick="openClusterMonitorPage()">打开集群监控登录页面</button>
            <button class="test-button" onclick="testUrlBuilding()">测试URL构建逻辑</button>
            
            <div id="clusterMonitorResult" class="result" style="display:none;"></div>
            <div id="clusterMonitorLog" class="log" style="display:none;"></div>
            
            <div style="margin-top: 15px;">
                <iframe id="clusterMonitorFrame" style="display:none;" src="about:blank"></iframe>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🧪 综合测试</h2>
            <p>运行所有测试用例</p>
            
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
            
            <div id="overallResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
            
            // 添加到日志显示区域
            const logElements = document.querySelectorAll('.log');
            logElements.forEach(logEl => {
                if (logEl.style.display !== 'none') {
                    logEl.innerHTML += `[${timestamp}] ${message}\n`;
                    logEl.scrollTop = logEl.scrollHeight;
                }
            });
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        function showLog(logId) {
            const logElement = document.getElementById(logId);
            logElement.style.display = 'block';
            logElement.innerHTML = '';
        }

        // 测试日志管理登录流程
        async function testLogManagementLogin() {
            showLog('logManagementLog');
            log('🔍 开始测试日志管理登录流程...');
            
            try {
                // 测试登录页面是否可访问
                log('📡 测试登录页面访问...');
                const loginResponse = await fetch('/api/log-management/log-login.html');
                if (loginResponse.ok) {
                    log('✅ 登录页面访问正常');
                } else {
                    throw new Error(`登录页面访问失败: ${loginResponse.status}`);
                }

                // 测试token刷新接口是否存在
                log('📡 测试token刷新接口...');
                const refreshResponse = await fetch('/api/log-management/auth/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': 'test-token'
                    }
                });
                
                if (refreshResponse.status === 401 || refreshResponse.status === 400) {
                    log('✅ Token刷新接口存在且正常响应（预期的认证失败）');
                } else {
                    log(`⚠️ Token刷新接口响应状态: ${refreshResponse.status}`);
                }

                showResult('logManagementResult', '✅ 日志管理登录流程测试完成，接口正常', 'success');
                testResults.push({test: '日志管理登录', result: 'success'});
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                showResult('logManagementResult', `❌ 测试失败: ${error.message}`, 'error');
                testResults.push({test: '日志管理登录', result: 'error', error: error.message});
            }
        }

        // 打开日志管理页面
        function openLogManagementPage() {
            const frame = document.getElementById('logManagementFrame');
            frame.src = '/api/log-management/log-login.html';
            frame.style.display = 'block';
            log('📖 已打开日志管理登录页面');
        }

        // 测试Token刷新机制
        async function testTokenRefresh() {
            showLog('logManagementLog');
            log('🔄 测试Token刷新机制...');
            
            try {
                const response = await fetch('/api/log-management/auth/refresh-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                log(`📡 刷新接口响应: ${JSON.stringify(result)}`);
                
                if (result.code === 400 && result.message.includes('缺少访问令牌')) {
                    log('✅ Token刷新接口正常工作（正确处理缺少token的情况）');
                    showResult('logManagementResult', '✅ Token刷新机制正常', 'success');
                } else {
                    log(`⚠️ 意外的响应: ${JSON.stringify(result)}`);
                    showResult('logManagementResult', '⚠️ Token刷新响应异常', 'info');
                }
                
            } catch (error) {
                log(`❌ Token刷新测试失败: ${error.message}`);
                showResult('logManagementResult', `❌ Token刷新测试失败: ${error.message}`, 'error');
            }
        }

        // 测试集群监控登录流程
        async function testClusterMonitorLogin() {
            showLog('clusterMonitorLog');
            log('🔍 开始测试集群监控登录流程...');
            
            try {
                // 测试登录页面是否可访问
                log('📡 测试集群监控登录页面访问...');
                const loginResponse = await fetch('/api/cluster-monitor/login.html');
                if (loginResponse.ok) {
                    log('✅ 集群监控登录页面访问正常');
                } else {
                    throw new Error(`集群监控登录页面访问失败: ${loginResponse.status}`);
                }

                // 测试登录接口
                log('📡 测试集群监控登录接口...');
                const loginApiResponse = await fetch('/api/cluster/monitor/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        secretKey: 'test-key'
                    })
                });
                
                if (loginApiResponse.status === 400 || loginApiResponse.status === 401) {
                    log('✅ 集群监控登录接口存在且正常响应（预期的认证失败）');
                } else {
                    log(`⚠️ 集群监控登录接口响应状态: ${loginApiResponse.status}`);
                }

                showResult('clusterMonitorResult', '✅ 集群监控登录流程测试完成，接口正常', 'success');
                testResults.push({test: '集群监控登录', result: 'success'});
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                showResult('clusterMonitorResult', `❌ 测试失败: ${error.message}`, 'error');
                testResults.push({test: '集群监控登录', result: 'error', error: error.message});
            }
        }

        // 打开集群监控页面
        function openClusterMonitorPage() {
            const frame = document.getElementById('clusterMonitorFrame');
            frame.src = '/api/cluster-monitor/login.html';
            frame.style.display = 'block';
            log('📖 已打开集群监控登录页面');
        }

        // 测试URL构建逻辑
        function testUrlBuilding() {
            showLog('clusterMonitorLog');
            log('🔧 测试URL构建逻辑...');
            
            // 模拟URL构建函数
            function buildApiUrl(path) {
                const protocol = window.location.protocol;
                const hostname = window.location.hostname;
                const port = window.location.port;
                
                let baseUrl = `${protocol}//${hostname}`;
                if (port && port !== '80' && port !== '443') {
                    baseUrl += `:${port}`;
                }
                
                return baseUrl + '/api/cluster/monitor' + path;
            }
            
            const testUrl = buildApiUrl('/login');
            log(`🔗 构建的URL: ${testUrl}`);
            log(`🔧 当前位置 - 协议: ${window.location.protocol}, 主机: ${window.location.hostname}, 端口: ${window.location.port}`);
            
            if (testUrl.includes(':8000')) {
                log('✅ URL构建正确，包含端口号');
                showResult('clusterMonitorResult', '✅ URL构建逻辑正确', 'success');
            } else {
                log('⚠️ URL构建可能有问题，未包含端口号');
                showResult('clusterMonitorResult', '⚠️ URL构建需要检查', 'info');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            testResults = [];
            log('🚀 开始运行所有测试...');
            
            await testLogManagementLogin();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            
            await testClusterMonitorLogin();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            
            testUrlBuilding();
            
            // 显示综合结果
            const successCount = testResults.filter(r => r.result === 'success').length;
            const totalCount = testResults.length;
            
            if (successCount === totalCount) {
                showResult('overallResult', `🎉 所有测试通过！(${successCount}/${totalCount})`, 'success');
            } else {
                showResult('overallResult', `⚠️ 部分测试失败 (${successCount}/${totalCount})`, 'error');
            }
            
            log(`📊 测试完成: ${successCount}/${totalCount} 通过`);
        }

        // 清除结果
        function clearResults() {
            const results = document.querySelectorAll('.result, .log');
            results.forEach(el => {
                el.style.display = 'none';
                el.innerHTML = '';
            });
            
            const frames = document.querySelectorAll('iframe');
            frames.forEach(frame => {
                frame.style.display = 'none';
                frame.src = 'about:blank';
            });
            
            testResults = [];
            console.clear();
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('🔧 修复验证测试页面已加载');
            log('📍 当前访问地址: ' + window.location.href);
        });
    </script>
</body>
</html>
