<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能对话 - EDC科研协作平台</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.8.0/styles/github.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            height: 100vh;
            width: 100%;
            background: white;
        }

        .sidebar {
            width: 280px;
            min-width: 280px;
            background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 8px rgba(0,0,0,0.05);
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .new-chat-btn {
            width: 100%;
            margin-top: 10px;
        }
        
        .session-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        
        .session-item {
            padding: 14px 16px;
            margin-bottom: 10px;
            background: white;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .session-item:hover {
            border-color: #409eff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }

        .session-item.active {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        
        .session-title {
            font-weight: 500;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .session-info {
            font-size: 12px;
            opacity: 0.7;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .chat-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .header-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .chat-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .control-panel {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            padding: 15px 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
        }

        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .control-label {
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            min-width: fit-content;
        }



        /* 响应式设计 */
        @media (max-width: 768px) {
            .control-panel {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .control-item {
                width: 100%;
                justify-content: space-between;
            }

            .stream-mode-selector {
                flex-direction: column;
                gap: 5px;
            }

            .sidebar {
                width: 250px;
                min-width: 250px;
            }

            .main-content {
                width: calc(100vw - 250px);
                max-width: calc(100vw - 250px);
            }

            .message-content {
                max-width: 85%;
                min-width: 150px;
            }
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px 30px;
            background: linear-gradient(to bottom, #f8f9fa 0%, #f1f3f4 100%);
            position: relative;
            min-height: 0;
            width: 100%;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0,0,0,0.3);
        }
        
        .message {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            animation: fadeInUp 0.3s ease-out;
        }

        .message.user {
            justify-content: flex-end !important;
            flex-direction: row-reverse !important;
        }

        .message.assistant {
            justify-content: flex-start !important;
            flex-direction: row !important;
        }

        .message.user .message-content {
            order: 1;
        }

        .message.user .message-avatar {
            order: 2;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            margin: 0 12px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            order: 2;
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        }
        
        .message-content {
            max-width: 85%;
            padding: 14px 18px;
            border-radius: 18px;
            background: white;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            position: relative;
            word-wrap: break-word;
            line-height: 1.5;
            min-width: 200px;
            flex: 1;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;
            border-bottom-right-radius: 6px;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 6px;
        }
        
        .message-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .message-time {
            font-size: 12px;
            color: #999;
        }

        .message.user .message-time {
            color: rgba(255,255,255,0.8);
        }

        .message-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .message-actions .el-button {
            padding: 4px 6px;
            font-size: 12px;
            color: #999;
        }

        .message-actions .el-button:hover {
            color: #409eff;
        }

        .message.user .message-actions .el-button {
            color: rgba(255,255,255,0.8);
        }

        .message.user .message-actions .el-button:hover {
            color: white;
        }
        
        .chat-input {
            padding: 20px 24px;
            background: white;
            border-top: 1px solid #e9ecef;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
            position: sticky;
            bottom: 0;
            z-index: 100;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .input-wrapper {
            max-width: 800px;
            width: 100%;
        }
        
        .input-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 4px;
        }

        .input-stats {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666;
        }

        .char-count {
            color: #409eff;
        }

        .file-count {
            color: #67c23a;
        }

        .input-tips {
            font-size: 12px;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }
        
        .input-area {
            flex: 1;
            position: relative;
        }

        .input-toolbar {
            position: absolute;
            right: 8px;
            bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: calc(100% - 16px);
            pointer-events: none;
        }

        .toolbar-left,
        .toolbar-right {
            display: flex;
            gap: 4px;
            pointer-events: auto;
        }

        .input-toolbar .el-button {
            padding: 4px 6px;
            color: #999;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 4px;
            backdrop-filter: blur(4px);
        }

        .input-toolbar .el-button:hover {
            color: #409eff;
            background: rgba(255, 255, 255, 0.9);
        }

        .uploaded-files {
            margin-top: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .file-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
            font-size: 12px;
        }

        .file-item i {
            color: #409eff;
        }

        .file-name {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-size {
            color: #999;
            font-size: 11px;
        }

        .remove-file {
            padding: 2px 4px !important;
            color: #f56c6c !important;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #999;
            font-size: 14px;
            padding: 10px 0;
        }
        
        .typing-dots {
            display: flex;
            gap: 3px;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .token-usage {
            padding: 10px 15px;
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            margin-top: 10px;
            font-size: 12px;
            color: #0369a1;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            padding: 40px 20px;
        }

        .welcome-container {
            max-width: 600px;
            text-align: center;
        }

        .welcome-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
        }

        .welcome-icon i {
            font-size: 36px;
            color: white;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            padding: 24px 16px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }

        .feature-card i {
            font-size: 24px;
            color: #409eff;
            margin-bottom: 12px;
        }

        .feature-card h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .feature-card p {
            font-size: 14px;
            color: #666;
            margin: 0;
            line-height: 1.4;
        }

        .quick-actions {
            background: rgba(64, 158, 255, 0.05);
            padding: 24px;
            border-radius: 12px;
            border: 1px solid rgba(64, 158, 255, 0.1);
        }

        .quick-actions h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
        }

        .action-buttons .el-button {
            border-radius: 20px;
            padding: 8px 16px;
        }

        /* 消息内容样式 */
        .message-content pre {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.45;
        }

        .message-content code {
            background: #f6f8fa;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 85%;
            color: #e83e8c;
        }

        .message-content pre code {
            background: transparent;
            padding: 0;
            color: inherit;
        }

        .message-content blockquote {
            border-left: 4px solid #dfe2e5;
            padding-left: 16px;
            margin: 8px 0;
            color: #6a737d;
        }

        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 8px 0;
        }

        .message-content th,
        .message-content td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }

        .message-content th {
            background: #f6f8fa;
            font-weight: 600;
        }

        .message-content ul,
        .message-content ol {
            padding-left: 20px;
            margin: 8px 0;
        }

        .message-content li {
            margin: 4px 0;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* 桌面端优化 */
        @media (min-width: 1200px) {
            .chat-container {
                min-width: 1200px;
            }

            .sidebar {
                width: 320px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
                max-width: 100%;
                margin: 0;
            }

            .sidebar {
                width: 100%;
                height: 200px;
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }

            .session-list {
                flex-direction: row;
                overflow-x: auto;
                overflow-y: hidden;
                padding: 10px;
            }

            .session-item {
                min-width: 200px;
                margin-right: 10px;
                margin-bottom: 0;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-messages {
                padding: 16px;
            }

            .chat-input {
                padding: 16px;
            }
        }

        @media (max-width: 480px) {
            .message-content {
                max-width: 90%;
                padding: 12px 14px;
            }

            .message-avatar {
                width: 32px;
                height: 32px;
                font-size: 14px;
                margin: 0 8px;
            }

            .chat-messages {
                padding: 12px;
            }

            .chat-input {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="chat-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-title">
                        <i class="fas fa-robot"></i> AI对话
                    </div>
                    <!-- 新建对话按钮移到这里 -->
                    <el-button type="primary" size="small" @click="createNewSession" class="new-chat-btn">
                        <i class="fas fa-plus"></i> 新建对话
                    </el-button>
                </div>

                <div class="session-list">
                    <div v-if="sessions.length === 0" class="empty-state">
                        <i class="fas fa-comments empty-icon"></i>
                        <p>暂无对话记录</p>
                    </div>

                    <div v-for="session in sessions"
                         :key="session.sessionId"
                         :class="['session-item', { active: currentSessionId === session.sessionId }]"
                         @click="selectSession(session.sessionId)">
                        <div class="session-title">{{ session.title }}</div>
                        <div class="session-info">
                            {{ session.modelType }} · {{ formatTime(session.lastMessageTime) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <div class="chat-header">
                    <!-- 顶部统一控制区 - 居中显示 -->
                    <div class="header-controls">
                        <!-- 会话标题 -->
                        <div class="chat-title">
                            {{ currentSession ? currentSession.title : 'AI智能对话' }}
                        </div>

                        <!-- 控制面板 -->
                        <div class="control-panel">
                            <!-- 模型选择 -->
                            <div class="control-item">
                                <span class="control-label">模型:</span>
                                <el-select v-model="selectedModel" size="small" placeholder="选择模型" style="width: 200px;">
                                    <el-option
                                        v-for="model in availableModels"
                                        :key="model.modelType + ':' + model.modelName"
                                        :label="model.description || (model.modelType + ' - ' + model.modelName)"
                                        :value="model.modelType + ':' + model.modelName">
                                    </el-option>
                                </el-select>
                            </div>

                            <!-- 数据返回格式 -->
                            <div class="control-item">
                                <span class="control-label">数据返回格式:</span>
                                <el-switch
                                    v-model="streamMode"
                                    active-text="流式"
                                    inactive-text="普通">
                                </el-switch>
                            </div>

                            <!-- 用户状态 -->
                            <div class="control-item">
                                <span class="control-label">账号:</span>
                                <el-tag :type="authStatus.isAuthenticated ? 'success' : 'info'" size="small">
                                    <i :class="authStatus.isAuthenticated ? 'fas fa-user' : 'fas fa-user-secret'"></i>
                                    {{ authStatus.userName }}
                                </el-tag>
                            </div>

                            <!-- 使用限制状态 -->
                            <div class="control-item">
                                <span class="control-label">使用状态:</span>
                                <el-tag v-if="!authStatus.isAuthenticated" type="warning" size="small">
                                    今日剩余: {{ usageStats.remainingCount }}/{{ authStatus.dailyLimit }}
                                </el-tag>
                                <el-tag v-if="authStatus.isAuthenticated" type="success" size="small">
                                    无限制使用
                                </el-tag>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chat-messages" ref="messagesContainer">
                    <div v-if="messages.length === 0" class="empty-state">
                        <div class="welcome-container">
                            <div class="welcome-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h2 class="welcome-title">欢迎使用AI智能助手</h2>
                            <p class="welcome-subtitle">我是您的专业AI助手，可以帮助您解决各种问题</p>

                            <div class="feature-cards">
                                <div class="feature-card">
                                    <i class="fas fa-comments"></i>
                                    <h4>智能对话</h4>
                                    <p>支持多轮对话，理解上下文</p>
                                </div>
                                <div class="feature-card">
                                    <i class="fas fa-file-upload"></i>
                                    <h4>文件分析</h4>
                                    <p>上传文档，获得专业分析</p>
                                </div>
                                <div class="feature-card">
                                    <i class="fas fa-lightbulb"></i>
                                    <h4>创意助手</h4>
                                    <p>写作、翻译、编程等多种能力</p>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <h4>快速开始</h4>
                                <div class="action-buttons">
                                    <el-button size="small" @click="sendQuickMessage('你好，请介绍一下你的功能')">
                                        <i class="fas fa-hand-wave"></i> 打个招呼
                                    </el-button>
                                    <el-button size="small" @click="sendQuickMessage('帮我写一份工作总结')">
                                        <i class="fas fa-edit"></i> 写作助手
                                    </el-button>
                                    <el-button size="small" @click="sendQuickMessage('解释一下人工智能的原理')">
                                        <i class="fas fa-question-circle"></i> 知识问答
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div v-for="message in messages" :key="message.messageId" :class="['message', message.role]">
                        <div class="message-avatar">
                            <i :class="message.role === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text" v-html="formatMessage(message.content)"></div>
                            <div class="message-footer">
                                <div class="message-time">{{ formatTime(message.createTime) }}</div>
                                <div class="message-actions">
                                    <el-button size="mini" type="text" @click="copyMessage(message.content)" title="复制">
                                        <i class="fas fa-copy"></i>
                                    </el-button>
                                    <el-button v-if="message.role === 'assistant'" size="mini" type="text" @click="regenerateMessage(message)" title="重新生成">
                                        <i class="fas fa-redo"></i>
                                    </el-button>
                                    <el-button size="mini" type="text" @click="deleteMessage(message)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </el-button>
                                </div>
                            </div>
                            <div v-if="message.tokenUsage" class="token-usage">
                                Token: {{ message.tokenUsage.totalTokens }} | 成本: ¥{{ message.tokenUsage.totalCost }}
                            </div>
                        </div>
                    </div>
                    
                    <div v-if="isTyping" class="typing-indicator">
                        <div class="message-avatar" style="background: #67c23a;">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div>
                            AI正在思考
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="input-wrapper">
                        <!-- 输入提示和统计 -->
                        <div class="input-header" v-if="inputMessage.length > 0 || uploadedFiles.length > 0">
                            <div class="input-stats">
                                <span class="char-count">{{ inputMessage.length }} 字符</span>
                                <span v-if="uploadedFiles.length > 0" class="file-count">
                                    <i class="fas fa-file"></i> {{ uploadedFiles.length }} 个文件
                                </span>
                            </div>
                            <div class="input-tips">
                                <el-tag size="mini" type="info">Ctrl+Enter 快速发送</el-tag>
                            </div>
                        </div>

                        <div class="input-container">
                            <div class="input-area">
                                <el-input type="textarea"
                                         v-model="inputMessage"
                                         :rows="inputMessage.length > 100 ? 4 : 3"
                                         placeholder="输入您的问题... (支持Markdown格式，Ctrl+Enter发送)"
                                         @keydown.ctrl.enter="sendMessage"
                                         @keydown.shift.enter="addNewLine"
                                         :disabled="isLoading"
                                         resize="none">
                                </el-input>

                                <!-- 文件上传和工具栏 -->
                                <div class="input-toolbar">
                                    <div class="toolbar-left">
                                        <el-upload :before-upload="handleFileUpload"
                                                  :show-file-list="false"
                                                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.md,.xls,.xlsx,.ppt,.pptx">
                                            <el-button size="mini" type="text" title="上传文件">
                                                <i class="fas fa-paperclip"></i>
                                            </el-button>
                                        </el-upload>

                                        <el-button size="mini" type="text" @click="clearInput" title="清空输入" v-if="inputMessage.length > 0">
                                            <i class="fas fa-eraser"></i>
                                        </el-button>
                                    </div>

                                    <div class="toolbar-right">
                                        <el-button size="mini" type="text" @click="showMarkdownHelp" title="Markdown帮助">
                                            <i class="fab fa-markdown"></i>
                                        </el-button>
                                    </div>
                                </div>
                            </div>

                            <el-button type="primary"
                                      @click="sendMessage"
                                      :loading="isLoading"
                                      :disabled="!inputMessage.trim() && uploadedFiles.length === 0"
                                      size="medium">
                                <i class="fas fa-paper-plane"></i> 发送
                            </el-button>
                        </div>

                        <!-- 已上传文件列表 -->
                        <div class="uploaded-files" v-if="uploadedFiles.length > 0">
                            <div class="file-list">
                                <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                                    <i :class="getFileIcon(file.name)"></i>
                                    <span class="file-name">{{ file.name }}</span>
                                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                                    <el-button size="mini" type="text" @click="removeFile(index)" class="remove-file">
                                        <i class="fas fa-times"></i>
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.13/index.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.27.2/axios.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/marked/4.0.18/marked.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>

    <!-- 用户信息初始化脚本 -->
    <script>
        // 页面加载时尝试获取用户信息
        (function() {
            // 尝试从服务器端获取用户信息（如果是通过模板引擎渲染的）
            // 这里可以通过Thymeleaf等模板引擎注入用户信息

            // 检查是否有通过模板引擎注入的用户信息
            if (typeof window.serverUserInfo !== 'undefined' && window.serverUserInfo) {
                console.log('从服务器端获取到用户信息:', window.serverUserInfo);

                // 存储到sessionStorage
                try {
                    sessionStorage.setItem('userInfo', JSON.stringify(window.serverUserInfo));
                    console.log('用户信息已存储到sessionStorage');
                } catch (e) {
                    console.warn('存储用户信息到sessionStorage失败:', e);
                }

                // 设置到全局变量
                window.currentUser = window.serverUserInfo;
            }

            // 尝试从cookie中获取用户信息（如果登录时设置了）
            function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            }

            // 如果没有从服务器端获取到用户信息，尝试从cookie获取
            if (!window.currentUser) {
                const userId = getCookie('userId');
                const userName = getCookie('userName');
                const realName = getCookie('realName');

                if (userId) {
                    const userInfo = {
                        userId: decodeURIComponent(userId),
                        userName: userName ? decodeURIComponent(userName) : '用户',
                        realName: realName ? decodeURIComponent(realName) : '用户'
                    };

                    console.log('从cookie获取到用户信息:', userInfo);

                    // 存储到sessionStorage
                    try {
                        sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
                        console.log('用户信息已存储到sessionStorage');
                    } catch (e) {
                        console.warn('存储用户信息到sessionStorage失败:', e);
                    }

                    // 设置到全局变量
                    window.currentUser = userInfo;
                }
            }

            console.log('当前用户信息:', window.currentUser || '未登录');
        })();
    </script>

    <script src="/api/ai-chat/js/ai-chat.js"></script>
</body>
</html>
