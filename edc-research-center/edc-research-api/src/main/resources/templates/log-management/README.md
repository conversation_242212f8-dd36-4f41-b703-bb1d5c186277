# EDC 日志管理系统

## 概述

EDC 日志管理系统提供了一个完整的日志查看和管理解决方案，支持实时日志流、文件下载、WebSocket连接管理等功能。

## 目录结构

```
log-management/
├── README.md                    # 说明文档
├── log-login.html              # 日志登录页面（templates目录）
├── log-viewer.html             # 日志查看器页面（templates目录，已合并static版本功能）
└── test-token.html             # Token验证测试页面

注意：static/log-management/ 目录下的文件已被删除，统一使用templates版本
```

## 访问方式

### 1. 通过API端点访问（推荐）

#### 日志登录页面
- **URL**: `/api/log-management/login`
- **描述**: 动态生成的日志登录页面，支持步骤式验证流程

#### 静态日志登录页面
- **URL**: `/api/log-management/log-login.html`
- **描述**: 静态日志登录页面，直接从static目录提供

#### 日志查看器页面
- **URL**: `/api/log-management/viewer/page?accessToken=<token>`
- **描述**: 需要有效的accessToken才能访问

#### 静态日志查看器页面
- **URL**: `/api/log-management/log-viewer.html?accessToken=<token>`
- **描述**: 静态日志查看器页面，支持URL参数传递accessToken

### 2. 通过静态资源访问

如果配置了静态资源访问，也可以直接访问：
- `/static/log-management/log-login.html`
- `/static/log-management/log-viewer.html`

## 使用流程

### 步骤1：获取访问令牌
1. 访问日志登录页面
2. 输入配置文件中的 `log.viewer.access-token` 值
3. 点击"验证令牌"按钮

### 步骤2：获取验证码
1. 调用 `/api/secure/token/getAccessToken` 接口获取验证码
2. 在登录页面输入获取到的 `code` 和 `refreshCode`
3. 点击"验证并登录"按钮

### 步骤3：访问日志系统
验证成功后，系统会自动跳转到日志查看器页面。

## API接口

### 获取日志查看器信息
```
GET /api/log-management/viewer?accessToken=<token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "日志查看器访问授权成功",
  "data": {
    "message": "日志查看器访问授权成功",
    "websocketUrl": "ws://localhost:8080/api/websocket/logs/{clientId}",
    "logViewerPage": "/log-management/log-viewer.html",
    "logFilePath": "/path/to/logs/application.log",
    "serverInfo": {
      "address": "localhost",
      "port": "8080",
      "contextPath": "/api",
      "environment": "dev"
    },
    "accessGranted": true,
    "tokenExpireTime": "2025-01-15 14:30:00"
  }
}
```

### 获取系统状态
```
GET /api/log-management/status
```

### 获取日志文件列表
```
GET /api/log-management/files
```

### 下载日志文件
```
GET /api/log-management/download/{filename}?accessToken=<token>
```

## 配置说明

### 必需配置项

```yaml
# 日志查看器访问令牌
log:
  viewer:
    access-token: edc-log-viewer-2025-dev
    token-expire-minutes: 60

# 安全Token管理配置
secure-token:
  enabled: true
  secret-key: "EDC-SECURE-TOKEN-SECRET-KEY-2025"
  code-expiration: 120
  access-token-expiration: 3600
  redis-prefix: "secure:token:"

# 日志文件配置
logging:
  file:
    name: logs/application.log
  pattern:
    rolling-file-name: logs/application-%d{yyyy-MM-dd}.%i.log
```

### 可选配置项

```yaml
# WebSocket服务器地址（用于远程部署）
websocket:
  server:
    address: ${server.address:localhost}

# 服务器配置
server:
  address: localhost
  port: 8080
  servlet:
    context-path: /api
```

## 功能特性

### 日志查看器功能
- ✅ 实时日志流显示
- ✅ WebSocket连接状态监控
- ✅ 日志搜索和过滤
- ✅ 自动滚动控制
- ✅ 日志下载功能
- ✅ 键盘快捷键支持
- ✅ 响应式设计

### 安全特性
- ✅ 访问令牌验证
- ✅ 双重验证机制（accessToken + code/refreshCode）
- ✅ 令牌过期时间显示
- ✅ 动态域名和端口获取

### 部署特性
- ✅ 支持nginx代理部署
- ✅ 动态获取访问域名和端口
- ✅ 兼容不同环境配置
- ✅ 静态资源和动态页面双重支持

## 键盘快捷键

- `Ctrl+K` / `Cmd+K`: 清空日志
- `Ctrl+F` / `Cmd+F`: 聚焦搜索框
- `Ctrl+S` / `Cmd+S`: 下载日志
- `Enter`: 在登录页面提交表单

## 故障排除

### 常见问题

1. **无法连接WebSocket**
   - 检查服务器地址和端口配置
   - 确认防火墙设置
   - 验证WebSocket路径是否正确

2. **访问令牌验证失败**
   - 检查配置文件中的 `log.viewer.access-token` 值
   - 确认令牌是否已过期

3. **页面加载失败**
   - 检查静态资源配置
   - 确认文件路径是否正确
   - 查看服务器日志获取详细错误信息

### 日志级别

系统支持以下日志级别的颜色区分：
- `INFO`: 白色文本
- `WARN`: 黄色文本，浅黄色背景
- `ERROR`: 红色文本，浅红色背景
- `DEBUG`: 绿色文本
- `TRACE`: 蓝色文本

## 更新日志

### v2.0.0 (2025-01-15)
- ✅ 重构LogManagementController，支持动态域名和端口获取
- ✅ 修改tokenExpireTime返回格式为yyyy-MM-dd HH:mm:ss
- ✅ 新增日志登录页面，支持步骤式验证流程
- ✅ 新增静态日志查看器页面，支持URL参数传递accessToken
- ✅ 优化页面目录结构，便于维护管理
- ✅ 增强安全验证机制，集成secure/token验证流程

### v1.0.0
- 基础日志查看功能
- WebSocket实时日志流
- 基本的访问控制
