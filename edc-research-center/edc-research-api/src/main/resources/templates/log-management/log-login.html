<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDC 日志系统登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e1e5e9;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.completed {
            background: #27ae60;
            color: white;
        }

        .step-line {
            width: 50px;
            height: 2px;
            background: #e1e5e9;
            margin-top: 14px;
        }

        .step-line.completed {
            background: #27ae60;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .info-text {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: left;
            font-size: 14px;
            color: #666;
        }

        .info-text strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>EDC 日志系统</h1>
            <p>请完成身份验证以访问日志管理系统</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step-line" id="line1"></div>
            <div class="step" id="step2">2</div>
            <div class="step-line" id="line2"></div>
            <div class="step" id="step3">3</div>
        </div>

        <!-- 第一步：输入配置秘钥 -->
        <div id="step1-form">
            <div class="info-text">
                <strong>步骤 1:</strong> 请输入系统配置秘钥
            </div>
            <div class="form-group">
                <label for="secretKey">配置秘钥</label>
                <input type="password" id="secretKey" placeholder="请输入配置秘钥" required>
            </div>
            <button class="btn" onclick="verifySecretKey()">
                <span id="step1-loading" class="loading hidden"></span>
                验证秘钥
            </button>
            <div id="step1-error" class="error-message"></div>
        </div>

        <!-- 第二步：输入验证码 -->
        <div id="step2-form" class="hidden">
            <div class="info-text">
                <strong>步骤 2:</strong> 请输入通过 <code>/secure/token/getAccessToken</code> 获取的验证码
            </div>
            <div class="form-group">
                <label for="code">验证码 (Code)</label>
                <input type="text" id="code" placeholder="请输入验证码" required>
            </div>
            <div class="form-group">
                <label for="refreshCode">刷新码 (RefreshCode)</label>
                <input type="text" id="refreshCode" placeholder="请输入刷新码" required>
            </div>
            <button class="btn" onclick="validateCodes()">
                <span id="step2-loading" class="loading hidden"></span>
                验证并登录
            </button>
            <div id="step2-error" class="error-message"></div>
        </div>

        <!-- 第三步：登录成功 -->
        <div id="step3-form" class="hidden">
            <div class="success-message" style="display: block;">
                ✅ 验证成功！正在跳转到日志管理系统...
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let validatedAccessToken = '';

        // 构建API URL（与log-viewer.html保持一致的逻辑）
        function buildApiUrl(path) {
            // 使用当前页面的基础路径构建API URL，避免路径拼接问题
            const currentUrl = window.location.href;

            if (currentUrl.includes('/log-management/')) {
                // 提取基础路径并构建API URL
                const baseUrl = currentUrl.substring(0, currentUrl.indexOf('/log-management/'));
                const fullUrl = baseUrl + '/log-management' + path;
                console.log('🔗 构建API URL (基于当前路径):', fullUrl);
                return fullUrl;
            } else {
                // 备用方案：使用origin + /api/log-management
                const fullUrl = window.location.origin + '/api/log-management' + path;
                console.log('🔗 构建API URL (备用方案):', fullUrl);
                return fullUrl;
            }
        }

        // 验证配置秘钥
        async function verifySecretKey() {
            const secretKey = document.getElementById('secretKey').value.trim();
            if (!secretKey) {
                showError('step1-error', '请输入配置秘钥');
                return;
            }

            showLoading('step1-loading', true);
            hideError('step1-error');

            try {
                const response = await fetch(buildApiUrl('/auth/verify-secret'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ secretKey: secretKey })
                });

                const result = await response.json();

                if (result.code === 200) {
                    nextStep();
                } else {
                    showError('step1-error', result.message || '秘钥验证失败');
                }
            } catch (error) {
                console.error('验证配置秘钥失败:', error);
                showError('step1-error', '网络错误，请稍后重试');
            } finally {
                showLoading('step1-loading', false);
            }
        }

        // 验证Token
        async function validateCodes() {
            const code = document.getElementById('code').value.trim();
            const refreshCode = document.getElementById('refreshCode').value.trim();

            if (!code || !refreshCode) {
                showError('step2-error', '请输入验证码和刷新码');
                return;
            }

            showLoading('step2-loading', true);
            hideError('step2-error');

            try {
                const response = await fetch(buildApiUrl('/auth/verify-token'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        refreshCode: refreshCode
                    })
                });

                const result = await response.json();

                if (result.code === 200 && result.data && result.data.accessToken) {
                    // 存储 AccessToken 到 sessionStorage
                    sessionStorage.setItem('accessToken', result.data.accessToken);
                    console.log('AccessToken 已存储到 sessionStorage:', result.data.accessToken);

                    nextStep();
                    // 跳转到日志查看器页面，同时在URL中传递AccessToken
                    setTimeout(() => {
                        const accessToken = result.data.accessToken;

                        // 彻底修复：使用当前页面的基础路径构建viewer URL
                        const currentUrl = window.location.href;
                        let targetUrl;

                        if (currentUrl.includes('/log-management/')) {
                            // 使用当前URL的基础路径，替换页面名称
                            targetUrl = currentUrl.substring(0, currentUrl.indexOf('/log-management/')) +
                                       '/log-management/log-viewer.html?accessToken=' + encodeURIComponent(accessToken);
                        } else {
                            // 备用方案
                            targetUrl = buildApiUrl(`/log-viewer.html?accessToken=${encodeURIComponent(accessToken)}`);
                        }

                        console.log('🔗 跳转到日志查看器页面');
                        console.log('📍 当前URL:', currentUrl);
                        console.log('🎯 目标URL:', targetUrl);
                        window.location.href = targetUrl;
                    }, 2000);
                } else {
                    showError('step2-error', result.message || 'Token验证失败');
                }
            } catch (error) {
                console.error('验证Token失败:', error);
                showError('step2-error', '网络错误，请稍后重试');
            } finally {
                showLoading('step2-loading', false);
            }
        }

        // 下一步
        function nextStep() {
            if (currentStep < 3) {
                // 隐藏当前步骤
                document.getElementById(`step${currentStep}-form`).classList.add('hidden');
                
                // 更新步骤指示器
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                if (currentStep < 3) {
                    document.getElementById(`line${currentStep}`).classList.add('completed');
                }
                
                currentStep++;
                
                // 显示下一步骤
                document.getElementById(`step${currentStep}-form`).classList.remove('hidden');
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError(elementId) {
            const errorElement = document.getElementById(elementId);
            errorElement.style.display = 'none';
        }

        // 显示/隐藏加载动画
        function showLoading(elementId, show) {
            const loadingElement = document.getElementById(elementId);
            if (show) {
                loadingElement.classList.remove('hidden');
            } else {
                loadingElement.classList.add('hidden');
            }
        }

        // 回车键提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (currentStep === 1) {
                    verifySecretKey();
                } else if (currentStep === 2) {
                    validateCodes();
                }
            }
        });
    </script>
</body>
</html>
