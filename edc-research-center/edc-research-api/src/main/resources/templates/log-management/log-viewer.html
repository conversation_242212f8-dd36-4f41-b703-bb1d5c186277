<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EDC 日志管理系统</title>

    <!-- 动态域名适配器 -->
    <link rel="stylesheet" href="/common/css/dynamic-domain-themes.css">
    <script src="/common/js/dynamic-domain-adapter.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: #2d2d30;
            padding: 10px 20px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #569cd6;
            font-size: 18px;
            font-weight: normal;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #f44747;
        }

        .status-dot.connected {
            background: #4ec9b0;
        }

        .status-dot.valid {
            background: #28a745;
        }

        .status-dot.invalid {
            background: #dc3545;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
            margin-left: 10px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 300px;
            background: #252526;
            border-right: 1px solid #3e3e42;
            padding: 15px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-section:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-bottom: 0;
        }

        .sidebar-section h3 {
            color: #cccccc;
            font-size: 14px;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #3e3e42;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            color: #cccccc;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            background: #3c3c3c;
            border: 1px solid #5a5a5a;
            color: #d4d4d4;
            font-size: 12px;
            border-radius: 3px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007acc;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #0e639c;
            color: white;
        }

        .btn-primary:hover {
            background: #1177bb;
        }

        .btn-secondary {
            background: #5a5a5a;
            color: white;
        }

        .btn-secondary:hover {
            background: #6a6a6a;
        }

        .btn-success {
            background: #16825d;
            color: white;
        }

        .btn-success:hover {
            background: #1e9973;
        }

        .btn-danger {
            background: #c5282f;
            color: white;
        }

        .btn-danger:hover {
            background: #d73a41;
        }

        .file-list {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            background: #3c3c3c;
            min-height: 200px;
        }

        .file-item {
            padding: 8px 12px;
            border-bottom: 1px solid #5a5a5a;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-item:hover {
            background: #4a4a4a;
        }

        .file-item.selected {
            background: #094771;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-size: 12px;
            color: #d4d4d4;
        }

        .file-meta {
            font-size: 10px;
            color: #858585;
            margin-top: 2px;
        }

        .file-actions {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 10px;
        }

        .log-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1e1e1e;
        }

        .log-controls {
            background: #2d2d30;
            padding: 10px 15px;
            border-bottom: 1px solid #3e3e42;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .log-output {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .log-entry {
            margin-bottom: 2px;
            padding: 2px 0;
        }

        .log-entry.error {
            color: #f44747;
        }

        .log-entry.warn {
            color: #ffcc02;
        }

        .log-entry.info {
            color: #4fc1ff;
        }

        .log-entry.debug {
            color: #b5cea8;
        }

        .log-entry.system {
            color: #dcdcaa;
        }

        .loading {
            text-align: center;
            color: #858585;
            padding: 20px;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .message.show {
            opacity: 1;
            transform: translateX(0);
        }

        .message.success {
            background: #16825d;
        }

        .message.error {
            background: #c5282f;
        }

        .message.info {
            background: #0e639c;
        }

        .filter-group {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .checkbox-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            color: #cccccc;
            cursor: pointer;
        }

        .checkbox-group input[type="checkbox"] {
            margin: 0;
        }

        .search-box {
            flex: 1;
            max-width: 200px;
        }

        .stats {
            font-size: 11px;
            color: #858585;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #2d2d30;
        }

        ::-webkit-scrollbar-thumb {
            background: #5a5a5a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #6a6a6a;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 EDC 日志管理系统</h1>
        <div class="status">
            <div class="status-item">
                <div class="status-dot" id="connectionStatus"></div>
                <span id="connectionText">未连接</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="tokenStatus"></div>
                <span id="tokenStatusText">验证中...</span>
            </div>
            <div class="status-item">
                <span>过期时间: </span>
                <span id="tokenExpireTime">--</span>
            </div>
            <div class="status-item">
                <span>📊</span>
                <span id="logCount">0 条日志</span>
            </div>
            <div class="status-item">
                <span>⏱️</span>
                <span id="lastUpdate">--:--:--</span>
            </div>
            <button class="logout-btn" id="logoutBtn" onclick="logout()" style="display: none;">退出</button>
            <button class="logout-btn" id="reloginBtn" onclick="relogin()" style="display: none; background: #ffc107; color: #000;">重新登录</button>
        </div>
    </div>

    <div class="main-content">
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>🔗 连接设置</h3>
                <div class="form-group">
                    <label for="serverUrl">服务器地址</label>
                    <input type="text" id="serverUrl" class="form-control" placeholder="自动获取...">
                </div>
                <div class="form-group">
                    <label for="clientId">客户端ID</label>
                    <input type="text" id="clientId" class="form-control" value="log-viewer-001">
                </div>
                <div class="form-group">
                    <button id="connectBtn" class="btn btn-primary" style="width: 100%;">连接</button>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>📁 日志文件</h3>
                <div class="form-group">
                    <button id="refreshFilesBtn" class="btn btn-secondary" style="width: 100%;">刷新文件列表</button>
                </div>
                <div id="fileList" class="file-list">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <div class="log-controls">
                <div class="filter-group">
                    <label>级别过滤:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" id="filterError" checked> ERROR</label>
                        <label><input type="checkbox" id="filterWarn" checked> WARN</label>
                        <label><input type="checkbox" id="filterInfo" checked> INFO</label>
                        <label><input type="checkbox" id="filterDebug" checked> DEBUG</label>
                        <label><input type="checkbox" id="filterSystem" checked> SYSTEM</label>
                    </div>
                </div>
                
                <div class="filter-group">
                    <label for="searchBox">搜索:</label>
                    <input type="text" id="searchBox" class="form-control search-box" placeholder="关键词搜索...">
                </div>
                
                <div class="filter-group">
                    <button id="clearLogsBtn" class="btn btn-secondary btn-small">清空日志</button>
                    <button id="exportLogsBtn" class="btn btn-success btn-small">导出日志</button>
                    <button id="pauseBtn" class="btn btn-secondary btn-small">暂停</button>
                </div>
                
                <div class="stats">
                    <span id="filteredCount">显示: 0</span> | 
                    <span id="totalCount">总计: 0</span>
                </div>
            </div>
            
            <div id="logOutput" class="log-output">
                <div class="loading">等待连接...</div>
            </div>
        </div>
    </div>

    <div id="messageContainer"></div>

    <script>
        class LogViewer {
            constructor() {
                this.websocket = null;
                this.isConnected = false;
                this.isPaused = false;
                this.logs = [];
                this.filteredLogs = [];
                this.serverConfig = null;
                this.elements = {};
                this.currentFile = null;
                this.realTimeUpdateTimer = null;
                this.lastFileSize = 0;
                this.isRealTimeMode = false;
                // 修复：添加token状态监控相关属性
                this.tokenRefreshTimer = null;
                this.tokenStatusTimer = null;
                this.tokenExpireTime = null;
                this.tokenRemainingSeconds = null;
            }

            init() {
                console.log('🚀 初始化日志查看器...');
                this.initElements();
                this.bindEvents();

                // 首先检查是否有AccessToken
                const accessToken = this.getAccessToken();
                console.log('🔍 检查AccessToken:', accessToken ? '存在' : '不存在');

                if (!accessToken) {
                    console.log('❌ 未找到AccessToken，直接跳转到登录页面');
                    this.redirectToLogin();
                    return;
                }

                // 如果有token，进行验证
                this.validateToken().then(isValid => {
                    if (isValid) {
                        console.log('✅ Token验证成功，加载系统配置');
                        this.loadServerConfig();
                        this.refreshFileList();
                    } else {
                        console.log('❌ Token验证失败，跳转到登录页面');
                        this.redirectToLogin();
                    }
                }).catch(error => {
                    console.error('❌ Token验证异常:', error);
                    this.redirectToLogin();
                });
            }

            initElements() {
                this.elements = {
                    serverUrl: document.getElementById('serverUrl'),
                    clientId: document.getElementById('clientId'),
                    connectBtn: document.getElementById('connectBtn'),
                    refreshFilesBtn: document.getElementById('refreshFilesBtn'),
                    fileList: document.getElementById('fileList'),
                    logOutput: document.getElementById('logOutput'),
                    connectionStatus: document.getElementById('connectionStatus'),
                    connectionText: document.getElementById('connectionText'),
                    logCount: document.getElementById('logCount'),
                    lastUpdate: document.getElementById('lastUpdate'),
                    clearLogsBtn: document.getElementById('clearLogsBtn'),
                    exportLogsBtn: document.getElementById('exportLogsBtn'),
                    pauseBtn: document.getElementById('pauseBtn'),
                    searchBox: document.getElementById('searchBox'),
                    filteredCount: document.getElementById('filteredCount'),
                    totalCount: document.getElementById('totalCount'),
                    filterError: document.getElementById('filterError'),
                    filterWarn: document.getElementById('filterWarn'),
                    filterInfo: document.getElementById('filterInfo'),
                    filterDebug: document.getElementById('filterDebug'),
                    filterSystem: document.getElementById('filterSystem'),
                    tokenStatus: document.getElementById('tokenStatus'),
                    tokenStatusText: document.getElementById('tokenStatusText'),
                    tokenExpireTime: document.getElementById('tokenExpireTime'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    reloginBtn: document.getElementById('reloginBtn')
                };
            }

            bindEvents() {
                this.elements.connectBtn.addEventListener('click', () => this.toggleConnection());
                this.elements.refreshFilesBtn.addEventListener('click', () => this.refreshFileList());
                this.elements.clearLogsBtn.addEventListener('click', () => this.clearLogs());
                this.elements.exportLogsBtn.addEventListener('click', () => this.exportLogs());
                this.elements.pauseBtn.addEventListener('click', () => this.togglePause());
                this.elements.searchBox.addEventListener('input', () => this.filterLogs());

                // 过滤器事件
                [this.elements.filterError, this.elements.filterWarn, this.elements.filterInfo,
                 this.elements.filterDebug, this.elements.filterSystem].forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.filterLogs());
                });

                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'k':
                                e.preventDefault();
                                this.clearLogs();
                                break;
                            case 'f':
                                e.preventDefault();
                                this.elements.searchBox.focus();
                                break;
                            case 's':
                                e.preventDefault();
                                this.exportLogs();
                                break;
                        }
                    }
                });
            }

            async loadServerConfig() {
                try {
                    console.log('🔧 开始加载服务器配置...');

                    // 优先使用注入的配置
                    if (window.SERVER_CONFIG) {
                        this.serverConfig = window.SERVER_CONFIG;
                        this.elements.serverUrl.value = this.serverConfig.websocketUrl.replace('{clientId}', '');
                        this.elements.serverUrl.placeholder = '已通过配置注入获取服务器地址';
                        console.log('✅ 使用注入的服务器配置:', this.serverConfig);
                        return;
                    }

                    // 回退到API获取（需要accessToken）
                    const urlParams = new URLSearchParams(window.location.search);
                    const accessToken = urlParams.get('accessToken') || urlParams.get('token');
                    console.log('🔍 检查URL参数中的accessToken:', accessToken ? accessToken.substring(0, 20) + '...' : '未找到');

                    if (accessToken) {
                        const apiUrl = this.buildApiUrl(`/viewer?accessToken=${accessToken}`);
                        console.log('🌐 尝试通过API获取服务器配置:', apiUrl);

                        const response = await fetch(apiUrl);
                        console.log('📡 API响应状态:', response.status, response.statusText);

                        if (response.ok) {
                            const result = await response.json();
                            console.log('📋 API响应数据:', result);

                            if (result.code === 200) {
                                this.serverConfig = result.data;
                                this.elements.serverUrl.value = result.data.websocketUrl.replace('{clientId}', '');
                                this.elements.serverUrl.placeholder = '已通过API获取服务器配置';
                                console.log('✅ 通过API获取服务器配置成功:', result.data);
                                return;
                            }
                        }
                    }

                    // 最后回退到默认配置（基于local环境：localhost:8000/api）
                    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsHost = window.location.host; // localhost:8000
                    // 根据local环境配置：context-path: /api，WebSocket路径：/websocket/logs/
                    this.elements.serverUrl.value = `${wsProtocol}//${wsHost}/api/websocket/logs/{clientId}`;
                    this.elements.serverUrl.placeholder = '使用默认配置(local环境)';
                    console.log('⚠️ 使用默认WebSocket配置(local环境):', this.elements.serverUrl.value);

                } catch (error) {
                    console.error('加载服务器配置失败:', error);
                    this.showMessage('加载服务器配置失败', 'error');
                }
            }

            buildApiUrl(path) {
                // 动态域名方案：优先使用DynamicDomainAdapter
                if (window.DynamicDomainAdapter && window.DynamicDomainAdapter.isInitialized) {
                    const fullUrl = window.DynamicDomainAdapter.buildApiUrl('/log-management' + path);
                    console.log('🌐 使用动态域名适配器构建API URL:', fullUrl);
                    console.log('🏷️ 客户端类型:', window.DynamicDomainAdapter.getConfig('clientType'));
                    console.log('🎨 主题:', window.DynamicDomainAdapter.getTheme());
                    return fullUrl;
                }

                // 回退方案1：使用服务器配置的requestUrl
                if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
                    const baseUrl = window.SERVER_CONFIG.requestUrl;

                    let apiBaseUrl = baseUrl;
                    if (!apiBaseUrl.endsWith('/api') && !apiBaseUrl.endsWith('/api/')) {
                        if (apiBaseUrl.endsWith('/')) {
                            apiBaseUrl += 'api';
                        } else {
                            apiBaseUrl += '/api';
                        }
                    }

                    const fullUrl = apiBaseUrl + '/log-management' + path;

                    console.log('🔗 使用服务器配置构建API URL:', fullUrl);
                    console.log('⚙️ 配置来源:', window.SERVER_CONFIG.configSource || 'server-config');

                    return fullUrl;
                }

                // 回退方案2：使用当前页面的基础路径
                const currentUrl = window.location.href;
                const baseUrl = currentUrl.substring(0, currentUrl.indexOf('/log-management/'));
                const fullUrl = baseUrl + '/log-management' + path;

                console.log('🔗 使用备用方案构建API URL:', fullUrl);
                console.log('📍 当前URL:', currentUrl);

                return fullUrl;
            }

            async refreshFileList() {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        this.showMessage('缺少访问令牌，无法获取文件列表', 'error');
                        console.error('❌ 缺少访问令牌');
                        return;
                    }

                    const apiUrl = this.buildApiUrl('/files');
                    console.log('🔄 刷新文件列表，请求URL:', apiUrl);
                    console.log('🔑 使用访问令牌:', accessToken.substring(0, 20) + '...');

                    const response = await fetch(apiUrl, {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('📡 API响应状态:', response.status, response.statusText);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('📋 API响应数据:', result);

                        if (result.code === 200) {
                            this.lastFileList = result.data.files; // 保存文件列表
                            this.renderFileList(result.data.files);
                            this.showMessage(`已加载 ${result.data.count} 个日志文件`, 'success');
                            console.log('✅ 文件列表刷新成功，文件数量:', result.data.count);
                        } else {
                            this.showMessage(`获取文件列表失败: ${result.message}`, 'error');
                            console.error('❌ API返回错误:', result);
                        }
                    } else {
                        const errorText = await response.text();
                        this.showMessage(`获取文件列表失败: HTTP ${response.status}`, 'error');
                        console.error('❌ HTTP请求失败:', response.status, errorText);
                    }
                } catch (error) {
                    console.error('❌ 刷新文件列表异常:', error);
                    this.showMessage(`刷新文件列表失败: ${error.message}`, 'error');
                }
            }

            renderFileList(files) {
                if (!files || files.length === 0) {
                    this.elements.fileList.innerHTML = '<div class="loading">暂无日志文件</div>';
                    return;
                }

                const html = files.map(file => `
                    <div class="file-item" data-filename="${file.name}">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-meta">${this.formatFileSize(file.size)} | ${this.formatDate(file.lastModified)}</div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-primary btn-small" onclick="logViewer.viewFile('${file.name}')">查看</button>
                            <button class="btn btn-success btn-small" onclick="logViewer.downloadFile('${file.name}')">下载</button>
                        </div>
                    </div>
                `).join('');

                this.elements.fileList.innerHTML = html;

                // 标记当前查看的文件
                if (this.currentFile) {
                    const currentItem = this.elements.fileList.querySelector(`[data-filename="${this.currentFile}"]`);
                    if (currentItem) {
                        currentItem.classList.add('selected');
                    }
                }
            }

            async viewFile(filename) {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        throw new Error('缺少访问令牌');
                    }

                    // 停止之前的实时更新
                    this.stopRealTimeUpdate();

                    // 设置当前文件
                    this.currentFile = filename;
                    this.isRealTimeMode = true;

                    const response = await fetch(this.buildApiUrl(`/tail/${filename}?lines=100`), {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken,
                            'Content-Type': 'application/json'
                        }
                    });
                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            this.clearLogs();
                            result.data.lines.forEach((line, index) => {
                                this.addLogEntry({
                                    timestamp: new Date().toISOString(),
                                    level: this.detectLogLevel(line),
                                    body: line,
                                    sequence: Date.now() + index
                                });
                            });

                            // 记录文件大小用于实时更新
                            this.lastFileSize = result.data.fileSize || 0;

                            this.showMessage(`已加载文件 ${filename} 的最后 ${result.data.actualLines} 行`, 'success');

                            // 启动实时更新
                            this.startRealTimeUpdate();

                            // 更新文件列表选中状态
                            this.renderFileList(this.lastFileList || []);
                        }
                    }
                } catch (error) {
                    this.showMessage(`查看文件失败: ${error.message}`, 'error');
                }
            }

            async downloadFile(filename) {
                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) {
                        throw new Error('缺少访问令牌');
                    }

                    // 修复：使用正确的下载URL和处理方式
                    const downloadUrl = this.buildApiUrl(`/download/${filename}`);

                    // 使用fetch下载文件（支持header验证）
                    const response = await fetch(downloadUrl, {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken
                        }
                    });

                    if (response.ok) {
                        const blob = await response.blob();
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = filename;
                        link.style.display = 'none';

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        this.showMessage(`开始下载文件: ${filename}`, 'success');
                    } else {
                        throw new Error(`下载失败: HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('下载文件失败:', error);
                    this.showMessage(`下载文件失败: ${error.message}`, 'error');
                }
            }

            getAccessToken() {
                console.log('🔍 开始获取AccessToken...');

                // 第一优先级：从服务器配置获取（服务器端注入）
                if (window.SERVER_CONFIG && window.SERVER_CONFIG.accessToken) {
                    console.log('✅ 从SERVER_CONFIG获取到AccessToken:', window.SERVER_CONFIG.accessToken.substring(0, 20) + '...');
                    return window.SERVER_CONFIG.accessToken;
                }

                // 第二优先级：从 sessionStorage 获取（登录后存储）
                let accessToken = sessionStorage.getItem('accessToken');
                if (accessToken) {
                    console.log('✅ 从sessionStorage获取到AccessToken:', accessToken.substring(0, 20) + '...');
                    return accessToken;
                }

                // 第三优先级：从 URL 参数获取
                const urlParams = new URLSearchParams(window.location.search);
                accessToken = urlParams.get('accessToken') || urlParams.get('token');
                console.log('🔍 从URL参数获取AccessToken:', accessToken ? accessToken.substring(0, 20) + '...' : '未找到');

                // 如果从URL获取到token，存储到sessionStorage
                if (accessToken) {
                    sessionStorage.setItem('accessToken', accessToken);
                    console.log('💾 AccessToken已存储到sessionStorage');
                } else {
                    console.log('❌ 未找到任何AccessToken');
                }

                return accessToken;
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }

            toggleConnection() {
                if (this.isConnected) {
                    this.disconnect();
                } else {
                    this.connect();
                }
            }

            connect() {
                try {
                    const serverUrl = this.elements.serverUrl.value.trim();
                    const clientId = this.elements.clientId.value.trim();

                    if (!serverUrl || !clientId) {
                        this.showMessage('请填写服务器地址和客户端ID', 'error');
                        return;
                    }

                    // 修复WebSocket URL构建逻辑，避免重复拼接clientId
                    let wsUrl;
                    if (serverUrl.includes('{clientId}')) {
                        // 如果URL模板包含占位符，替换它
                        wsUrl = serverUrl.replace('{clientId}', clientId);
                    } else {
                        // 如果URL不包含占位符，直接拼接clientId
                        wsUrl = serverUrl.endsWith('/') ? serverUrl + clientId : serverUrl + '/' + clientId;
                    }
                    console.log('🔗 连接WebSocket URL:', wsUrl);

                    this.websocket = new WebSocket(wsUrl);

                    this.websocket.onopen = () => {
                        this.isConnected = true;
                        this.updateConnectionStatus();
                        this.showMessage('WebSocket连接成功', 'success');
                        this.elements.connectBtn.textContent = '断开连接';
                        this.elements.connectBtn.className = 'btn btn-danger';
                    };

                    this.websocket.onmessage = (event) => {
                        console.log('📨 收到WebSocket消息:', event.data);
                        if (!this.isPaused) {
                            this.handleMessage(event.data);
                        } else {
                            console.log('⏸️ 日志输出已暂停，跳过消息处理');
                        }
                    };

                    this.websocket.onclose = () => {
                        this.isConnected = false;
                        this.updateConnectionStatus();
                        this.showMessage('WebSocket连接已断开', 'info');
                        this.elements.connectBtn.textContent = '连接';
                        this.elements.connectBtn.className = 'btn btn-primary';
                    };

                    this.websocket.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.showMessage('WebSocket连接错误', 'error');
                    };

                } catch (error) {
                    console.error('连接失败:', error);
                    this.showMessage('连接失败: ' + error.message, 'error');
                }
            }

            disconnect() {
                if (this.websocket) {
                    this.websocket.close();
                    this.websocket = null;
                }
                this.stopRealTimeUpdate(); // 断开连接时停止实时更新
            }

            handleMessage(data) {
                try {
                    let logMessage;

                    // 尝试解析JSON格式的消息
                    try {
                        logMessage = JSON.parse(data);
                        console.log('✅ 成功解析JSON消息:', logMessage);
                    } catch (e) {
                        // 如果不是JSON，创建简单的日志对象
                        logMessage = {
                            timestamp: new Date().toISOString(),
                            level: 'INFO',
                            body: data,
                            sequence: Date.now()
                        };
                        console.log('📝 创建简单日志对象:', logMessage);
                    }

                    this.addLogEntry(logMessage);
                    console.log('📊 当前日志总数:', this.logs.length);
                } catch (error) {
                    console.error('处理消息失败:', error);
                }
            }

            addLogEntry(logMessage) {
                this.logs.push(logMessage);

                // 限制日志数量，避免内存溢出
                if (this.logs.length > 10000) {
                    this.logs = this.logs.slice(-5000);
                }

                this.filterLogs();
                this.updateStats();
                this.updateLastUpdate();
            }

            filterLogs() {
                const searchTerm = this.elements.searchBox.value.toLowerCase();
                const filters = {
                    error: this.elements.filterError.checked,
                    warn: this.elements.filterWarn.checked,
                    info: this.elements.filterInfo.checked,
                    debug: this.elements.filterDebug.checked,
                    system: this.elements.filterSystem.checked
                };

                this.filteredLogs = this.logs.filter(log => {
                    // 级别过滤
                    const level = (log.level || 'info').toLowerCase();
                    if (!filters[level] && !filters.system) {
                        return false;
                    }

                    // 搜索过滤
                    if (searchTerm) {
                        const content = (log.body || log.formattedMessage || '').toLowerCase();
                        return content.includes(searchTerm);
                    }

                    return true;
                });

                this.renderLogs();
                this.updateStats();
            }

            renderLogs() {
                if (this.filteredLogs.length === 0) {
                    this.elements.logOutput.innerHTML = '<div class="loading">暂无日志数据</div>';
                    return;
                }

                const html = this.filteredLogs.map(log => {
                    const level = (log.level || 'info').toLowerCase();
                    const timestamp = log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : '';
                    const content = log.body || log.formattedMessage || '';

                    return `<div class="log-entry ${level}">[${timestamp}] ${content}</div>`;
                }).join('');

                this.elements.logOutput.innerHTML = html;

                // 自动滚动到底部
                this.elements.logOutput.scrollTop = this.elements.logOutput.scrollHeight;
            }

            clearLogs() {
                this.logs = [];
                this.filteredLogs = [];
                this.elements.logOutput.innerHTML = '<div class="loading">日志已清空</div>';
                this.updateStats();
                this.showMessage('日志已清空', 'info');
                this.stopRealTimeUpdate();
            }

            // 启动实时更新
            startRealTimeUpdate() {
                if (!this.currentFile || !this.isRealTimeMode) return;

                this.stopRealTimeUpdate(); // 确保没有重复的定时器

                this.realTimeUpdateTimer = setInterval(async () => {
                    await this.checkForLogUpdates();
                }, 3000); // 每3秒检查一次更新

                console.log('✅ 启动实时日志更新');
            }

            // 停止实时更新
            stopRealTimeUpdate() {
                if (this.realTimeUpdateTimer) {
                    clearInterval(this.realTimeUpdateTimer);
                    this.realTimeUpdateTimer = null;
                    console.log('⏹️ 停止实时日志更新');
                }
                this.isRealTimeMode = false;
            }

            // 检查日志文件更新
            async checkForLogUpdates() {
                if (!this.currentFile || this.isPaused) return;

                try {
                    const accessToken = this.getAccessToken();
                    if (!accessToken) return;

                    // 获取文件最新的几行内容
                    const response = await fetch(this.buildApiUrl(`/tail/${this.currentFile}?lines=10`), {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken,
                            'Content-Type': 'application/json'
                        }
                    });
                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === 200) {
                            const currentFileSize = result.data.fileSize || 0;

                            // 如果文件大小发生变化，说明有新内容
                            if (currentFileSize > this.lastFileSize) {
                                const newLines = result.data.lines;

                                // 添加新的日志条目
                                newLines.forEach((line, index) => {
                                    // 避免重复添加已存在的日志
                                    if (!this.logs.some(log => log.body === line)) {
                                        this.addLogEntry({
                                            timestamp: new Date().toISOString(),
                                            level: this.detectLogLevel(line),
                                            body: line,
                                            sequence: Date.now() + index
                                        });
                                    }
                                });

                                this.lastFileSize = currentFileSize;

                                // 限制日志条目数量，避免内存溢出
                                if (this.logs.length > 1000) {
                                    this.logs = this.logs.slice(-800); // 保留最新的800条
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('检查日志更新失败:', error);
                }
            }

            // 检测日志级别
            detectLogLevel(logLine) {
                const line = logLine.toLowerCase();
                if (line.includes('error') || line.includes('错误')) return 'ERROR';
                if (line.includes('warn') || line.includes('警告')) return 'WARN';
                if (line.includes('debug') || line.includes('调试')) return 'DEBUG';
                if (line.includes('info') || line.includes('信息')) return 'INFO';
                return 'INFO';
            }

            exportLogs() {
                if (this.filteredLogs.length === 0) {
                    this.showMessage('没有日志可导出', 'error');
                    return;
                }

                const content = this.filteredLogs.map(log => {
                    const timestamp = log.timestamp ? new Date(log.timestamp).toISOString() : '';
                    const level = log.level || 'INFO';
                    const body = log.body || log.formattedMessage || '';
                    return `[${timestamp}] [${level}] ${body}`;
                }).join('\n');

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `edc-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                link.click();
                URL.revokeObjectURL(url);

                this.showMessage(`已导出 ${this.filteredLogs.length} 条日志`, 'success');
            }

            togglePause() {
                this.isPaused = !this.isPaused;
                this.elements.pauseBtn.textContent = this.isPaused ? '继续' : '暂停';
                this.elements.pauseBtn.className = this.isPaused ? 'btn btn-success btn-small' : 'btn btn-secondary btn-small';
                this.showMessage(this.isPaused ? '日志输出已暂停' : '日志输出已恢复', 'info');
            }

            updateConnectionStatus() {
                if (this.isConnected) {
                    this.elements.connectionStatus.className = 'status-dot connected';
                    this.elements.connectionText.textContent = '已连接';
                } else {
                    this.elements.connectionStatus.className = 'status-dot';
                    this.elements.connectionText.textContent = '未连接';
                }
            }

            updateStats() {
                this.elements.logCount.textContent = `${this.logs.length} 条日志`;
                this.elements.filteredCount.textContent = `显示: ${this.filteredLogs.length}`;
                this.elements.totalCount.textContent = `总计: ${this.logs.length}`;
            }

            updateLastUpdate() {
                this.elements.lastUpdate.textContent = new Date().toLocaleTimeString();
            }

            showMessage(text, type = 'info') {
                const message = document.createElement('div');
                message.className = `message ${type}`;
                message.textContent = text;

                document.getElementById('messageContainer').appendChild(message);

                // 显示消息
                setTimeout(() => message.classList.add('show'), 100);

                // 3秒后隐藏消息
                setTimeout(() => {
                    message.classList.remove('show');
                    setTimeout(() => {
                        if (message.parentNode) {
                            message.parentNode.removeChild(message);
                        }
                    }, 300);
                }, 3000);
            }

            // ============ 认证区域 ============
            // 第一步：秘钥验证
            // 第二步：获取访问令牌
            // 第三步：验证访问令牌
            // 验证成功！正在跳转管理页面...
            // ================================

            // Token验证相关方法
            async validateToken() {
                const accessToken = this.getAccessToken();
                if (!accessToken) {
                    console.error('❌ Token验证失败：缺少访问令牌');
                    this.updateTokenStatus(false, '缺少访问令牌');
                    return false;
                }

                try {
                    const apiUrl = this.buildApiUrl('/viewer');
                    console.log('🔐 开始Token验证，请求URL:', apiUrl);
                    console.log('🔑 使用访问令牌:', accessToken.substring(0, 20) + '...');

                    // 第三步：验证访问令牌
                    const response = await fetch(apiUrl, {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('📡 Token验证响应状态:', response.status, response.statusText);
                    const result = await response.json();
                    console.log('📋 Token验证响应数据:', result);

                    if (result.code === 200 && result.data) {
                        // 验证成功！正在跳转管理页面...
                        console.log('✅ Token验证成功');
                        this.updateTokenStatus(true, 'Token有效');
                        // 修复：传递剩余时间参数
                        this.updateTokenExpireTime(result.data.tokenExpireTime, result.data.remainingTimeSeconds);
                        this.elements.logoutBtn.style.display = 'inline-block';

                        // 启动token自动刷新机制
                        this.startTokenRefreshTimer();
                        return true;
                    } else {
                        console.error('❌ Token验证失败:', result);
                        this.updateTokenStatus(false, result.message || 'Token验证失败');

                        // 修复：不立即尝试刷新token，而是显示友好提示
                        console.log('⚠️ Token验证失败，但不立即跳转，等待用户操作');
                        this.showMessage('访问令牌验证失败，请重新登录', 'warning');
                        return false;
                    }
                } catch (error) {
                    console.error('❌ Token验证异常:', error);
                    this.updateTokenStatus(false, 'Token验证异常');

                    // 网络异常时不立即跳转，给用户重试机会
                    this.showMessage('网络连接异常，请检查网络后刷新页面重试', 'error');
                    return false;
                }
            }

            updateTokenStatus(isValid, message) {
                if (isValid) {
                    this.elements.tokenStatus.className = 'status-dot valid';
                    this.elements.tokenStatusText.textContent = message || 'Token有效';
                    // 修复：Token有效时显示退出按钮，隐藏重新登录按钮
                    if (this.elements.logoutBtn) this.elements.logoutBtn.style.display = 'inline-block';
                    if (this.elements.reloginBtn) this.elements.reloginBtn.style.display = 'none';
                } else {
                    this.elements.tokenStatus.className = 'status-dot invalid';
                    this.elements.tokenStatusText.textContent = message || 'Token无效';
                    // 修复：Token无效时显示重新登录按钮，隐藏退出按钮
                    if (this.elements.logoutBtn) this.elements.logoutBtn.style.display = 'none';
                    if (this.elements.reloginBtn) this.elements.reloginBtn.style.display = 'inline-block';
                }
            }

            updateTokenExpireTime(expireTime, remainingSeconds) {
                if (expireTime) {
                    // 修复：存储过期时间和剩余秒数，启动实时倒计时
                    this.tokenExpireTime = new Date(expireTime).getTime();
                    this.tokenRemainingSeconds = remainingSeconds || null;

                    // 启动token状态实时更新
                    this.startTokenStatusTimer();

                    this.elements.tokenExpireTime.textContent = expireTime;
                    console.log('🕒 Token过期时间已设置:', expireTime, '剩余秒数:', remainingSeconds);
                } else {
                    this.elements.tokenExpireTime.textContent = '--';
                    this.stopTokenStatusTimer();
                }
            }

            // 启动token状态实时更新定时器
            startTokenStatusTimer() {
                // 清除之前的定时器
                if (this.tokenStatusTimer) {
                    clearInterval(this.tokenStatusTimer);
                }

                // 每秒更新一次token状态
                this.tokenStatusTimer = setInterval(() => {
                    this.updateTokenStatusDisplay();
                }, 1000);

                console.log('⏰ Token状态实时更新定时器已启动');
            }

            // 停止token状态更新定时器
            stopTokenStatusTimer() {
                if (this.tokenStatusTimer) {
                    clearInterval(this.tokenStatusTimer);
                    this.tokenStatusTimer = null;
                }
            }

            // 更新token状态显示
            updateTokenStatusDisplay() {
                if (!this.tokenExpireTime) return;

                const now = Date.now();
                const remainingTime = this.tokenExpireTime - now;

                if (remainingTime <= 0) {
                    // Token已过期
                    this.elements.tokenExpireTime.textContent = '已过期';
                    this.elements.tokenStatusText.textContent = 'Token已过期';
                    this.elements.tokenStatus.className = 'status-dot danger';

                    // 停止定时器
                    this.stopTokenStatusTimer();

                    // 修复：Token过期后显示重新登录按钮，而不是认证区域
                    console.log('🔴 Token已过期，显示重新登录选项');
                    this.showTokenExpiredOptions();
                    return;
                }

                // 计算剩余时间
                const hours = Math.floor(remainingTime / (1000 * 60 * 60));
                const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

                // 格式化显示
                const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                // 更新显示
                const expireTimeStr = new Date(this.tokenExpireTime).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                this.elements.tokenExpireTime.textContent = `${expireTimeStr} (剩余: ${timeStr})`;

                // 根据剩余时间设置状态
                if (remainingTime < 5 * 60 * 1000) { // 少于5分钟
                    this.elements.tokenStatusText.textContent = 'Token即将过期';
                    this.elements.tokenStatus.className = 'status-dot danger';
                } else if (remainingTime < 15 * 60 * 1000) { // 少于15分钟
                    this.elements.tokenStatusText.textContent = 'Token即将过期';
                    this.elements.tokenStatus.className = 'status-dot warning';
                } else {
                    this.elements.tokenStatusText.textContent = 'Token有效';
                    this.elements.tokenStatus.className = 'status-dot success';
                }
            }

            // 显示token过期选项
            showTokenExpiredOptions() {
                // 显示重新登录按钮
                if (this.elements.logoutBtn) this.elements.logoutBtn.style.display = 'none';
                if (this.elements.reloginBtn) this.elements.reloginBtn.style.display = 'inline-block';

                // 显示过期提示
                this.showMessage('访问令牌已过期，请点击"重新登录"按钮', 'error');
            }

            // Token自动刷新机制 - 修复版本，支持短期token测试
            startTokenRefreshTimer() {
                // 清除之前的定时器
                if (this.tokenRefreshTimer) {
                    clearInterval(this.tokenRefreshTimer);
                }

                // 修复：根据token剩余时间动态调整检查间隔
                let checkInterval = 30 * 1000; // 默认30秒检查一次

                if (this.tokenRemainingSeconds) {
                    if (this.tokenRemainingSeconds <= 120) { // 剩余时间少于2分钟
                        checkInterval = 5 * 1000; // 5秒检查一次
                    } else if (this.tokenRemainingSeconds <= 600) { // 剩余时间少于10分钟
                        checkInterval = 15 * 1000; // 15秒检查一次
                    }
                }

                this.tokenRefreshTimer = setInterval(async () => {
                    console.log('🔄 定时检查Token状态...');
                    const isValid = await this.validateTokenSilently();
                    if (!isValid) {
                        console.log('⚠️ Token已失效，尝试刷新...');
                        const refreshSuccess = await this.attemptTokenRefresh();
                        if (!refreshSuccess) {
                            console.log('❌ Token刷新失败，显示重新登录选项');
                            clearInterval(this.tokenRefreshTimer);
                            // 修复：Token过期后显示重新登录选项
                            this.showTokenExpiredOptions();
                        }
                    }
                }, checkInterval);

                console.log('⏰ Token自动刷新定时器已启动，检查间隔:', checkInterval / 1000, '秒');
            }

            // 静默验证token（不显示错误信息）
            async validateTokenSilently() {
                const accessToken = this.getAccessToken();
                if (!accessToken) return false;

                try {
                    const response = await fetch(this.buildApiUrl('/viewer'), {
                        method: 'GET',
                        headers: {
                            'X-Access-Token': accessToken,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    return result.code === 200 && result.data;
                } catch (error) {
                    console.error('静默Token验证失败:', error);
                    return false;
                }
            }

            // 尝试刷新token
            async attemptTokenRefresh() {
                const accessToken = this.getAccessToken();
                if (!accessToken) return false;

                try {
                    console.log('🔄 尝试刷新AccessToken...');
                    const response = await fetch(this.buildApiUrl('/auth/refresh-token'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Access-Token': accessToken
                        }
                    });

                    const result = await response.json();
                    if (result.code === 200 && result.data && result.data.accessToken) {
                        const newToken = result.data.accessToken;
                        sessionStorage.setItem('accessToken', newToken);
                        console.log('✅ Token刷新成功');
                        this.updateTokenStatus(true, 'Token已刷新');
                        return true;
                    } else {
                        console.log('❌ Token刷新失败:', result.message);
                        return false;
                    }
                } catch (error) {
                    console.error('Token刷新异常:', error);
                    return false;
                }
            }

            redirectToLogin() {
                // 清除定时器
                if (this.tokenRefreshTimer) {
                    clearInterval(this.tokenRefreshTimer);
                    this.tokenRefreshTimer = null;
                }

                // 清除可能存在的无效token
                sessionStorage.removeItem('accessToken');
                localStorage.removeItem('accessToken');

                const currentUrl = window.location.href;

                // 彻底修复：使用与buildApiUrl相同的逻辑构建登录URL
                let loginUrl;

                if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
                    // 使用服务器配置的requestUrl构建登录URL
                    const baseUrl = window.SERVER_CONFIG.requestUrl;

                    // 确保baseUrl以/api结尾
                    let apiBaseUrl = baseUrl;
                    if (!apiBaseUrl.endsWith('/api') && !apiBaseUrl.endsWith('/api/')) {
                        if (apiBaseUrl.endsWith('/')) {
                            apiBaseUrl += 'api';
                        } else {
                            apiBaseUrl += '/api';
                        }
                    }

                    loginUrl = apiBaseUrl + '/log-management/log-login.html';
                    console.log('🔗 使用配置的requestUrl构建登录URL:', loginUrl);
                } else {
                    // 备用方案：使用当前URL构建
                    loginUrl = currentUrl.substring(0, currentUrl.indexOf('/log-management/')) + '/log-management/log-login.html';
                    console.log('🔗 使用备用方案构建登录URL:', loginUrl);
                }

                console.log('🔄 Token验证失败，清除本地token并跳转到登录页面');
                console.log('📍 当前URL:', currentUrl);
                console.log('🎯 登录URL:', loginUrl);

                // 显示跳转提示
                this.showMessage('访问令牌无效或已过期，正在跳转到登录页面...', 'info');

                // 延迟跳转，让用户看到提示信息
                setTimeout(() => {
                    window.location.href = loginUrl;
                }, 1500);
            }
        }

        // 全局变量
        let logViewer = null;

        // 全局退出函数
        function logout() {
            if (confirm('确定要退出日志管理系统吗？')) {
                // 清除可能的本地存储
                localStorage.removeItem('accessToken');
                sessionStorage.removeItem('accessToken');

                // 跳转到登录页面
                window.location.href = '/api/log-management/log-login.html';
            }
        }

        // 重新登录函数
        function relogin() {
            // 清除可能的本地存储
            localStorage.removeItem('accessToken');
            sessionStorage.removeItem('accessToken');

            // 直接跳转到登录页面
            window.location.href = '/api/log-management/log-login.html';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logViewer = new LogViewer();
            logViewer.init();
        });
    </script>
</body>
</html>
