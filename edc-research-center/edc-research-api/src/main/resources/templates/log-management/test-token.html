<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token验证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #666;
            margin-bottom: 15px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 EDC 日志系统 Token验证测试</h1>
        
        <div class="test-section">
            <h3>1. 测试无Token访问</h3>
            <p>测试直接访问log-viewer.html页面（无token参数）</p>
            <button class="btn" onclick="testNoToken()">测试无Token访问</button>
            <div id="noTokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试无效Token访问</h3>
            <p>测试使用无效token访问log-viewer.html页面</p>
            <button class="btn" onclick="testInvalidToken()">测试无效Token访问</button>
            <div id="invalidTokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 获取有效Token</h3>
            <p>通过正确的流程获取有效的AccessToken</p>
            <div class="input-group">
                <label for="appId">App ID:</label>
                <input type="text" id="appId" value="test-app-001" placeholder="输入App ID">
            </div>
            <div class="input-group">
                <label for="appSecret">App Secret:</label>
                <input type="text" id="appSecret" value="test-secret-001" placeholder="输入App Secret">
            </div>
            <button class="btn" onclick="generateToken()">生成Token</button>
            <div id="tokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试有效Token访问</h3>
            <p>使用获取到的有效token访问log-viewer.html页面</p>
            <div class="input-group">
                <label for="accessToken">Access Token:</label>
                <input type="text" id="accessToken" placeholder="从上面获取的AccessToken">
            </div>
            <button class="btn" onclick="testValidToken()">测试有效Token访问</button>
            <button class="btn danger" onclick="openLogViewer()">直接打开日志查看器</button>
            <div id="validTokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 快速链接</h3>
            <p>快速访问相关页面</p>
            <button class="btn" onclick="openLoginPage()">打开登录页面</button>
            <button class="btn" onclick="openLogViewerDirect()">直接访问日志查看器</button>
        </div>
    </div>

    <script>
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        function testNoToken() {
            showResult('noTokenResult', '正在测试无Token访问...', true);
            
            // 尝试访问log-viewer.html页面（无token）
            const testUrl = '/api/log-management/log-viewer.html';
            window.open(testUrl, '_blank');
            
            showResult('noTokenResult', '已打开新窗口测试无Token访问，应该会跳转到登录页面', true);
        }

        function testInvalidToken() {
            showResult('invalidTokenResult', '正在测试无效Token访问...', true);
            
            // 使用无效token访问
            const testUrl = '/api/log-management/log-viewer.html?accessToken=invalid-token-123';
            window.open(testUrl, '_blank');
            
            showResult('invalidTokenResult', '已打开新窗口测试无效Token访问，应该会跳转到登录页面', true);
        }

        async function generateToken() {
            const appId = document.getElementById('appId').value;
            const appSecret = document.getElementById('appSecret').value;
            
            if (!appId || !appSecret) {
                showResult('tokenResult', '请输入App ID和App Secret', false);
                return;
            }
            
            showResult('tokenResult', '正在生成Token...', true);
            
            try {
                // 第一步：生成Code
                const codeResponse = await fetch('/api/secure/token/generate-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        appId: appId,
                        appSecret: appSecret,
                        environment: 'local'
                    })
                });
                
                const codeResult = await codeResponse.json();
                if (codeResult.code !== 200) {
                    showResult('tokenResult', `生成Code失败: ${codeResult.message}`, false);
                    return;
                }
                
                // 第二步：获取AccessToken
                const tokenResponse = await fetch('/api/secure/token/get-access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: codeResult.data.code,
                        refreshCode: codeResult.data.refreshCode
                    })
                });
                
                const tokenResult = await tokenResponse.json();
                if (tokenResult.code !== 200) {
                    showResult('tokenResult', `获取AccessToken失败: ${tokenResult.message}`, false);
                    return;
                }
                
                // 成功获取Token
                const accessToken = tokenResult.data.accessToken;
                document.getElementById('accessToken').value = accessToken;
                
                showResult('tokenResult', 
                    `Token生成成功！\n` +
                    `AccessToken: ${accessToken}\n` +
                    `过期时间: ${tokenResult.data.expireTime}\n` +
                    `有效期: ${tokenResult.data.expiresIn}秒`, true);
                
            } catch (error) {
                showResult('tokenResult', `生成Token异常: ${error.message}`, false);
            }
        }

        function testValidToken() {
            const accessToken = document.getElementById('accessToken').value;
            
            if (!accessToken) {
                showResult('validTokenResult', '请先获取有效的AccessToken', false);
                return;
            }
            
            showResult('validTokenResult', '正在测试有效Token访问...', true);
            
            // 使用有效token访问
            const testUrl = `/api/log-management/log-viewer.html?accessToken=${encodeURIComponent(accessToken)}`;
            window.open(testUrl, '_blank');
            
            showResult('validTokenResult', '已打开新窗口测试有效Token访问，应该能正常进入日志查看器', true);
        }

        function openLogViewer() {
            const accessToken = document.getElementById('accessToken').value;
            
            if (!accessToken) {
                alert('请先获取有效的AccessToken');
                return;
            }
            
            const url = `/api/log-management/log-viewer.html?accessToken=${encodeURIComponent(accessToken)}`;
            window.location.href = url;
        }

        function openLoginPage() {
            window.open('/api/log-management/log-login.html', '_blank');
        }

        function openLogViewerDirect() {
            window.open('/api/log-management/log-viewer.html', '_blank');
        }
    </script>
</body>
</html>
