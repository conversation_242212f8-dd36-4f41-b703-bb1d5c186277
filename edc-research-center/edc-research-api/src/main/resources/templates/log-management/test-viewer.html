<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 EDC日志查看器测试页面</h1>
        <p>此页面用于测试日志查看器的各种功能和修复效果</p>

        <div class="test-section">
            <h3>1. AccessToken验证测试</h3>
            <p>测试不同的AccessToken场景：</p>
            <a href="log-viewer.html" class="btn btn-danger">无Token访问（应跳转登录）</a>
            <a href="log-viewer.html?accessToken=invalid_token" class="btn btn-warning">无效Token访问</a>
            <a href="log-viewer.html?accessToken=access_829972_ea0c71c8cdc24418_2202e8c57067" class="btn btn-success">有效Token访问</a>
        </div>

        <div class="test-section">
            <h3>2. 本地存储测试</h3>
            <p>测试sessionStorage中的token：</p>
            <button class="btn btn-primary" onclick="setTestToken()">设置测试Token到sessionStorage</button>
            <button class="btn btn-warning" onclick="clearTokens()">清除所有Token</button>
            <button class="btn btn-success" onclick="checkTokens()">检查当前Token状态</button>
            <div id="tokenStatus" class="status info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 环境配置验证</h3>
            <p>当前环境信息：</p>
            <div class="code">
                <strong>当前URL:</strong> <span id="currentUrl"></span><br>
                <strong>协议:</strong> <span id="protocol"></span><br>
                <strong>主机:</strong> <span id="host"></span><br>
                <strong>预期WebSocket URL:</strong> <span id="expectedWsUrl"></span><br>
                <strong>预期API基础URL:</strong> <span id="expectedApiUrl"></span>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 功能测试步骤</h3>
            <ol>
                <li><strong>无Token测试:</strong> 点击"无Token访问"按钮，应该跳转到登录页面</li>
                <li><strong>有效Token测试:</strong> 点击"有效Token访问"按钮，应该正常进入日志查看器</li>
                <li><strong>WebSocket连接:</strong> 在日志查看器中点击"连接"按钮，检查连接状态</li>
                <li><strong>文件列表:</strong> 点击"刷新文件列表"按钮，检查是否能正常加载文件</li>
                <li><strong>控制台日志:</strong> 打开浏览器开发者工具，查看详细的调试信息</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>5. 预期结果</h3>
            <div class="status success">
                <strong>修复后应该实现：</strong><br>
                ✅ 无AccessToken时自动跳转到登录页面<br>
                ✅ WebSocket连接正常建立<br>
                ✅ 文件列表能够正常刷新<br>
                ✅ 控制台输出详细的调试信息<br>
                ✅ 语法错误已修复，页面正常加载
            </div>
        </div>
    </div>

    <script>
        // 页面加载时显示环境信息
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('host').textContent = window.location.host;
            
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const expectedWsUrl = `${wsProtocol}//${window.location.host}/api/websocket/logs/{clientId}`;
            document.getElementById('expectedWsUrl').textContent = expectedWsUrl;
            
            const expectedApiUrl = `${window.location.origin}/api/log-management`;
            document.getElementById('expectedApiUrl').textContent = expectedApiUrl;
        });

        function setTestToken() {
            const testToken = 'access_829972_ea0c71c8cdc24418_2202e8c57067';
            sessionStorage.setItem('accessToken', testToken);
            showStatus('测试Token已设置到sessionStorage', 'success');
        }

        function clearTokens() {
            sessionStorage.removeItem('accessToken');
            localStorage.removeItem('accessToken');
            showStatus('所有Token已清除', 'info');
        }

        function checkTokens() {
            const sessionToken = sessionStorage.getItem('accessToken');
            const localToken = localStorage.getItem('accessToken');
            const urlParams = new URLSearchParams(window.location.search);
            const urlToken = urlParams.get('accessToken') || urlParams.get('token');
            
            let message = '';
            message += `SessionStorage Token: ${sessionToken ? sessionToken.substring(0, 20) + '...' : '无'}\n`;
            message += `LocalStorage Token: ${localToken ? localToken.substring(0, 20) + '...' : '无'}\n`;
            message += `URL参数Token: ${urlToken ? urlToken.substring(0, 20) + '...' : '无'}`;
            
            showStatus(message, 'info');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('tokenStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = message.replace(/\n/g, '<br>');
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
