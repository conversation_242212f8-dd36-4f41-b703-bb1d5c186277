<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">EDC科研协作平台 - 统一管理门户</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .module-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }

        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            background: var(--card-color);
            color: white;
            font-size: 1.5rem;
        }

        .module-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.8rem;
            color: #2d3748;
        }

        .module-description {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .module-action {
            display: flex;
            align-items: center;
            color: var(--card-color);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .module-action i {
            margin-left: 0.5rem;
            transition: transform 0.2s ease;
        }

        .module-card:hover .module-action i {
            transform: translateX(4px);
        }

        .footer {
            text-align: center;
            margin-top: 4rem;
            color: white;
            opacity: 0.8;
        }

        .footer p {
            font-size: 0.9rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .module-card {
                padding: 1.5rem;
            }
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片颜色变量 */
        .module-card[data-color="#4CAF50"] {
            --card-color: #4CAF50;
        }
        .module-card[data-color="#2196F3"] {
            --card-color: #2196F3;
        }
        .module-card[data-color="#FF5722"] {
            --card-color: #FF5722;
        }
        .module-card[data-color="#9C27B0"] {
            --card-color: #9C27B0;
        }
        .module-card[data-color="#FF9800"] {
            --card-color: #FF9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1>EDC科研协作平台</h1>
            <p>统一管理门户 - 一站式系统管理中心</p>
        </div>

        <!-- 管理模块网格 -->
        <div class="modules-grid">
            <div th:each="module, iterStat : ${managementModules}" 
                 class="module-card fade-in" 
                 th:data-color="${module.color}"
                 th:style="'animation-delay: ' + ${iterStat.index * 0.1} + 's'"
                 th:onclick="'navigateToModule(\'' + ${baseUrl} + ${module.loginPath} + '\')'">
                
                <div class="module-icon">
                    <i th:class="${module.icon}"></i>
                </div>
                
                <h3 class="module-title" th:text="${module.title}">模块标题</h3>
                
                <p class="module-description" th:text="${module.description}">模块描述</p>
                
                <div class="module-action">
                    <span>进入管理</span>
                    <i class="fas fa-arrow-right"></i>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="footer fade-in" style="animation-delay: 0.8s">
            <p>&copy; 2025 EDC科研协作平台. 统一管理门户系统</p>
        </div>
    </div>

    <script>
        // 页面跳转函数
        function navigateToModule(url) {
            console.log('跳转到模块:', url);
            window.open(url, '_blank');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('统一管理门户页面加载完成');
            console.log('基础URL:', '[[${baseUrl}]]');
            console.log('管理模块数量:', '[[${managementModules.size()}]]');
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // 按数字键1-5快速访问对应模块
            if (e.key >= '1' && e.key <= '5') {
                const moduleIndex = parseInt(e.key) - 1;
                const cards = document.querySelectorAll('.module-card');
                if (cards[moduleIndex]) {
                    cards[moduleIndex].click();
                }
            }
        });
    </script>
</body>
</html>
