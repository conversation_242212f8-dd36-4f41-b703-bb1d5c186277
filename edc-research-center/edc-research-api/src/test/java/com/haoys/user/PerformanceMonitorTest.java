package com.haoys.user;

import com.haoys.user.common.aspect.PerformanceMonitorAspect;
import com.haoys.user.common.interceptor.SqlPerformanceInterceptor;
import com.haoys.user.service.SystemLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能监控和系统许可证功能测试
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
public class PerformanceMonitorTest {

    @Autowired
    private PerformanceMonitorAspect performanceMonitorAspect;
    
    @Autowired
    private SqlPerformanceInterceptor sqlPerformanceInterceptor;
    
    @Autowired
    private SystemLicenseService systemLicenseService;

    @Test
    public void testPerformanceMonitorAspect() {
        log.info("========================================");
        log.info("测试性能监控切面功能");
        log.info("========================================");
        
        try {
            // 获取性能统计信息
            ConcurrentHashMap<String, PerformanceMonitorAspect.MethodStats> stats = 
                performanceMonitorAspect.getPerformanceStats();
            
            log.info("当前监控的方法数量: {}", stats.size());
            
            // 显示统计信息
            stats.forEach((methodName, methodStats) -> {
                log.info("方法: {} - 调用次数: {}, 平均耗时: {}ms, 成功率: {:.2f}%", 
                        methodName, 
                        methodStats.getTotalCalls(),
                        methodStats.getAverageDuration(),
                        methodStats.getSuccessRate());
            });
            
            log.info("✅ 性能监控切面测试通过");
            
        } catch (Exception e) {
            log.error("❌ 性能监控切面测试失败", e);
            throw e;
        }
    }

    @Test
    public void testSqlPerformanceInterceptor() {
        log.info("========================================");
        log.info("测试SQL性能监控拦截器功能");
        log.info("========================================");
        
        try {
            // 获取SQL性能统计信息
            ConcurrentHashMap<String, SqlPerformanceInterceptor.SqlStats> sqlStats = 
                sqlPerformanceInterceptor.getSqlPerformanceStats();
            
            log.info("当前监控的SQL方法数量: {}", sqlStats.size());
            
            // 显示SQL统计信息
            sqlStats.forEach((sqlId, stats) -> {
                log.info("SQL: {} - 执行次数: {}, 平均耗时: {}ms, 慢查询次数: {}, 成功率: {:.2f}%", 
                        sqlId, 
                        stats.getTotalExecutions(),
                        stats.getAverageDuration(),
                        stats.getSlowQueryCount(),
                        stats.getSuccessRate());
            });
            
            log.info("✅ SQL性能监控拦截器测试通过");
            
        } catch (Exception e) {
            log.error("❌ SQL性能监控拦截器测试失败", e);
            throw e;
        }
    }

    @Test
    public void testSystemLicenseService() {
        log.info("========================================");
        log.info("测试系统许可证服务功能");
        log.info("========================================");
        
        try {
            // 测试许可证验证
            boolean isValid = systemLicenseService.validateSystemLicense();
            log.info("系统许可证验证结果: {}", isValid ? "✅ 有效" : "❌ 无效");
            
            // 测试系统可用性
            boolean isAvailable = systemLicenseService.isSystemAvailable();
            log.info("系统可用性: {}", isAvailable ? "✅ 可用" : "❌ 不可用");
            
            if (!isAvailable) {
                String restrictionMessage = systemLicenseService.getSystemRestrictionMessage();
                log.warn("系统限制信息: {}", restrictionMessage);
            }
            
            // 获取当前许可证信息
            com.haoys.user.common.domain.SystemLicenseInfo currentLicense = systemLicenseService.getCurrentLicense();
            if (currentLicense != null) {
                log.info("当前许可证信息:");
                log.info("  - 许可证名称: {}", currentLicense.getLicenseName());
                log.info("  - 许可证类型: {}", currentLicense.getLicenseType());
                log.info("  - 最大用户数: {}", currentLicense.getMaxUsers());
                log.info("  - 最大项目数: {}", currentLicense.getMaxProjects());
                log.info("  - 剩余天数: {}", systemLicenseService.getRemainingDays());
            } else {
                log.warn("未找到当前许可证");
            }
            
            // 测试用户和项目限制
            boolean userLimitOk = systemLicenseService.checkUserLimit();
            boolean projectLimitOk = systemLicenseService.checkProjectLimit();
            
            log.info("用户数量限制检查: {}", userLimitOk ? "✅ 正常" : "❌ 超限");
            log.info("项目数量限制检查: {}", projectLimitOk ? "✅ 正常" : "❌ 超限");
            
            log.info("✅ 系统许可证服务测试通过");
            
        } catch (Exception e) {
            log.error("❌ 系统许可证服务测试失败", e);
            throw e;
        }
    }

    @Test
    public void testPerformanceStatsClear() {
        log.info("========================================");
        log.info("测试性能统计清空功能");
        log.info("========================================");
        
        try {
            // 获取清空前的统计信息
            int methodStatsBefore = performanceMonitorAspect.getPerformanceStats().size();
            int sqlStatsBefore = sqlPerformanceInterceptor.getSqlPerformanceStats().size();
            
            log.info("清空前 - 方法统计: {}, SQL统计: {}", methodStatsBefore, sqlStatsBefore);
            
            // 清空统计信息
            performanceMonitorAspect.clearStats();
            sqlPerformanceInterceptor.clearSqlStats();
            
            // 获取清空后的统计信息
            int methodStatsAfter = performanceMonitorAspect.getPerformanceStats().size();
            int sqlStatsAfter = sqlPerformanceInterceptor.getSqlPerformanceStats().size();
            
            log.info("清空后 - 方法统计: {}, SQL统计: {}", methodStatsAfter, sqlStatsAfter);
            
            // 验证清空结果
            if (methodStatsAfter == 0 && sqlStatsAfter == 0) {
                log.info("✅ 性能统计清空功能测试通过");
            } else {
                log.error("❌ 性能统计清空功能测试失败");
                throw new RuntimeException("统计信息未完全清空");
            }
            
        } catch (Exception e) {
            log.error("❌ 性能统计清空功能测试失败", e);
            throw e;
        }
    }

    @Test
    public void testSystemLicenseValidation() {
        log.info("========================================");
        log.info("测试系统许可证验证详细功能");
        log.info("========================================");
        
        try {
            // 初始化许可证
            systemLicenseService.initializeSystemLicense();
            log.info("✅ 许可证初始化完成");
            
            // 多次验证测试
            for (int i = 1; i <= 3; i++) {
                boolean isValid = systemLicenseService.validateSystemLicense();
                log.info("第{}次验证结果: {}", i, isValid ? "✅ 通过" : "❌ 失败");
                
                if (!isValid) {
                    log.warn("验证失败原因: {}", systemLicenseService.getSystemRestrictionMessage());
                }
                
                // 短暂等待
                Thread.sleep(100);
            }
            
            // 获取所有许可证
            java.util.List<com.haoys.user.common.domain.SystemLicenseInfo> allLicenses = systemLicenseService.getAllLicenses();
            log.info("系统中共有 {} 个许可证", allLicenses.size());
            
            allLicenses.forEach(license -> {
                log.info("许可证ID: {}, 名称: {}, 状态: {}, 类型: {}", 
                        license.getId(),
                        license.getLicenseName(),
                        license.getStatus(),
                        license.getLicenseType());
            });
            
            log.info("✅ 系统许可证验证详细功能测试通过");
            
        } catch (Exception e) {
            log.error("❌ 系统许可证验证详细功能测试失败", e);
        }
    }

    @Test
    public void testIntegratedFunctionality() {
        log.info("========================================");
        log.info("测试集成功能");
        log.info("========================================");
        
        try {
            // 1. 验证许可证
            boolean licenseValid = systemLicenseService.validateSystemLicense();
            log.info("1. 许可证验证: {}", licenseValid ? "✅ 通过" : "❌ 失败");
            
            // 2. 检查性能监控是否正常工作
            int initialMethodStats = performanceMonitorAspect.getPerformanceStats().size();
            int initialSqlStats = sqlPerformanceInterceptor.getSqlPerformanceStats().size();
            log.info("2. 性能监控状态 - 方法统计: {}, SQL统计: {}", initialMethodStats, initialSqlStats);
            
            // 3. 模拟一些操作来触发监控
            systemLicenseService.getCurrentLicense(); // 这会触发SQL监控
            systemLicenseService.checkUserLimit();    // 这会触发更多SQL监控
            
            // 4. 检查监控数据是否增加
            int finalMethodStats = performanceMonitorAspect.getPerformanceStats().size();
            int finalSqlStats = sqlPerformanceInterceptor.getSqlPerformanceStats().size();
            log.info("3. 操作后性能监控状态 - 方法统计: {}, SQL统计: {}", finalMethodStats, finalSqlStats);
            
            // 5. 验证监控数据确实在工作
            boolean monitoringWorking = finalSqlStats >= initialSqlStats;
            log.info("4. 性能监控工作状态: {}", monitoringWorking ? "✅ 正常" : "❌ 异常");
            
            log.info("✅ 集成功能测试通过");
            
        } catch (Exception e) {
            log.error("❌ 集成功能测试失败", e);
            throw e;
        }
    }
}
