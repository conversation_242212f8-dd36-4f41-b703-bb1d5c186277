package com.haoys.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.haoys.user.domain.vo.monitor.SystemPerformanceVo;
import com.haoys.user.service.SystemPerformanceMonitorService;
import com.haoys.user.web.controller.system.SystemPerformanceMonitorController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 系统性能监控控制器测试类
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
class SystemPerformanceMonitorControllerTest {

    @Mock
    private SystemPerformanceMonitorService performanceMonitorService;

    @InjectMocks
    private SystemPerformanceMonitorController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetSystemPerformanceOverview() throws Exception {
        // 准备测试数据
        SystemPerformanceVo mockPerformance = SystemPerformanceVo.builder()
                .monitorTime(LocalDateTime.now())
                .systemInfo(SystemPerformanceVo.SystemInfoVo.builder()
                        .osName("Linux")
                        .osVersion("5.4.0")
                        .javaVersion("1.8.0_291")
                        .processorCount(8)
                        .uptime(3600L)
                        .build())
                .cpuInfo(SystemPerformanceVo.CpuInfoVo.builder()
                        .cpuUsage(45.5)
                        .availableProcessors(8)
                        .systemLoadAverage(2.5)
                        .build())
                .memoryInfo(SystemPerformanceVo.MemoryInfoVo.builder()
                        .totalMemory(8192L)
                        .usedMemory(4096L)
                        .freeMemory(4096L)
                        .memoryUsage(50.0)
                        .build())
                .build();

        when(performanceMonitorService.getSystemPerformanceOverview()).thenReturn(mockPerformance);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/overview")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.systemInfo.osName").value("Linux"))
                .andExpect(jsonPath("$.data.cpuInfo.cpuUsage").value(45.5))
                .andExpect(jsonPath("$.data.memoryInfo.totalMemory").value(8192));

        verify(performanceMonitorService, times(1)).getSystemPerformanceOverview();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetCpuInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockCpuInfo = new HashMap<>();
        mockCpuInfo.put("systemCpuLoad", "45.50%");
        mockCpuInfo.put("processCpuLoad", "12.30%");
        mockCpuInfo.put("availableProcessors", 8);
        mockCpuInfo.put("systemLoadAverage", 2.5);

        when(performanceMonitorService.getCpuInfo()).thenReturn(mockCpuInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/cpu")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.systemCpuLoad").value("45.50%"))
                .andExpect(jsonPath("$.data.availableProcessors").value(8));

        verify(performanceMonitorService, times(1)).getCpuInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetMemoryInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockMemoryInfo = new HashMap<>();
        mockMemoryInfo.put("totalPhysicalMemory", "8.00 GB");
        mockMemoryInfo.put("usedPhysicalMemory", "4.00 GB");
        mockMemoryInfo.put("freePhysicalMemory", "4.00 GB");
        mockMemoryInfo.put("physicalMemoryUsage", "50.00%");

        when(performanceMonitorService.getMemoryInfo()).thenReturn(mockMemoryInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/memory")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalPhysicalMemory").value("8.00 GB"))
                .andExpect(jsonPath("$.data.physicalMemoryUsage").value("50.00%"));

        verify(performanceMonitorService, times(1)).getMemoryInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetDiskInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockDiskInfo = new HashMap<>();
        Map<String, Object> partition = new HashMap<>();
        partition.put("path", "/");
        partition.put("totalSpace", "100.00 GB");
        partition.put("freeSpace", "60.00 GB");
        partition.put("usedSpace", "40.00 GB");
        partition.put("usage", "40.00%");
        mockDiskInfo.put("partitions", java.util.Arrays.asList(partition));

        when(performanceMonitorService.getDiskInfo()).thenReturn(mockDiskInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/disk")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.partitions").isArray())
                .andExpect(jsonPath("$.data.partitions[0].path").value("/"));

        verify(performanceMonitorService, times(1)).getDiskInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetJvmInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockJvmInfo = new HashMap<>();
        mockJvmInfo.put("jvmName", "OpenJDK 64-Bit Server VM");
        mockJvmInfo.put("jvmVersion", "25.291-b10");
        mockJvmInfo.put("threadCount", 25);
        mockJvmInfo.put("daemonThreadCount", 20);

        Map<String, Object> heapMemory = new HashMap<>();
        heapMemory.put("used", "512.00 MB");
        heapMemory.put("committed", "1.00 GB");
        heapMemory.put("max", "2.00 GB");
        mockJvmInfo.put("heapMemory", heapMemory);

        when(performanceMonitorService.getJvmInfo()).thenReturn(mockJvmInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/jvm")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.jvmName").value("OpenJDK 64-Bit Server VM"))
                .andExpect(jsonPath("$.data.threadCount").value(25));

        verify(performanceMonitorService, times(1)).getJvmInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetDatabaseInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockDatabaseInfo = new HashMap<>();
        Map<String, Object> poolInfo = new HashMap<>();
        poolInfo.put("poolName", "EDC-HikariCP");
        poolInfo.put("activeConnections", 5);
        poolInfo.put("idleConnections", 10);
        poolInfo.put("totalConnections", 15);
        poolInfo.put("maximumPoolSize", 50);
        mockDatabaseInfo.put("connectionPool", poolInfo);

        when(performanceMonitorService.getDatabaseInfo()).thenReturn(mockDatabaseInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/database")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.connectionPool.poolName").value("EDC-HikariCP"))
                .andExpect(jsonPath("$.data.connectionPool.activeConnections").value(5));

        verify(performanceMonitorService, times(1)).getDatabaseInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetThreadPoolInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockThreadPoolInfo = new HashMap<>();
        mockThreadPoolInfo.put("totalThreads", 25);
        mockThreadPoolInfo.put("activeThreads", 5);
        mockThreadPoolInfo.put("daemonThreads", 20);

        when(performanceMonitorService.getThreadPoolInfo()).thenReturn(mockThreadPoolInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/thread-pool")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalThreads").value(25))
                .andExpect(jsonPath("$.data.activeThreads").value(5));

        verify(performanceMonitorService, times(1)).getThreadPoolInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetProcessInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockProcessInfo = new HashMap<>();
        mockProcessInfo.put("currentPid", "12345");
        mockProcessInfo.put("totalProcesses", 150);

        when(performanceMonitorService.getProcessInfo()).thenReturn(mockProcessInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/processes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.currentPid").value("12345"))
                .andExpect(jsonPath("$.data.totalProcesses").value(150));

        verify(performanceMonitorService, times(1)).getProcessInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetDeadlockInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockDeadlockInfo = new HashMap<>();
        mockDeadlockInfo.put("hasDeadlock", false);
        mockDeadlockInfo.put("jvmDeadlocks", java.util.Collections.emptyList());
        mockDeadlockInfo.put("databaseDeadlocks", java.util.Collections.emptyList());

        when(performanceMonitorService.getDeadlockInfo()).thenReturn(mockDeadlockInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/deadlocks")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.hasDeadlock").value(false));

        verify(performanceMonitorService, times(1)).getDeadlockInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetCacheInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockCacheInfo = new HashMap<>();
        mockCacheInfo.put("redisStatus", "连接正常");

        when(performanceMonitorService.getCacheInfo()).thenReturn(mockCacheInfo);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/cache")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.redisStatus").value("连接正常"));

        verify(performanceMonitorService, times(1)).getCacheInfo();
    }

    @Test
    void testGetHealthInfo() throws Exception {
        // 准备测试数据
        Map<String, Object> mockHealthInfo = new HashMap<>();
        mockHealthInfo.put("status", "UP");

        when(performanceMonitorService.getHealthInfo()).thenReturn(mockHealthInfo);

        // 执行测试（健康检查不需要权限）
        mockMvc.perform(get("/monitor/performance/health")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value("UP"));

        verify(performanceMonitorService, times(1)).getHealthInfo();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:admin"})
    void testForceGarbageCollection() throws Exception {
        // 执行测试
        mockMvc.perform(post("/monitor/performance/gc")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("垃圾回收执行完成"));

        verify(performanceMonitorService, times(1)).forceGarbageCollection();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:admin"})
    void testClearSystemCache() throws Exception {
        // 执行测试
        mockMvc.perform(post("/monitor/performance/clear-cache")
                        .param("cacheName", "user-cache")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("缓存 [user-cache] 清理完成"));

        verify(performanceMonitorService, times(1)).clearSystemCache("user-cache");
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testGetSystemMetrics() throws Exception {
        // 准备测试数据
        Map<String, Object> mockMetrics = new HashMap<>();
        mockMetrics.put("message", "指标端点未配置");

        when(performanceMonitorService.getSystemMetrics()).thenReturn(mockMetrics);

        // 执行测试
        mockMvc.perform(get("/monitor/performance/metrics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.message").value("指标端点未配置"));

        verify(performanceMonitorService, times(1)).getSystemMetrics();
    }

    @Test
    @WithMockUser(authorities = {"monitor:performance:view"})
    void testServiceException() throws Exception {
        // 模拟服务异常
        when(performanceMonitorService.getCpuInfo()).thenThrow(new RuntimeException("服务异常"));

        // 执行测试
        mockMvc.perform(get("/monitor/performance/cpu")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("获取CPU信息失败: 服务异常"));

        verify(performanceMonitorService, times(1)).getCpuInfo();
    }
}
