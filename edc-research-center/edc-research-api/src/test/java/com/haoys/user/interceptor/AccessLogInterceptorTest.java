package com.haoys.user.interceptor;

import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.service.SystemMonitorService;
import com.haoys.user.util.JwtTokenHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * AccessLogInterceptor测试类
 * 验证JWT Token解析和用户信息获取功能
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@ActiveProfiles("test")
public class AccessLogInterceptorTest {

    @Mock
    private SystemMonitorService systemMonitorService;

    @Mock
    private JwtTokenHelper jwtTokenHelper;

    @Mock
    private RedisTemplateService redisTemplateService;

    @InjectMocks
    private AccessLogInterceptor accessLogInterceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
    }

    /**
     * 测试JWT Token解析用户信息功能
     */
    @Test
    public void testJwtTokenUserInfoExtraction() {
        log.info("开始测试JWT Token解析用户信息功能...");

        // 模拟JWT Token
        String mockToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.signature";
        String mockTokenKey = "login_tokens:test-key";
        
        // 设置请求头
        request.addHeader("Authorization", mockToken);
        request.setRequestURI("/api/test");
        request.setMethod("GET");
        request.setRemoteAddr("192.168.1.100");

        // 模拟LoginUserInfo
        LoginUserInfo mockUserInfo = new LoginUserInfo();
        mockUserInfo.setId(1001L);
        mockUserInfo.setUserName("testuser");
        mockUserInfo.setRealName("测试用户");

        // 配置Mock行为
        when(jwtTokenHelper.getTokenKeyFromRequest(any())).thenReturn(mockTokenKey);
        when(redisTemplateService.get(mockTokenKey)).thenReturn(mockUserInfo);

        try {
            // 执行拦截器
            boolean result = accessLogInterceptor.preHandle(request, response, new Object());
            
            log.info("拦截器执行结果: {}", result);
            
            // 验证SystemMonitorService被调用
            verify(systemMonitorService, times(1)).recordAccessLog(
                anyString(), // userId
                anyString(), // userName
                anyString(), // realName
                anyString(), // sessionId
                anyString(), // requestUrl
                anyString(), // requestMethod
                anyString(), // requestIp
                anyString(), // userAgent
                any(),       // responseTime
                any()        // responseStatus
            );

            log.info("JWT Token解析用户信息功能测试通过!");

        } catch (Exception e) {
            log.error("测试执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试匿名用户处理功能
     */
    @Test
    public void testAnonymousUserHandling() {
        log.info("开始测试匿名用户处理功能...");

        // 设置无Token的请求
        request.setRequestURI("/api/public");
        request.setMethod("GET");
        request.setRemoteAddr("*************");

        // 配置Mock行为 - 返回null表示无用户信息
        when(jwtTokenHelper.getTokenKeyFromRequest(any())).thenReturn(null);

        try {
            // 执行拦截器
            boolean result = accessLogInterceptor.preHandle(request, response, new Object());
            
            log.info("匿名用户拦截器执行结果: {}", result);
            
            // 验证SystemMonitorService被调用，且用户信息为匿名
            verify(systemMonitorService, times(1)).recordAccessLog(
                eq("anonymousUser"), // userId应该是anonymousUser
                eq("anonymousUser"), // userName应该是anonymousUser
                eq("anonymousUser"), // realName应该是anonymousUser
                anyString(),         // sessionId
                anyString(),         // requestUrl
                anyString(),         // requestMethod
                anyString(),         // requestIp
                anyString(),         // userAgent
                any(),               // responseTime
                any()                // responseStatus
            );

            log.info("匿名用户处理功能测试通过!");

        } catch (Exception e) {
            log.error("匿名用户测试执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试Token解析异常处理
     */
    @Test
    public void testTokenParsingException() {
        log.info("开始测试Token解析异常处理功能...");

        // 设置无效Token的请求
        request.addHeader("Authorization", "Bearer invalid.token.here");
        request.setRequestURI("/api/test");
        request.setMethod("POST");
        request.setRemoteAddr("*************");

        // 配置Mock行为 - 模拟解析异常
        when(jwtTokenHelper.getTokenKeyFromRequest(any())).thenThrow(new RuntimeException("Token解析失败"));

        try {
            // 执行拦截器
            boolean result = accessLogInterceptor.preHandle(request, response, new Object());
            
            log.info("异常处理拦截器执行结果: {}", result);
            
            // 验证即使出现异常，也会记录访问日志，且用户信息为匿名
            verify(systemMonitorService, times(1)).recordAccessLog(
                eq("anonymousUser"), // userId应该是anonymousUser
                eq("anonymousUser"), // userName应该是anonymousUser
                eq("anonymousUser"), // realName应该是anonymousUser
                anyString(),         // sessionId
                anyString(),         // requestUrl
                anyString(),         // requestMethod
                anyString(),         // requestIp
                anyString(),         // userAgent
                any(),               // responseTime
                any()                // responseStatus
            );

            log.info("Token解析异常处理功能测试通过!");

        } catch (Exception e) {
            log.error("异常处理测试执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试不同请求方法的处理
     */
    @Test
    public void testDifferentRequestMethods() {
        log.info("开始测试不同请求方法的处理功能...");

        String[] methods = {"GET", "POST", "PUT", "DELETE", "PATCH"};
        
        for (String method : methods) {
            // 重置Mock
            reset(systemMonitorService);
            
            // 设置请求
            MockHttpServletRequest testRequest = new MockHttpServletRequest();
            testRequest.setMethod(method);
            testRequest.setRequestURI("/api/test/" + method.toLowerCase());
            testRequest.setRemoteAddr("192.168.1.103");

            // 配置Mock行为
            when(jwtTokenHelper.getTokenKeyFromRequest(any())).thenReturn(null);

            try {
                // 执行拦截器
                boolean result = accessLogInterceptor.preHandle(testRequest, response, new Object());
                
                log.info("{} 方法拦截器执行结果: {}", method, result);
                
                // 验证每种方法都被正确记录
                verify(systemMonitorService, times(1)).recordAccessLog(
                    anyString(), anyString(), anyString(), anyString(),
                    contains(method.toLowerCase()), // requestUrl包含方法名
                    eq(method),  // requestMethod应该匹配
                    anyString(), anyString(), any(), any()
                );

            } catch (Exception e) {
                log.error("{} 方法测试执行失败: {}", method, e.getMessage(), e);
            }
        }

        log.info("不同请求方法处理功能测试通过!");
    }

    /**
     * 测试用户信息完整性
     */
    @Test
    public void testUserInfoCompleteness() {
        log.info("开始测试用户信息完整性...");

        // 模拟完整的用户信息
        String mockTokenKey = "login_tokens:complete-user-key";
        LoginUserInfo completeUserInfo = new LoginUserInfo();
        completeUserInfo.setId(2001L);
        completeUserInfo.setUserName("completeuser");
        completeUserInfo.setRealName("完整用户信息");

        // 设置请求
        request.addHeader("Authorization", "Bearer complete.token.here");
        request.setRequestURI("/api/complete-test");
        request.setMethod("GET");
        request.setRemoteAddr("192.168.1.104");

        // 配置Mock行为
        when(jwtTokenHelper.getTokenKeyFromRequest(any())).thenReturn(mockTokenKey);
        when(redisTemplateService.get(mockTokenKey)).thenReturn(completeUserInfo);

        try {
            // 执行拦截器
            boolean result = accessLogInterceptor.preHandle(request, response, new Object());
            
            log.info("完整用户信息拦截器执行结果: {}", result);
            
            // 验证完整用户信息被正确记录
            verify(systemMonitorService, times(1)).recordAccessLog(
                eq("2001"),           // userId
                eq("completeuser"),   // userName
                eq("完整用户信息"),      // realName
                anyString(),          // sessionId
                anyString(),          // requestUrl
                anyString(),          // requestMethod
                anyString(),          // requestIp
                anyString(),          // userAgent
                any(),                // responseTime
                any()                 // responseStatus
            );

            log.info("用户信息完整性测试通过!");

        } catch (Exception e) {
            log.error("用户信息完整性测试执行失败: {}", e.getMessage(), e);
        }
    }
}
