package com.haoys.user.security.interceptor;

import com.haoys.user.service.SecureTokenService;
import com.haoys.user.util.SecureTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AccessToken验证拦截器测试类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class AccessTokenValidationInterceptorTest {
    
    @Autowired
    private AccessTokenValidationInterceptor accessTokenValidationInterceptor;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    
    @BeforeEach
    public void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
    }
    
    @Test
    public void testCriticalPathValidation() {
        log.info("=== 测试关键路径验证 ===");
        
        // 测试关键路径
        String[] criticalPaths = {
            "/api/secure/token/generateCode",
            "/api/secure/token/getAccessToken",
            "/api/secure/token/validateToken",
            "/api/secure/token/refreshToken"
        };
        
        for (String path : criticalPaths) {
            request.setRequestURI(path);
            
            try {
                boolean result = accessTokenValidationInterceptor.preHandle(request, response, new Object());
                assertTrue(result, "关键路径验证应该通过: " + path);
                log.info("关键路径验证通过: {}", path);
            } catch (Exception e) {
                log.error("关键路径验证失败: {} - {}", path, e.getMessage());
                fail("关键路径验证失败: " + path + " - " + e.getMessage());
            }
        }
    }
    
    @Test
    public void testNonCriticalPathValidation() {
        log.info("=== 测试非关键路径验证 ===");
        
        // 测试非关键路径
        String[] nonCriticalPaths = {
            "/api/user/info",
            "/api/project/list",
            "/api/system/config",
            "/api/other/endpoint"
        };
        
        for (String path : nonCriticalPaths) {
            request.setRequestURI(path);
            
            try {
                boolean result = accessTokenValidationInterceptor.preHandle(request, response, new Object());
                assertTrue(result, "非关键路径应该直接通过: " + path);
                log.info("非关键路径验证通过: {}", path);
            } catch (Exception e) {
                log.error("非关键路径验证失败: {} - {}", path, e.getMessage());
                fail("非关键路径验证失败: " + path + " - " + e.getMessage());
            }
        }
    }
    
    @Test
    public void testSecureTokenServiceValidation() {
        log.info("=== 测试SecureTokenService验证 ===");
        
        try {
            // 检查服务是否存在
            SecureTokenService secureTokenService = applicationContext.getBean(SecureTokenService.class);
            assertNotNull(secureTokenService, "SecureTokenService应该存在");
            log.info("SecureTokenService存在: {}", secureTokenService.getClass().getName());
            
            // 检查关键方法是否存在
            Class<?> serviceClass = secureTokenService.getClass();
            String[] criticalMethods = {
                "generateCode",
                "getAccessToken",
                "validateAccessToken",
                "refreshAccessToken"
            };
            
            for (String methodName : criticalMethods) {
                boolean methodExists = false;
                for (Method method : serviceClass.getMethods()) {
                    if (method.getName().equals(methodName)) {
                        methodExists = true;
                        break;
                    }
                }
                assertTrue(methodExists, "关键方法应该存在: " + methodName);
                log.info("关键方法存在: {}", methodName);
            }
            
        } catch (Exception e) {
            log.error("SecureTokenService验证失败: {}", e.getMessage());
            fail("SecureTokenService验证失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSecureTokenUtilValidation() {
        log.info("=== 测试SecureTokenUtil验证 ===");
        
        try {
            // 检查工具类是否存在
            SecureTokenUtil secureTokenUtil = applicationContext.getBean(SecureTokenUtil.class);
            assertNotNull(secureTokenUtil, "SecureTokenUtil应该存在");
            log.info("SecureTokenUtil存在: {}", secureTokenUtil.getClass().getName());
            
            // 检查关键方法是否存在
            Class<?> utilClass = secureTokenUtil.getClass();
            String[] criticalMethods = {
                "isValidAccessToken",
                "getUserIdFromToken",
                "getRemainingTime"
            };
            
            for (String methodName : criticalMethods) {
                boolean methodExists = false;
                for (Method method : utilClass.getMethods()) {
                    if (method.getName().equals(methodName)) {
                        methodExists = true;
                        break;
                    }
                }
                assertTrue(methodExists, "关键方法应该存在: " + methodName);
                log.info("关键方法存在: {}", methodName);
            }
            
        } catch (Exception e) {
            log.error("SecureTokenUtil验证失败: {}", e.getMessage());
            fail("SecureTokenUtil验证失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testHandlerMethodValidation() {
        log.info("=== 测试处理器方法验证 ===");
        
        try {
            // 创建一个模拟的HandlerMethod
            Method testMethod = this.getClass().getMethod("testHandlerMethodValidation");
            HandlerMethod handlerMethod = new HandlerMethod(this, testMethod);
            
            request.setRequestURI("/api/secure/token/generateCode");
            
            boolean result = accessTokenValidationInterceptor.preHandle(request, response, handlerMethod);
            assertTrue(result, "处理器方法验证应该通过");
            log.info("处理器方法验证通过");
            
        } catch (Exception e) {
            log.error("处理器方法验证失败: {}", e.getMessage());
            fail("处理器方法验证失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testInterceptorConfiguration() {
        log.info("=== 测试拦截器配置 ===");
        
        // 验证拦截器是否正确注入
        assertNotNull(accessTokenValidationInterceptor, "拦截器应该被正确注入");
        log.info("拦截器注入成功: {}", accessTokenValidationInterceptor.getClass().getName());
        
        // 验证ApplicationContext是否正确注入
        assertNotNull(applicationContext, "ApplicationContext应该被正确注入");
        log.info("ApplicationContext注入成功");
    }
    
    @Test
    public void testAfterCompletion() {
        log.info("=== 测试afterCompletion方法 ===");
        
        request.setRequestURI("/api/secure/token/generateCode");
        
        try {
            // 测试afterCompletion方法不会抛出异常
            accessTokenValidationInterceptor.afterCompletion(request, response, new Object(), null);
            log.info("afterCompletion方法执行成功");
            
            // 测试带异常的afterCompletion
            Exception testException = new RuntimeException("测试异常");
            accessTokenValidationInterceptor.afterCompletion(request, response, new Object(), testException);
            log.info("带异常的afterCompletion方法执行成功");
            
        } catch (Exception e) {
            log.error("afterCompletion方法测试失败: {}", e.getMessage());
            fail("afterCompletion方法测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSecurityValidationIntegrity() {
        log.info("=== 测试安全验证完整性 ===");
        
        request.setRequestURI("/api/secure/token/generateCode");
        
        try {
            boolean result = accessTokenValidationInterceptor.preHandle(request, response, new Object());
            assertTrue(result, "安全验证应该通过");
            
            // 验证响应状态
            assertEquals(200, response.getStatus(), "响应状态应该是200");
            
            log.info("安全验证完整性测试通过");
            
        } catch (Exception e) {
            log.error("安全验证完整性测试失败: {}", e.getMessage());
            fail("安全验证完整性测试失败: " + e.getMessage());
        }
    }
}
