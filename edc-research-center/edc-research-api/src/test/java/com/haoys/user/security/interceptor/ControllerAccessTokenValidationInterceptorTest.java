package com.haoys.user.security.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 控制器AccessToken验证拦截器测试类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ControllerAccessTokenValidationInterceptorTest {
    
    @Autowired
    private ControllerAccessTokenValidationInterceptor controllerAccessTokenValidationInterceptor;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    
    @BeforeEach
    public void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
    }
    
    @Test
    public void testCriticalControllerPathValidation() {
        log.info("=== 测试关键控制器路径验证 ===");
        
        // 测试关键控制器路径
        String[] criticalPaths = {
            "/api/monitor/cluster/status",
            "/api/monitor/access-log/list",
            "/api/monitor/log-viewer/view",
            "/api/monitor/quartz/jobs",
            "/api/monitor/redis/info",
            "/api/monitor/system/status",
            "/api/monitor/system-view/dashboard"
        };
        
        for (String path : criticalPaths) {
            request.setRequestURI(path);
            
            try {
                boolean result = controllerAccessTokenValidationInterceptor.preHandle(request, response, new Object());
                // 注意：由于控制器可能不存在，这里可能返回false，这是正常的
                log.info("关键路径验证结果: {} -> {}", path, result);
                
                if (!result) {
                    // 检查响应状态是否为403
                    assertEquals(403, response.getStatus(), "安全验证失败应该返回403状态码");
                    log.info("安全验证正确拒绝了请求: {}", path);
                } else {
                    log.info("安全验证通过: {}", path);
                }
                
                // 重置响应状态
                response.setStatus(200);
                
            } catch (Exception e) {
                log.error("关键路径验证异常: {} - {}", path, e.getMessage());
            }
        }
    }
    
    @Test
    public void testNonCriticalControllerPathValidation() {
        log.info("=== 测试非关键控制器路径验证 ===");
        
        // 测试非关键控制器路径
        String[] nonCriticalPaths = {
            "/api/user/info",
            "/api/project/list",
            "/api/system/config",
            "/api/other/endpoint",
            "/api/public/health"
        };
        
        for (String path : nonCriticalPaths) {
            request.setRequestURI(path);
            
            try {
                boolean result = controllerAccessTokenValidationInterceptor.preHandle(request, response, new Object());
                assertTrue(result, "非关键路径应该直接通过: " + path);
                assertEquals(200, response.getStatus(), "非关键路径响应状态应该是200");
                log.info("非关键路径验证通过: {}", path);
            } catch (Exception e) {
                log.error("非关键路径验证失败: {} - {}", path, e.getMessage());
                fail("非关键路径验证失败: " + path + " - " + e.getMessage());
            }
        }
    }
    
    @Test
    public void testControllerExistenceValidation() {
        log.info("=== 测试控制器存在性验证 ===");
        
        // 测试已知存在的控制器（如果有的话）
        String[] existingControllers = {
            "SystemMonitorController",
            "AccessLogManagementController"
        };
        
        for (String controllerName : existingControllers) {
            try {
                // 尝试获取控制器Bean
                Object controller = null;
                try {
                    controller = applicationContext.getBean(controllerName);
                } catch (Exception e) {
                    try {
                        String beanName = Character.toLowerCase(controllerName.charAt(0)) + controllerName.substring(1);
                        controller = applicationContext.getBean(beanName);
                    } catch (Exception e2) {
                        log.warn("控制器不存在: {} - 这可能是正常的", controllerName);
                        continue;
                    }
                }
                
                if (controller != null) {
                    log.info("控制器存在: {} -> {}", controllerName, controller.getClass().getName());
                    
                    // 检查控制器的方法
                    java.lang.reflect.Method[] methods = controller.getClass().getMethods();
                    boolean hasTokenMethod = false;
                    for (java.lang.reflect.Method method : methods) {
                        if (method.getName().toLowerCase().contains("token") || 
                            method.getName().toLowerCase().contains("access")) {
                            hasTokenMethod = true;
                            log.info("找到可能的Token验证方法: {}.{}", controllerName, method.getName());
                            break;
                        }
                    }
                    
                    if (!hasTokenMethod) {
                        log.warn("控制器 {} 可能缺少Token验证方法", controllerName);
                    }
                }
                
            } catch (Exception e) {
                log.error("控制器存在性验证失败: {} - {}", controllerName, e.getMessage());
            }
        }
    }
    
    @Test
    public void testInterceptorConfiguration() {
        log.info("=== 测试拦截器配置 ===");
        
        // 验证拦截器是否正确注入
        assertNotNull(controllerAccessTokenValidationInterceptor, "拦截器应该被正确注入");
        log.info("拦截器注入成功: {}", controllerAccessTokenValidationInterceptor.getClass().getName());
        
        // 验证ApplicationContext是否正确注入
        assertNotNull(applicationContext, "ApplicationContext应该被正确注入");
        log.info("ApplicationContext注入成功");
    }
    
    @Test
    public void testAfterCompletion() {
        log.info("=== 测试afterCompletion方法 ===");
        
        request.setRequestURI("/api/monitor/system/status");
        
        try {
            // 测试afterCompletion方法不会抛出异常
            controllerAccessTokenValidationInterceptor.afterCompletion(request, response, new Object(), null);
            log.info("afterCompletion方法执行成功");
            
            // 测试带异常的afterCompletion
            Exception testException = new RuntimeException("测试异常");
            controllerAccessTokenValidationInterceptor.afterCompletion(request, response, new Object(), testException);
            log.info("带异常的afterCompletion方法执行成功");
            
        } catch (Exception e) {
            log.error("afterCompletion方法测试失败: {}", e.getMessage());
            fail("afterCompletion方法测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSecurityValidationIntegrity() {
        log.info("=== 测试安全验证完整性 ===");
        
        // 测试多个关键路径的安全验证
        String[] testPaths = {
            "/api/monitor/cluster",
            "/api/monitor/redis",
            "/api/monitor/system"
        };
        
        for (String path : testPaths) {
            request.setRequestURI(path);
            
            try {
                boolean result = controllerAccessTokenValidationInterceptor.preHandle(request, response, new Object());
                
                // 记录验证结果
                if (result) {
                    log.info("安全验证通过: {}", path);
                } else {
                    log.info("安全验证拒绝: {} (状态码: {})", path, response.getStatus());
                }
                
                // 重置响应状态
                response.setStatus(200);
                
            } catch (Exception e) {
                log.error("安全验证异常: {} - {}", path, e.getMessage());
            }
        }
        
        log.info("安全验证完整性测试完成");
    }
    
    @Test
    public void testValidationDisabled() {
        log.info("=== 测试验证禁用功能 ===");
        
        // 注意：这个测试需要修改配置，在实际环境中可能不适用
        // 这里只是演示如何测试禁用功能
        
        request.setRequestURI("/api/monitor/system/status");
        
        try {
            // 即使验证被禁用，方法也应该正常执行
            boolean result = controllerAccessTokenValidationInterceptor.preHandle(request, response, new Object());
            log.info("验证禁用测试结果: {}", result);
            
        } catch (Exception e) {
            log.error("验证禁用测试失败: {}", e.getMessage());
        }
    }
}
