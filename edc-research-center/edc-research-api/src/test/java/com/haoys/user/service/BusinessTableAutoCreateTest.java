package com.haoys.user.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 业务表自动创建功能测试类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class BusinessTableAutoCreateTest {
    
    @Autowired
    private DatabaseTableService databaseTableService;
    
    @Autowired
    private BusinessProcessService businessProcessService;
    
    @Test
    public void testDatabaseInfo() {
        log.info("=== 测试获取数据库信息 ===");
        
        Map<String, Object> dbInfo = databaseTableService.getDatabaseInfo();
        
        assertNotNull(dbInfo);
        assertNotNull(dbInfo.get("database"));
        assertNotNull(dbInfo.get("version"));
        
        log.info("数据库: {}", dbInfo.get("database"));
        log.info("版本: {}", dbInfo.get("version"));
        log.info("字符集: {}", dbInfo.get("charset"));
        log.info("表数量: {}", dbInfo.get("tableCount"));
    }
    
    @Test
    public void testTableExists() {
        log.info("=== 测试检查表是否存在 ===");
        
        // 测试检查一个肯定存在的系统表
        boolean existsSystemTable = databaseTableService.tableExists("information_schema.tables");
        log.info("系统表存在检查: {}", existsSystemTable);
        
        // 测试检查业务表
        boolean existsSecureAppConfig = databaseTableService.tableExists("secure_app_config");
        log.info("secure_app_config表存在: {}", existsSecureAppConfig);
        
        boolean existsSecureAppRequestLog = databaseTableService.tableExists("secure_app_request_log");
        log.info("secure_app_request_log表存在: {}", existsSecureAppRequestLog);
    }
    
    @Test
    public void testCheckAndCreateBusinessTables() {
        log.info("=== 测试检查并创建业务表 ===");
        
        Map<String, Object> result = databaseTableService.checkAndCreateBusinessTables();
        
        assertNotNull(result);
        assertNotNull(result.get("totalTables"));
        assertNotNull(result.get("createdCount"));
        assertNotNull(result.get("existingCount"));
        assertNotNull(result.get("failedCount"));
        
        log.info("总表数: {}", result.get("totalTables"));
        log.info("新建表数: {}", result.get("createdCount"));
        log.info("已存在表数: {}", result.get("existingCount"));
        log.info("失败表数: {}", result.get("failedCount"));
        
        if (result.get("createdTables") != null) {
            log.info("新建的表: {}", result.get("createdTables"));
        }
        
        if (result.get("existingTables") != null) {
            log.info("已存在的表: {}", result.get("existingTables"));
        }
        
        if (result.get("failedTables") != null) {
            log.info("失败的表: {}", result.get("failedTables"));
        }
        
        // 验证失败数量应该为0
        assertEquals(0, result.get("failedCount"));
    }
    
    @Test
    public void testBusinessProcessIntegrity() {
        log.info("=== 测试业务流程完整性检查 ===");
        
        Map<String, Object> result = businessProcessService.checkBusinessProcessIntegrity();
        
        assertNotNull(result);
        assertNotNull(result.get("isComplete"));
        assertNotNull(result.get("existingComponents"));
        assertNotNull(result.get("missingComponents"));
        
        log.info("流程完整: {}", result.get("isComplete"));
        log.info("存在组件数: {}", result.get("existingCount"));
        log.info("缺失组件数: {}", result.get("missingCount"));
        
        if (result.get("existingComponents") != null) {
            log.info("存在的组件: {}", result.get("existingComponents"));
        }
        
        if (result.get("missingComponents") != null) {
            log.info("缺失的组件: {}", result.get("missingComponents"));
        }
    }
    
    @Test
    public void testInitializeBusinessProcess() {
        log.info("=== 测试初始化业务流程 ===");
        
        Map<String, Object> result = businessProcessService.initializeBusinessProcess();
        
        assertNotNull(result);
        assertNotNull(result.get("success"));
        assertNotNull(result.get("completedSteps"));
        assertNotNull(result.get("failedSteps"));
        
        log.info("初始化成功: {}", result.get("success"));
        log.info("完成步骤数: {}", result.get("completedCount"));
        log.info("失败步骤数: {}", result.get("failedCount"));
        
        if (result.get("completedSteps") != null) {
            log.info("完成的步骤: {}", result.get("completedSteps"));
        }
        
        if (result.get("failedSteps") != null) {
            log.info("失败的步骤: {}", result.get("failedSteps"));
        }
        
        // 验证初始化应该成功
        assertTrue((Boolean) result.get("success"));
    }
    
    @Test
    public void testCreateSecureTokenBusinessProcess() {
        log.info("=== 测试创建安全Token业务流程 ===");
        
        Map<String, Object> result = businessProcessService.createBusinessTableAndProcess("secure_token");
        
        assertNotNull(result);
        assertNotNull(result.get("success"));
        
        log.info("创建成功: {}", result.get("success"));
        log.info("业务类型: {}", result.get("businessType"));
        
        if (result.get("tableResult") != null) {
            Map<String, Object> tableResult = (Map<String, Object>) result.get("tableResult");
            log.info("表创建结果: 总计={}, 新建={}, 已存在={}, 失败={}", 
                    tableResult.get("totalTables"),
                    tableResult.get("createdCount"),
                    tableResult.get("existingCount"),
                    tableResult.get("failedCount"));
        }
        
        // 验证创建应该成功
        assertTrue((Boolean) result.get("success"));
    }
    
    @Test
    public void testGetBusinessProcessStatus() {
        log.info("=== 测试获取业务流程状态 ===");
        
        Map<String, Object> status = businessProcessService.getBusinessProcessStatus();
        
        assertNotNull(status);
        assertNotNull(status.get("systemReady"));
        
        log.info("系统就绪: {}", status.get("systemReady"));
        log.info("当前环境: {}", status.get("currentEnvironment"));
        log.info("应用配置数量: {}", status.get("appConfigCount"));
        
        if (status.get("database") != null) {
            Map<String, Object> dbInfo = (Map<String, Object>) status.get("database");
            log.info("数据库信息: {}", dbInfo);
        }
        
        if (status.get("integrity") != null) {
            Map<String, Object> integrity = (Map<String, Object>) status.get("integrity");
            log.info("完整性检查: {}", integrity.get("isComplete"));
        }
    }
    
    @Test
    public void testExecuteSqlScript() {
        log.info("=== 测试执行SQL脚本 ===");
        
        // 测试执行一个简单的SQL脚本
        String testSql = "SELECT 1 as test_value; SELECT 2 as another_value;";
        
        boolean result = databaseTableService.executeSqlScript(testSql);
        
        assertTrue(result);
        log.info("SQL脚本执行结果: {}", result);
    }
    
    @Test
    public void testTableStructure() {
        log.info("=== 测试获取表结构 ===");
        
        // 先确保表存在
        databaseTableService.checkAndCreateBusinessTables();
        
        // 获取secure_app_config表结构
        if (databaseTableService.tableExists("secure_app_config")) {
            List<Map<String, Object>> structure = databaseTableService.getTableStructure("secure_app_config");

            assertNotNull(structure);
            assertFalse(structure.isEmpty());

            log.info("secure_app_config表结构:");
            for (Map<String, Object> column : structure) {
                log.info("  字段: {}, 类型: {}, 是否为空: {}, 键: {}, 默认值: {}",
                        column.get("Field"),
                        column.get("Type"),
                        column.get("Null"),
                        column.get("Key"),
                        column.get("Default"));
            }
        } else {
            log.warn("secure_app_config表不存在，跳过结构检查");
        }
    }
}
