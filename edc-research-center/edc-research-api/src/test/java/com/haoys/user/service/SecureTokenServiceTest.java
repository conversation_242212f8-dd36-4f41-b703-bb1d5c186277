package com.haoys.user.service;

import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全Token服务测试类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class SecureTokenServiceTest {
    
    @Autowired
    private SecureTokenService secureTokenService;
    
    @Test
    public void testGenerateCode() {
        log.info("=== 测试生成Code ===");

        SecureTokenParam.GenerateCodeParam param = new SecureTokenParam.GenerateCodeParam();
        param.setAppId("edc_app_local");
        param.setAppSecret("edc_secret_local_2025_local123456");
        param.setUserId("testUser123");
        param.setExtraInfo("测试信息");
        param.setEnvironment("local");

        SecureTokenVo.CodeResponse response = secureTokenService.generateCode(param);

        assertNotNull(response);
        assertNotNull(response.getCode());
        assertNotNull(response.getRefreshCode());
        assertNotNull(response.getExpireTime());
        assertTrue(response.getExpiresIn() > 0);

        log.info("生成的Code: {}", response.getCode());
        log.info("生成的RefreshCode: {}", response.getRefreshCode());
        log.info("过期时间: {}", response.getExpireTime());
        log.info("有效期: {} 秒", response.getExpiresIn());
    }
    
    @Test
    public void testGetAccessToken() {
        log.info("=== 测试获取AccessToken ===");

        // 先生成Code
        SecureTokenParam.GenerateCodeParam generateParam = new SecureTokenParam.GenerateCodeParam();
        generateParam.setAppId("edc_app_local");
        generateParam.setAppSecret("edc_secret_local_2025_local123456");
        generateParam.setUserId("testUser456");
        generateParam.setExtraInfo("测试获取AccessToken");
        generateParam.setEnvironment("local");

        SecureTokenVo.CodeResponse codeResponse = secureTokenService.generateCode(generateParam);
        log.info("生成的Code: {}", codeResponse.getCode());
        log.info("生成的RefreshCode: {}", codeResponse.getRefreshCode());

        // 使用Code和RefreshCode获取AccessToken
        SecureTokenParam.GetAccessTokenParam getTokenParam = new SecureTokenParam.GetAccessTokenParam();
        getTokenParam.setCode(codeResponse.getCode());
        getTokenParam.setRefreshCode(codeResponse.getRefreshCode());

        SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(getTokenParam);

        assertNotNull(tokenResponse);
        assertNotNull(tokenResponse.getAccessToken());
        assertNotNull(tokenResponse.getExpireTime());
        assertTrue(tokenResponse.getExpiresIn() > 0);
        assertEquals("testUser456", tokenResponse.getUserId());

        log.info("生成的AccessToken: {}", tokenResponse.getAccessToken());
        log.info("关联用户ID: {}", tokenResponse.getUserId());
        log.info("过期时间: {}", tokenResponse.getExpireTime());
        log.info("有效期: {} 秒", tokenResponse.getExpiresIn());
    }
    
    @Test
    public void testValidateAccessToken() {
        log.info("=== 测试验证AccessToken ===");
        
        // 先生成Code和AccessToken
        SecureTokenParam.GenerateCodeParam generateParam = new SecureTokenParam.GenerateCodeParam();
        generateParam.setUserId("testUser789");
        generateParam.setExtraInfo("测试验证AccessToken");
        
        SecureTokenVo.CodeResponse codeResponse = secureTokenService.generateCode(generateParam);
        
        SecureTokenParam.GetAccessTokenParam getTokenParam = new SecureTokenParam.GetAccessTokenParam();
        getTokenParam.setRefreshCode(codeResponse.getRefreshCode());
        
        SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(getTokenParam);
        
        // 验证AccessToken
        SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(tokenResponse.getAccessToken());
        
        assertNotNull(validateResponse);
        assertTrue(validateResponse.getValid());
        assertEquals("testUser789", validateResponse.getUserId());
        assertEquals("测试验证AccessToken", validateResponse.getExtraInfo());
        assertTrue(validateResponse.getRemainingTime() > 0);
        
        log.info("Token验证结果: {}", validateResponse.getValid());
        log.info("关联用户ID: {}", validateResponse.getUserId());
        log.info("扩展信息: {}", validateResponse.getExtraInfo());
        log.info("剩余时间: {} 秒", validateResponse.getRemainingTime());
    }
    
    @Test
    public void testInvalidAccessToken() {
        log.info("=== 测试无效AccessToken ===");
        
        // 测试空Token
        SecureTokenVo.ValidateResponse response1 = secureTokenService.validateAccessToken("");
        assertFalse(response1.getValid());
        assertEquals(0L, response1.getRemainingTime());
        
        // 测试不存在的Token
        SecureTokenVo.ValidateResponse response2 = secureTokenService.validateAccessToken("invalid_token_123");
        assertFalse(response2.getValid());
        assertEquals(0L, response2.getRemainingTime());
        
        log.info("空Token验证结果: {}", response1.getValid());
        log.info("无效Token验证结果: {}", response2.getValid());
    }
    
    @Test
    public void testRefreshCodeReuse() {
        log.info("=== 测试RefreshCode重复使用 ===");
        
        // 生成Code
        SecureTokenParam.GenerateCodeParam generateParam = new SecureTokenParam.GenerateCodeParam();
        generateParam.setUserId("testUserReuse");
        
        SecureTokenVo.CodeResponse codeResponse = secureTokenService.generateCode(generateParam);
        
        // 第一次使用RefreshCode
        SecureTokenParam.GetAccessTokenParam getTokenParam = new SecureTokenParam.GetAccessTokenParam();
        getTokenParam.setRefreshCode(codeResponse.getRefreshCode());
        
        SecureTokenVo.AccessTokenResponse tokenResponse1 = secureTokenService.getAccessToken(getTokenParam);
        assertNotNull(tokenResponse1.getAccessToken());
        
        // 第二次使用相同的RefreshCode（应该失败）
        try {
            secureTokenService.getAccessToken(getTokenParam);
            fail("应该抛出异常，因为RefreshCode已被使用");
        } catch (RuntimeException e) {
            log.info("预期的异常: {}", e.getMessage());
            assertTrue(e.getMessage().contains("RefreshCode无效或已过期"));
        }
    }
    
    @Test
    public void testRevokeAccessToken() {
        log.info("=== 测试撤销AccessToken ===");
        
        // 生成AccessToken
        SecureTokenParam.GenerateCodeParam generateParam = new SecureTokenParam.GenerateCodeParam();
        generateParam.setUserId("testUserRevoke");
        
        SecureTokenVo.CodeResponse codeResponse = secureTokenService.generateCode(generateParam);
        
        SecureTokenParam.GetAccessTokenParam getTokenParam = new SecureTokenParam.GetAccessTokenParam();
        getTokenParam.setRefreshCode(codeResponse.getRefreshCode());
        
        SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(getTokenParam);
        
        // 验证Token有效
        SecureTokenVo.ValidateResponse validateResponse1 = secureTokenService.validateAccessToken(tokenResponse.getAccessToken());
        assertTrue(validateResponse1.getValid());
        
        // 撤销Token
        Boolean revokeResult = secureTokenService.revokeAccessToken(tokenResponse.getAccessToken());
        assertTrue(revokeResult);
        
        // 验证Token已无效
        SecureTokenVo.ValidateResponse validateResponse2 = secureTokenService.validateAccessToken(tokenResponse.getAccessToken());
        assertFalse(validateResponse2.getValid());
        
        log.info("撤销前Token有效: {}", validateResponse1.getValid());
        log.info("撤销结果: {}", revokeResult);
        log.info("撤销后Token有效: {}", validateResponse2.getValid());
    }
}
