package com.haoys.user.service;

import com.haoys.user.domain.vo.monitor.SystemPerformanceVo;
import com.haoys.user.service.impl.SystemPerformanceMonitorServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;

import javax.sql.DataSource;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 系统性能监控服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
public class SystemPerformanceMonitorServiceTest {

    private SystemPerformanceMonitorServiceImpl performanceMonitorService;

    @Mock
    private DataSource dataSource;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private CacheManager cacheManager;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        performanceMonitorService = new SystemPerformanceMonitorServiceImpl();
        // 注入mock对象
        performanceMonitorService.setDataSource(dataSource);
        performanceMonitorService.setRedisTemplate(redisTemplate);
        performanceMonitorService.setCacheManager(cacheManager);
    }

    @Test
    public void testGetSystemPerformanceOverview() {
        try {
            log.info("开始测试系统性能监控概览功能");

            // Mock Redis连接测试
            when(redisTemplate.getConnectionFactory()).thenReturn(null);
            when(cacheManager.getCacheNames()).thenReturn(Collections.emptyList());

            SystemPerformanceVo performanceVo = performanceMonitorService.getSystemPerformanceOverview();

            // 验证基本信息
            assertNotNull(performanceVo, "性能监控信息不能为空");
            assertNotNull(performanceVo.getMonitorTime(), "监控时间不能为空");
            assertNotNull(performanceVo.getSystemInfo(), "系统信息不能为空");
            assertNotNull(performanceVo.getCpuInfo(), "CPU信息不能为空");
            assertNotNull(performanceVo.getMemoryInfo(), "内存信息不能为空");
            assertNotNull(performanceVo.getDiskInfo(), "磁盘信息不能为空");
            assertNotNull(performanceVo.getJvmInfo(), "JVM信息不能为空");

            // 验证数据库信息
            SystemPerformanceVo.DatabaseInfoVo databaseInfo = performanceVo.getDatabaseInfo();
            assertNotNull(databaseInfo, "数据库信息不能为空");
            assertNotNull(databaseInfo.getConnectionPools(), "连接池信息不能为空");
            assertNotNull(databaseInfo.getDeadlocks(), "死锁信息不能为空");

            log.info("数据库连接池数量: {}", databaseInfo.getConnectionPools().size());
            log.info("检测到的死锁数量: {}", databaseInfo.getDeadlocks().size());

            // 验证缓存信息
            SystemPerformanceVo.CacheInfoVo cacheInfo = performanceVo.getCacheInfo();
            assertNotNull(cacheInfo, "缓存信息不能为空");
            assertNotNull(cacheInfo.getRedisStatus(), "Redis状态不能为空");
            assertNotNull(cacheInfo.getCacheStats(), "缓存统计不能为空");

            log.info("Redis状态: {}", cacheInfo.getRedisStatus());
            log.info("缓存数量: {}", cacheInfo.getCacheStats().size());

            // 验证其他信息
            assertNotNull(performanceVo.getThreadPoolInfo(), "线程池信息不能为空");
            assertNotNull(performanceVo.getProcessInfo(), "进程信息不能为空");

            log.info("系统性能监控概览测试通过");

        } catch (Exception e) {
            log.error("系统性能监控测试失败", e);
            fail("系统性能监控测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testDatabaseInfoNotEmpty() {
        try {
            log.info("开始测试数据库信息是否为空");
            
            SystemPerformanceVo performanceVo = performanceMonitorService.getSystemPerformanceOverview();
            SystemPerformanceVo.DatabaseInfoVo databaseInfo = performanceVo.getDatabaseInfo();
            
            // 数据库信息应该不为空
            assertNotNull(databaseInfo);
            assertNotNull(databaseInfo.getConnectionPools());
            assertNotNull(databaseInfo.getDeadlocks());
            
            // 如果有数据源配置，连接池信息应该不为空
            if (!databaseInfo.getConnectionPools().isEmpty()) {
                assertTrue(databaseInfo.getConnectionPools().size() > 0, "应该有连接池信息");
                log.info("数据库连接池信息验证通过");
            } else {
                log.warn("未检测到数据库连接池，可能数据源未配置");
            }
            
        } catch (Exception e) {
            log.error("数据库信息测试失败", e);
            fail("数据库信息测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testCacheInfoNotEmpty() {
        try {
            log.info("开始测试缓存信息是否为空");
            
            SystemPerformanceVo performanceVo = performanceMonitorService.getSystemPerformanceOverview();
            SystemPerformanceVo.CacheInfoVo cacheInfo = performanceVo.getCacheInfo();
            
            // 缓存信息应该不为空
            assertNotNull(cacheInfo);
            assertNotNull(cacheInfo.getRedisStatus());
            assertNotNull(cacheInfo.getCacheStats());
            
            // Redis状态应该有值
            assertFalse(cacheInfo.getRedisStatus().isEmpty(), "Redis状态不应该为空");
            log.info("Redis状态: {}", cacheInfo.getRedisStatus());
            
            // 缓存统计信息应该是Map
            assertTrue(cacheInfo.getCacheStats() instanceof java.util.Map, "缓存统计应该是Map类型");
            
            log.info("缓存信息验证通过");
            
        } catch (Exception e) {
            log.error("缓存信息测试失败", e);
            fail("缓存信息测试失败: " + e.getMessage());
        }
    }
}
