package com.haoys.user.system;

import com.haoys.user.service.SystemMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 登录统计功能验证测试
 * 验证system_online_user和system_access_log表的数据记录功能
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class LoginStatisticsVerificationTest {

    @Autowired
    private SystemMonitorService systemMonitorService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 测试记录用户登录到在线用户表
     */
    @Test
    public void testRecordUserLogin() {
        log.info("开始测试记录用户登录功能...");
        
        // 模拟用户登录数据
        String userId = "1001";
        String userName = "testuser";
        String realName = "测试用户";
        String sessionId = "test-session-" + System.currentTimeMillis();
        String loginIp = "*************";
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        String token = "test-token-" + System.currentTimeMillis();
        String loginType = "USERNAME_PASSWORD";
        String phoneNumber = null;

        // 记录用户登录
        systemMonitorService.recordUserLogin(
            userId, userName, realName, sessionId,
            loginIp, userAgent, token, loginType, phoneNumber
        );

        // 验证数据是否正确记录
        String sql = "SELECT COUNT(*) FROM system_online_user WHERE session_id = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, sessionId);
        
        log.info("在线用户表记录数量: {}", count);
        assert count != null && count > 0 : "在线用户表应该有记录";

        // 查询详细记录
        String detailSql = "SELECT * FROM system_online_user WHERE session_id = ?";
        List<Map<String, Object>> records = jdbcTemplate.queryForList(detailSql, sessionId);
        
        if (!records.isEmpty()) {
            Map<String, Object> record = records.get(0);
            log.info("在线用户记录详情: {}", record);
            
            assert userId.equals(record.get("user_id").toString()) : "用户ID应该匹配";
            assert userName.equals(record.get("user_name")) : "用户名应该匹配";
            assert loginType.equals(record.get("login_type")) : "登录类型应该匹配";
        }
        
        log.info("记录用户登录功能测试通过!");
    }

    /**
     * 测试记录登录日志到访问日志表
     */
    @Test
    public void testRecordLoginLog() {
        log.info("开始测试记录登录日志功能...");
        
        // 模拟登录日志数据
        String userId = "1002";
        String userName = "testuser2";
        String realName = "测试用户2";
        String sessionId = "test-session-log-" + System.currentTimeMillis();
        String requestUrl = "/admin/login";
        String requestMethod = "POST";
        String requestIp = "*************";
        String userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)";
        String loginType = "PHONE_CODE";
        String phoneNumber = "13800138000";
        Boolean loginSuccess = true;

        // 记录登录日志
        systemMonitorService.recordLoginLog(
            userId, userName, realName, sessionId,
            requestUrl, requestMethod, requestIp, userAgent,
            null, 200, loginType, phoneNumber, loginSuccess, null
        );

        // 验证数据是否正确记录
        String sql = "SELECT COUNT(*) FROM system_access_log WHERE session_id = ? AND is_login_request = 1";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, sessionId);
        
        log.info("访问日志表登录记录数量: {}", count);
        assert count != null && count > 0 : "访问日志表应该有登录记录";

        // 查询详细记录
        String detailSql = "SELECT * FROM system_access_log WHERE session_id = ? AND is_login_request = 1";
        List<Map<String, Object>> records = jdbcTemplate.queryForList(detailSql, sessionId);
        
        if (!records.isEmpty()) {
            Map<String, Object> record = records.get(0);
            log.info("登录日志记录详情: {}", record);
            
            assert userId.equals(record.get("user_id").toString()) : "用户ID应该匹配";
            assert loginType.equals(record.get("login_type")) : "登录类型应该匹配";
            assert phoneNumber.equals(record.get("phone_number")) : "手机号应该匹配";
            assert loginSuccess.equals(record.get("login_success")) : "登录成功状态应该匹配";
        }
        
        log.info("记录登录日志功能测试通过!");
    }

    /**
     * 测试手机验证码登录统计
     */
    @Test
    public void testPhoneCodeLoginStatistics() {
        log.info("开始测试手机验证码登录统计功能...");
        
        // 模拟手机验证码登录
        String userId = "1003";
        String userName = "mobileuser";
        String realName = "手机用户";
        String sessionId = "mobile-session-" + System.currentTimeMillis();
        String loginIp = "*************";
        String userAgent = "Mozilla/5.0 (Linux; Android 10; SM-G975F)";
        String token = "mobile-token-" + System.currentTimeMillis();
        String loginType = "PHONE_CODE";
        String phoneNumber = "13900139000";

        // 记录手机验证码登录
        systemMonitorService.recordUserLogin(
            userId, userName, realName, sessionId,
            loginIp, userAgent, token, loginType, phoneNumber
        );

        systemMonitorService.recordLoginLog(
            userId, userName, realName, sessionId,
            "/admin/verificationCodeLogin", "POST", loginIp, userAgent,
            null, 200, loginType, phoneNumber, true, null
        );

        // 验证在线用户表记录
        String onlineUserSql = "SELECT * FROM system_online_user WHERE session_id = ?";
        List<Map<String, Object>> onlineRecords = jdbcTemplate.queryForList(onlineUserSql, sessionId);
        
        assert !onlineRecords.isEmpty() : "应该有在线用户记录";
        Map<String, Object> onlineRecord = onlineRecords.get(0);
        assert phoneNumber.equals(onlineRecord.get("phone_number")) : "手机号应该正确记录";
        assert loginType.equals(onlineRecord.get("login_type")) : "登录类型应该是PHONE_CODE";

        // 验证访问日志表记录
        String accessLogSql = "SELECT * FROM system_access_log WHERE session_id = ? AND is_login_request = 1";
        List<Map<String, Object>> accessRecords = jdbcTemplate.queryForList(accessLogSql, sessionId);
        
        assert !accessRecords.isEmpty() : "应该有访问日志记录";
        Map<String, Object> accessRecord = accessRecords.get(0);
        assert phoneNumber.equals(accessRecord.get("phone_number")) : "访问日志中手机号应该正确";
        
        log.info("手机验证码登录统计功能测试通过!");
    }

    /**
     * 测试登录失败统计
     */
    @Test
    public void testLoginFailureStatistics() {
        log.info("开始测试登录失败统计功能...");
        
        // 模拟登录失败
        String sessionId = "fail-session-" + System.currentTimeMillis();
        String userName = "failuser";
        String requestIp = "*************";
        String failureReason = "用户名或密码错误";

        systemMonitorService.recordLoginLog(
            null, userName, null, sessionId,
            "/admin/login", "POST", requestIp, 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            null, 200, "USERNAME_PASSWORD", null, false, failureReason
        );

        // 验证失败记录
        String sql = "SELECT * FROM system_access_log WHERE session_id = ? AND login_success = 0";
        List<Map<String, Object>> records = jdbcTemplate.queryForList(sql, sessionId);
        
        assert !records.isEmpty() : "应该有登录失败记录";
        Map<String, Object> record = records.get(0);
        assert failureReason.equals(record.get("login_failure_reason")) : "失败原因应该正确记录";
        
        log.info("登录失败统计功能测试通过!");
    }

    /**
     * 测试统计数据查询功能
     */
    @Test
    public void testStatisticsQuery() {
        log.info("开始测试统计数据查询功能...");
        
        // 测试实时统计
        Map<String, Object> realTimeStats = systemMonitorService.getRealTimeStatistics();
        log.info("实时统计数据: {}", realTimeStats);
        
        assert realTimeStats.containsKey("onlineUsers") : "应该包含在线用户数";
        assert realTimeStats.containsKey("todayVisits") : "应该包含今日访问数";

        // 测试今日登录统计
        Map<String, Object> todayStats = systemMonitorService.getTodayLoginStatistics();
        log.info("今日登录统计: {}", todayStats);

        // 测试登录类型统计
        List<Map<String, Object>> loginTypeStats = systemMonitorService.getLoginTypeStatistics(
            "2025-01-16", "2025-01-16"
        );
        log.info("登录类型统计: {}", loginTypeStats);
        
        log.info("统计数据查询功能测试通过!");
    }
}
