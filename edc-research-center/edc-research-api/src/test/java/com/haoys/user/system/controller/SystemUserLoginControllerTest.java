package com.haoys.user.system.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.haoys.user.domain.param.system.SystemUserLoginParam;
import com.haoys.user.domain.param.system.UserVerificationCodeParam;
import com.haoys.user.service.SystemMonitorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 系统用户登录控制器测试类
 * 验证登录统计功能是否正常工作
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
public class SystemUserLoginControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SystemMonitorService systemMonitorService;

    /**
     * 测试用户名密码登录统计功能
     */
    @Test
    public void testLoginWithStatistics() throws Exception {
        // 准备测试数据
        SystemUserLoginParam loginParam = new SystemUserLoginParam();
        loginParam.setUsername("testuser");
        loginParam.setPassword("testpassword");
        loginParam.setTenantId("1");
        loginParam.setPlatformId("1");
        loginParam.setLoginSource("web");

        // 执行登录请求
        MvcResult result = mockMvc.perform(post("/admin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginParam))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"))
                .andExpect(status().isOk())
                .andReturn();

        // 验证响应
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("登录响应: " + responseContent);

        // 验证登录统计数据是否记录
        // 这里可以添加更多的验证逻辑，比如查询数据库验证记录是否正确
    }

    /**
     * 测试手机验证码登录统计功能
     */
    @Test
    public void testVerificationCodeLoginWithStatistics() throws Exception {
        // 准备测试数据
        UserVerificationCodeParam codeParam = new UserVerificationCodeParam();
        codeParam.setMobile("13800138000");
        codeParam.setCode("123456");
        codeParam.setTenantId("1");
        codeParam.setPlatformId("1");
        codeParam.setLoginSource("mobile");

        // 执行手机验证码登录请求
        MvcResult result = mockMvc.perform(post("/admin/verificationCodeLogin")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(codeParam))
                .header("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15"))
                .andExpect(status().isOk())
                .andReturn();

        // 验证响应
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("手机验证码登录响应: " + responseContent);

        // 验证登录统计数据是否记录
        // 这里可以添加更多的验证逻辑
    }

    /**
     * 测试登录失败统计功能
     */
    @Test
    public void testLoginFailureWithStatistics() throws Exception {
        // 准备错误的测试数据
        SystemUserLoginParam loginParam = new SystemUserLoginParam();
        loginParam.setUsername("nonexistentuser");
        loginParam.setPassword("wrongpassword");
        loginParam.setTenantId("1");
        loginParam.setPlatformId("1");
        loginParam.setLoginSource("web");

        // 执行登录请求（预期失败）
        MvcResult result = mockMvc.perform(post("/admin/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginParam))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"))
                .andExpect(status().isOk()) // 业务失败但HTTP状态码仍为200
                .andReturn();

        // 验证响应
        String responseContent = result.getResponse().getContentAsString();
        System.out.println("登录失败响应: " + responseContent);

        // 验证失败登录统计数据是否记录
        // 这里可以添加更多的验证逻辑
    }

    /**
     * 测试在线用户统计功能
     */
    @Test
    public void testOnlineUserStatistics() throws Exception {
        // 获取当前在线用户统计
        Map<String, Object> onlineStats = systemMonitorService.getRealTimeStatistics();
        System.out.println("实时统计数据: " + onlineStats);

        // 验证统计数据结构
        assert onlineStats.containsKey("onlineUsers");
        assert onlineStats.containsKey("todayVisits");
    }

    /**
     * 测试登录类型统计功能
     */
    @Test
    public void testLoginTypeStatistics() throws Exception {
        // 获取今日登录统计
        Map<String, Object> todayStats = systemMonitorService.getTodayLoginStatistics();
        System.out.println("今日登录统计: " + todayStats);

        // 获取登录类型统计
        List<Map<String, Object>> loginTypeStats = systemMonitorService.getLoginTypeStatistics("2025-01-16", "2025-01-16");
        System.out.println("登录类型统计: " + loginTypeStats);
    }
}
