package com.haoys.user.domain.cluster;

import com.haoys.user.common.config.ClusterConfig;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 服务实例模型
 * 参照Nacos的Instance设计
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class ServiceInstance {

    /**
     * 实例ID
     */
    private String instanceId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点类型
     */
    private ClusterConfig.NodeType nodeType;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 是否健康
     */
    private boolean healthy = true;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeat;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 实例状态
     */
    private InstanceStatus status = InstanceStatus.UP;

    /**
     * 心跳失败次数
     */
    private int failureCount = 0;

    /**
     * 环境标识
     */
    private String environment;

    /**
     * 实例状态枚举
     */
    public enum InstanceStatus {
        /**
         * 运行中
         */
        UP,
        /**
         * 已下线
         */
        DOWN,
        /**
         * 启动中
         */
        STARTING,
        /**
         * 未知状态
         */
        UNKNOWN,
        /**
         * 维护中
         */
        OUT_OF_SERVICE
    }

    /**
     * 检查实例是否在线
     */
    public boolean isOnline() {
        return enabled && healthy && status == InstanceStatus.UP;
    }

    /**
     * 检查实例是否过期
     */
    public boolean isExpired(int timeoutSeconds) {
        if (lastHeartbeat == null) {
            return true;
        }
        return lastHeartbeat.isBefore(LocalDateTime.now().minusSeconds(timeoutSeconds));
    }

    /**
     * 更新心跳时间
     */
    public void updateHeartbeat() {
        this.lastHeartbeat = LocalDateTime.now();
        this.failureCount = 0;
        if (this.status == InstanceStatus.DOWN) {
            this.status = InstanceStatus.UP;
        }
    }

    /**
     * 增加失败次数
     */
    public void incrementFailureCount() {
        this.failureCount++;
    }

    /**
     * 标记为下线
     */
    public void markAsDown() {
        this.status = InstanceStatus.DOWN;
        this.healthy = false;
    }

    /**
     * 标记为上线
     */
    public void markAsUp() {
        this.status = InstanceStatus.UP;
        this.healthy = true;
        this.failureCount = 0;
    }

    /**
     * 获取实例的唯一标识
     */
    public String getUniqueKey() {
        return String.format("%s:%s:%d", serviceName, ip, port);
    }

    /**
     * 获取实例的显示名称
     */
    public String getDisplayName() {
        if (nodeName != null && !nodeName.trim().isEmpty()) {
            return nodeName;
        }
        return String.format("%s-%s:%d", serviceName, ip, port);
    }
}
