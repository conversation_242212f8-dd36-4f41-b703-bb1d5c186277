package com.haoys.user.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * AI聊天请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiChatRequestDto", description = "AI聊天请求参数")
public class AiChatRequestDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "会话ID(新会话时可为空)")
    private String sessionId;
    
    @ApiModelProperty(value = "消息内容", required = true)
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 10000, message = "消息内容不能超过10000字符")
    private String content;
    
    @ApiModelProperty(value = "模型类型(qwen,kimi,chatgpt等)")
    private String modelType;
    
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "是否流式响应")
    private Boolean stream = false;
    
    @ApiModelProperty(value = "温度参数(0.0-2.0)")
    private Double temperature;
    
    @ApiModelProperty(value = "最大Token数")
    private Integer maxTokens;
    
    @ApiModelProperty(value = "系统提示词")
    private String systemPrompt;
    
    @ApiModelProperty(value = "会话标题")
    private String title;
    
    @ApiModelProperty(value = "文件信息列表")
    private List<FileInfo> files;
    
    @Data
    @ApiModel(value = "FileInfo", description = "文件信息")
    public static class FileInfo implements Serializable {
        
        @ApiModelProperty(value = "文件名")
        private String fileName;
        
        @ApiModelProperty(value = "文件路径")
        private String filePath;
        
        @ApiModelProperty(value = "文件类型")
        private String fileType;
        
        @ApiModelProperty(value = "文件大小")
        private Long fileSize;
        
        @ApiModelProperty(value = "MIME类型")
        private String mimeType;
        
        @ApiModelProperty(value = "文件内容(Base64编码)")
        private String fileContent;
    }
}
