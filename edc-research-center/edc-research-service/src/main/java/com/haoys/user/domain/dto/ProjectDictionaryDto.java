package com.haoys.user.domain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProjectDictionaryDto {

    @ApiModelProperty(value = "字典id")
    private String id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "字典英文名称")
    private String enName;

    @ApiModelProperty(value = "字典类型 1-项目表单字典 2-数据单位字典")
    private String dicType;

    @ApiModelProperty(value = "父级id-字典类型设置0，字典明细设置对应分类id")
    private String parentId = "0";

    @ApiModelProperty(value = "选项值")
    private String optionValue;

    @ApiModelProperty(value = "分值")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "计量单位")
    private String unitValue;

    @ApiModelProperty(value = "字典编码")
    private String code;

    @ApiModelProperty(value = "状态值 0-启用 1-停用")
    private String status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "描述")
    private String description;

}
