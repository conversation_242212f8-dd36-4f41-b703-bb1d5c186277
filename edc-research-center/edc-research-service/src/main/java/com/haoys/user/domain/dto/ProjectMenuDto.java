package com.haoys.user.domain.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 项目菜单dto
 *
 */
public class ProjectMenuDto implements Serializable {

    private static final long serialVersionUID = 6691598986199591850L;

    @NotNull(message = "项目ID不可以为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /** 菜单组 */
    private Long[] menuId;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long[] getMenuId() {
        return menuId;
    }

    public void setMenuId(Long[] menuId) {
        this.menuId = menuId;
    }
}
