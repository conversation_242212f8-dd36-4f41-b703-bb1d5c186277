package com.haoys.user.domain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ProjectOrgParam {

    @ApiModelProperty(value = "项目研究中心记录id")
    private Long id;

    @NotNull(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull(message = "中心ID不能为空")
    @ApiModelProperty(value = "中心id")
    private Long orgId;

    @ApiModelProperty(value = "是否更新企业中心所属地区")
    private Boolean updateSystemOrgAreaInfo = false;

    @ApiModelProperty(value = "项目中心编码")
    private String code;

    @ApiModelProperty(value = "中心负责人")
    private String officer;

    @ApiModelProperty(value = "扩展属性-地区属性等")
    private String expands;

    @ApiModelProperty(value = "省code")
    private Long provinceCode;

    @ApiModelProperty(value = "市code")
    private Long cityCode;

    @ApiModelProperty(value = "县code")
    private Long countyCode;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

}
