package com.haoys.user.domain.dto;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目成员用户dto
 */
@Data
public class ProjectUserParam extends BaseVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "所属中心id")
    private String orgId;

    @ApiModelProperty(value = "所属研究中心角色id")
    private String roleId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "数据启用状态 0-启用 1-停用")
    private String status;

    @ApiModelProperty(value = "激活状态0/1")
    private String activeStatus;

    @ApiModelProperty(value = "注册用户来源")
    private String userType;



}
