package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI文档解析记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiDocumentParse", description = "AI文档解析记录")
public class AiDocumentParse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "解析ID")
    private String parseId;
    
    @ApiModelProperty(value = "关联会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "用户名")
    private String userName;
    
    @ApiModelProperty(value = "文件名")
    private String fileName;
    
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    
    @ApiModelProperty(value = "文件大小(字节)")
    private Long fileSize;
    
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    
    @ApiModelProperty(value = "MIME类型")
    private String mimeType;
    
    @ApiModelProperty(value = "解析类型")
    private String parseType;
    
    @ApiModelProperty(value = "解析状态(0-处理中,1-成功,2-失败)")
    private Integer parseStatus;
    
    @ApiModelProperty(value = "解析结果")
    private String parseResult;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
    @ApiModelProperty(value = "使用Token数")
    private Integer tokensUsed;
    
    @ApiModelProperty(value = "解析成本")
    private BigDecimal parseCost;
    
    @ApiModelProperty(value = "解析耗时(毫秒)")
    private Integer parseTime;
    
    @ApiModelProperty(value = "使用的模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "使用的模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
