package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI模型配置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiModelConfig", description = "AI模型配置")
public class AiModelConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "API密钥")
    private String apiKey;
    
    @ApiModelProperty(value = "API地址")
    private String apiUrl;
    
    @ApiModelProperty(value = "最大Token数")
    private Integer maxTokens;
    
    @ApiModelProperty(value = "温度参数")
    private BigDecimal temperature;
    
    @ApiModelProperty(value = "Top-P参数")
    private BigDecimal topP;
    
    @ApiModelProperty(value = "频率惩罚")
    private BigDecimal frequencyPenalty;
    
    @ApiModelProperty(value = "存在惩罚")
    private BigDecimal presencePenalty;
    
    @ApiModelProperty(value = "系统提示词")
    private String systemPrompt;
    
    @ApiModelProperty(value = "输入Token单价")
    private BigDecimal tokenPriceInput;
    
    @ApiModelProperty(value = "输出Token单价")
    private BigDecimal tokenPriceOutput;
    
    @ApiModelProperty(value = "每分钟请求限制")
    private Integer rateLimitRpm;
    
    @ApiModelProperty(value = "每分钟Token限制")
    private Integer rateLimitTpm;
    
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;
    
    @ApiModelProperty(value = "优先级")
    private Integer priority;
    
    @ApiModelProperty(value = "模型描述")
    private String description;
    
    @ApiModelProperty(value = "创建者")
    private String createUser;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @ApiModelProperty(value = "更新者")
    private String updateUser;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
