package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * AI Token使用统计实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiTokenUsage", description = "AI Token使用统计")
public class AiTokenUsage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "用户名")
    private String userName;
    
    @ApiModelProperty(value = "模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "消息ID")
    private String messageId;
    
    @ApiModelProperty(value = "输入Token数")
    private Integer inputTokens;
    
    @ApiModelProperty(value = "输出Token数")
    private Integer outputTokens;
    
    @ApiModelProperty(value = "总Token数")
    private Integer totalTokens;
    
    @ApiModelProperty(value = "输入成本")
    private BigDecimal inputCost;
    
    @ApiModelProperty(value = "输出成本")
    private BigDecimal outputCost;
    
    @ApiModelProperty(value = "总成本")
    private BigDecimal totalCost;
    
    @ApiModelProperty(value = "使用日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date usageDate;
    
    @ApiModelProperty(value = "使用小时(0-23)")
    private Integer usageHour;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
