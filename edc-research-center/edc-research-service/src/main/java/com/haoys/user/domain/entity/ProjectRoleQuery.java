package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目角色表
 * project_role
 */
@Data
public class ProjectRoleQuery extends BaseEntity implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目角色id")
    private Long id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "角色名称不可以为空")
    private String name;

    @NotBlank(message = "英文名称code不可以为空")
    @ApiModelProperty(value = "英文名称code")
    private String enname;

    @NotNull(message = "项目ID不可以为空")
    @ApiModelProperty(value = "项目id")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long projectId;

    @ApiModelProperty(value = "所属中心id:如果是项目可以不传，如果是所属中心比传递")
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long orgId;

    @ApiModelProperty(value = "项目研究中心ID")
    private Long projectOrgId;

    @ApiModelProperty(value = "项目研究中心code")
    private String projectOrgCode;

    @ApiModelProperty(value = "启用状态：0->禁用；1->启用")
    private String status;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目角色模版id")
    private Long resourceRoleId;

    @ApiModelProperty(value = "项目模板0/1")
    private Boolean projectTemplate;

    @ApiModelProperty(value = "项目中心模板0/1")
    private Boolean orgTemplate;
    
    @ApiModelProperty(value = "全局标识-禁止编辑")
    private Boolean globalDefault;
    
    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @NotEmpty(message = "请选择角色对应的菜单权限")
    @ApiModelProperty(value = "请选择角色对应的菜单权限")
    private List<String> menuIds;

    @ApiModelProperty(value = "菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示")
    private boolean menuCheckStrictly;

    @ApiModelProperty(value = "是否系统默认角色 true/false")
    private Boolean systemDefault = false;

    @ApiModelProperty(value = "是否保存默认角色")
    private Boolean saveDefaultDefault = false;

    @ApiModelProperty(value = "是否显示全部角色 true/false")
    private Boolean showSelectAllOption = false;
    
    @ApiModelProperty(value = "创建者")
    private String createUser;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新者")
    private String updateUser;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
