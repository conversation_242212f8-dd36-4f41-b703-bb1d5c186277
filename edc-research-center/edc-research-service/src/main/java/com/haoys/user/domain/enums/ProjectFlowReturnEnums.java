package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 项目角色枚举
 */
public enum ProjectFlowReturnEnums implements BaseResultCode {
    E30003(30003,"请在项目配置中发布计划"),
    E60000(60000,"计划名称已被使用"),
    E60001(60001,"删除失败，计划下流程不为空，请清空后重试"),
    E60002(60002,"当前计划下流程为空，请添加后重试"),
    E60003(60003,"删除失败，流程下表单不为空，请清空后重试"),
    E60004(60004,"流程名称已被使用"),
    E60005(60005,"发布失败，只允许发布一个计划，请先撤销已发布的计划"),

    E60006(60006,"发布失败，流程下表单不能为空，请添加后重试"),
    ;

    private int code;

    private String message;

    ProjectFlowReturnEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
