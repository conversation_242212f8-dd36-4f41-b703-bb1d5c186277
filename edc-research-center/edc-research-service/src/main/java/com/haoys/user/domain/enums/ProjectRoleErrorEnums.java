package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 项目角色枚举
 */
public enum ProjectRoleErrorEnums implements BaseResultCode {
    E10101(10101,"角色名称已存在"),
    E10102(10102,"该角色已分配成员,不能删除");

    private int code;

    private String message;

    ProjectRoleErrorEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
