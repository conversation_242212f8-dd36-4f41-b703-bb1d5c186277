package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 项目角色枚举
 */
public enum ProjectRoleResultEnums implements BaseResultCode {
    E500(500,"角色创建失败"),
    D40002(40002,"删除失败，当前角色已授权"),
    S40003(40003,"角色禁用成功"),
    J40004(40004,"禁用失败，当前角色已被授权"),
    Q40005(40005,"用户启用成功"),
    B40006(40006,"角色名称已存在"),
    ;

    private int code;

    private String message;

    ProjectRoleResultEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
