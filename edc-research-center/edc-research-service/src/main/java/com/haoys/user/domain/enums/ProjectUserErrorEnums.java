package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 项目用户枚举
 */
public enum ProjectUserErrorEnums implements BaseResultCode {

    E10301(10301,"当前项目该手机账号已存在"),
    E10302(10302,"该项目成员已存在"),
    E10303(10303,"添加项目成员失败"),
    E10304(10304,"项目成员所属中心不能为空");


    private int code;

    private String message;

    ProjectUserErrorEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
