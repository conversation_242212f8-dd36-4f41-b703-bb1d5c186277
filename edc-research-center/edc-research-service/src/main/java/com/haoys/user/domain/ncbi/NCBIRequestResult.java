package com.haoys.user.domain.ncbi;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class NCBIRequestResult {
    
    private ESearchResult eSearchResult;
    
    @Data
    public static class ESearchResult {
        private float Count;
        private float RetMax;
        private float RetStart;
        private float queryKey;
        private String webEnv;
        //@JSO<PERSON>ield(name = "IdList")
        @JsonProperty("IdList")
        private List<IdList> IdList;
        private TranslationSet TranslationSet;
        private TranslationStack TranslationStack;
        private String queryTranslation;
    }
    
    @Data
    public static class TranslationStack {
        TermSet termSet;
        private String oP;
    }
    
    @Data
    public static class TermSet {
        private String term;
        private String field;
        private float count;
        private String explode;
    }
    
    @Data
    public static class TranslationSet {
        List<Translation> Translation;
    }
    
    @Data
    public static class Translation {
        private String From;
        private String To;
    }
    
    @Data
    public static class IdList {
        private String Id;
    }
}


