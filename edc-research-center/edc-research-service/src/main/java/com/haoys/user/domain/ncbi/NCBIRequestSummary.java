package com.haoys.user.domain.ncbi;


import lombok.Data;

import java.util.List;

@Data
public class NCBIRequestSummary {

/*<eSummaryResult>
<DocSum>
<Id>19008416</Id>
<Item Name="PubDate" Type="Date">2008 Dec 12</Item>
<Item Name="EPubDate" Type="Date">2008 Nov 13</Item>
<Item Name="Source" Type="String">Science</Item>
<Item Name="AuthorList" Type="List">
<Item Name="Author" Type="String">Varambally S</Item>
</Item>
<Item Name="LastAuthor" Type="String">Chinnaiyan AM</Item>
<Item Name="Title" Type="String">Genomic </Item>
<Item Name="Volume" Type="String">322</Item>
<Item Name="Issue" Type="String">5908</Item>
<Item Name="Pages" Type="String">1695-9</Item>
<Item Name="LangList" Type="List">
<Item Name="Lang" Type="String">English</Item>
</Item>
<Item Name="NlmUniqueID" Type="String">0404511</Item>
<Item Name="ISSN" Type="String">0036-8075</Item>
<Item Name="ESSN" Type="String">1095-9203</Item>
<Item Name="PubTypeList" Type="List">
<Item Name="PubType" Type="String">Journal Article</Item>
</Item>
<Item Name="RecordStatus" Type="String">PubMed - indexed for MEDLINE</Item>
<Item Name="PubStatus" Type="String">ppublish+epublish</Item>
<Item Name="ArticleIds" Type="List">
<Item Name="pubmed" Type="String">19008416</Item>
<Item Name="mid" Type="String">NIHMS104414</Item>
<Item Name="pmc" Type="String">PMC2684823</Item>
<Item Name="pmcid" Type="String">pmc-id: PMC2684823;manuscript-id: NIHMS104414;</Item>
<Item Name="doi" Type="String">10.1126/science.1165395</Item>
<Item Name="pii" Type="String">1165395</Item>
</Item>
<Item Name="DOI" Type="String">10.1126/science.1165395</Item>
<Item Name="History" Type="List">
<Item Name="entrez" Type="Date">2008/11/15 09:00</Item>
<Item Name="pubmed" Type="Date">2008/11/15 09:00</Item>
<Item Name="medline" Type="Date">2009/01/06 09:00</Item>
<Item Name="pmc-release" Type="Date">2009/06/12 00:00</Item>
</Item>
<Item Name="References" Type="List"/>
<Item Name="HasAbstract" Type="Integer">1</Item>
<Item Name="PmcRefCount" Type="Integer">29</Item>

<Item Name="FullJournalName" Type="String">Science (New York, N.Y.)</Item>
<Item Name="ELocationID" Type="String">doi: 10.1126/science.1165395</Item>
<Item Name="SO" Type="String">2008 Dec 12;322(5908):1695-9</Item>

</DocSum>
</eSummaryResult>*/
    
    
    private EsummaryResult eSummaryResult;
    
    
    
    @Data
    public static class EsummaryResult {
        private String ERROR;
        private DocSum DocSum;
    }
    
    @Data
    public static class DocSum {
        private String Id;
        private Item Item;
    }
    
    @Data
    public static class Item {
        private String pubDate;
        private String ePubDate;
        private String source;
        private List<String> authorList;
        private String lastAuthor;
        private String title;
        private String volume;
        private String issue;
        private String pages;
        private List<String> langList;
        private String nlmUniqueID;
        private String issn;
        private String essn;
        private String pubTypeList;
        private String recordStatus;
        private String pubStatus;
        private List<String> articleIds;
        private String doi;
        private String pii;
        private List<String> history;
        private List<String> references;
        private Integer hasAbstract;
        private Integer pmcRefCount;
        private String fullJournalName;
        private String eLocationID;
        private String so;
    }
    
}
