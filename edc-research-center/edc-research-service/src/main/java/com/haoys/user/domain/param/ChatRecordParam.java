package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ChatRecordParam {
    
    @NotEmpty(message = "会话主题不能为空")
    private String topic;
    
    @ApiModelProperty(value = "模型提示词等系列条件")
    private String promptValue;
    
    @NotEmpty(message = "使用模型类型不能为空")
    private String modelType;
    
    //@JsonProperty("Query")
    private String query;
    //@JsonProperty("Query_time")
    private String query_time;
    private String instruction_str;
    private String intention_category;
    private String raw_output;
    private String viewUrl;
}
