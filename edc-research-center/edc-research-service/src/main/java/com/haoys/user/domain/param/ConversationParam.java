package com.haoys.user.domain.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class ConversationParam {

    @NotEmpty(message = "会话主题不能为空")
    private String topic;

    private List<Conversation> conversation;

    @Data
    public static class Conversation {
        private String role = "user";
        private String content;
    }
}

