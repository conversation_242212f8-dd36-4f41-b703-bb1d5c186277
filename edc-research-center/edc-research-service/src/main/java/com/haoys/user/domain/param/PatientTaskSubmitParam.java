package com.haoys.user.domain.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class PatientTaskSubmitParam {

    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull
    @ApiModelProperty(value = "方案id")
    private Long planId;

    @NotNull
    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;


    @ApiModelProperty(value = "任务日期(不指定则使用当天日期) 格式yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDate;

    @ApiModelProperty(value = "任务提交时间 格式yyyy-MM-dd HH:mm:ss")
    private Date taskSubmitTime;

    @NotNull
    @ApiModelProperty(value = "患者id")
    private Long testeeId;

    @ApiModelProperty(value = "任务类型id")
    private String taskType;

    @ApiModelProperty(value = "任务触发频率 每天、每周、每月、每年")
    private String taskRate;

    @ApiModelProperty(value = "每日推送频率")
    private Integer pushRate;

    @ApiModelProperty(value = "表单录入完成情况 1-待录入、2-已录入、3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "提交人")
    private String createUserId;

    @ApiModelProperty(value = "任务变量详情记录")
    List<PatientTaskDetail> dataList = new ArrayList<>();

    @Data
    public static class PatientTaskDetail{

        @ApiModelProperty(value = "已提交记录id-编辑时设置")
        private Long formResultId;

        @ApiModelProperty(value = "访视id")
        private Long visitId;

        @ApiModelProperty(value = "表单id")
        private Long formId;

        @ApiModelProperty(value = "变量id")
        private Long formDetailId;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段key")
        private String key;

        @ApiModelProperty(value = "提交结果")
        private String result;
    }


}


