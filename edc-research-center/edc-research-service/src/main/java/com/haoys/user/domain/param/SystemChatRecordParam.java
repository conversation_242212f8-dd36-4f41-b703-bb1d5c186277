package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class SystemChatRecordParam {

    @NotEmpty(message = "会话主题不能为空")
    private String topic;
    @NotEmpty(message = "使用模型类型不能为空")
    private String modelType;
    @ApiModelProperty(value = "模型提示词等系列条件")
    private String promptValue;
    private List<ConversationParam.Conversation> conversation;

}
