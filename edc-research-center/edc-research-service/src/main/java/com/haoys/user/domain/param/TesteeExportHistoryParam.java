package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参与者数据导出历史搜索添加
 */
@Data
public class TesteeExportHistoryParam {

    @ApiModelProperty(value = "第几页")
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "导出类型：1.excel 2.word")
    private Integer exportType;
    @ApiModelProperty(value = "所属中心,支持多个，逗号隔开")
    private String orgIds;
    @ApiModelProperty(value = "导出开始时间")
    private String startDate;
    @ApiModelProperty(value = "导出结束时间")
    private String endDate;
    @ApiModelProperty(value = "任务名称")
    private String taskName;

}
