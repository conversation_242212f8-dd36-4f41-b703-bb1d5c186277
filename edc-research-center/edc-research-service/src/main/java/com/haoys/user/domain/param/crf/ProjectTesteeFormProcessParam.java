package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProjectTesteeFormProcessParam implements Serializable {

    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @NotNull
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @NotNull
    @ApiModelProperty(value = "表单id")
    private Long formId;
    
    @ApiModelProperty(value = "扩展表单id")
    private Long formExpandId;

    @NotNull
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "表单总计录入状态 1-未录入 2-录入中 3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "变量总计")
    private int variableCount = 0;

    @ApiModelProperty(value = "已录入变量总计")
    private int variableComplateCount = 0;
    
    @ApiModelProperty(value = "数据提交状态-1/0")
    private String status;

    @ApiModelProperty(value = "操作人")
    private String operator;


}
