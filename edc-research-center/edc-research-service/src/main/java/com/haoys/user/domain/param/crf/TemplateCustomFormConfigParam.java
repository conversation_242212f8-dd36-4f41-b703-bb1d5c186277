package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TemplateCustomFormConfigParam implements Serializable {

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "模板code")
    private String templateCode;

    @ApiModelProperty(value = "模板描述")
    private String description;

    @ApiModelProperty(value = "是否私有")
    private Boolean ifPrivate;

    @ApiModelProperty(value = "模版封面")
    private String logoUrl;

    // configType 1-全局模板 2-我的模板,需设置项目projectId 3-系统模版
    @ApiModelProperty(value = "模板类型 1-全局模板 2-项目模板 3-系统模版，如果是另存为模板必须设置此参数")
    private String configType;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @ApiModelProperty(value = "原始表单项id")
    private String copyFormId;

    @ApiModelProperty(value = "表单项分类id-必须设置----需求变更无需设置")
    private String groupName;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "表单code")
    private String formCode;

    @ApiModelProperty(value = "全局配置-定义表单是否为表单集合还是表格 [{'type':'form'}] type类型-form表单 table表格")
    private String formConfig;

    @ApiModelProperty(value = "表单类型")
    private String formType;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "是否上传原始资料")
    private Boolean uploadResourceFile = false;

    @ApiModelProperty(value = "是否开启患者端")
    private Boolean openEpro = false;

    @ApiModelProperty(value = "表单变量列表")
    private List<TemplateFormDetailParam> templateFormDetailParamList = new ArrayList<>();

    @ApiModelProperty(value = "操作人")
    private String createUser;
}
