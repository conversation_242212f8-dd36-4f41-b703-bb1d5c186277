package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class TemplateFormConfigBaseParam {

    @ApiModelProperty(value = "模版id")
    private Long templateId;

    @ApiModelProperty(value = "模版名称")
    private String templateName;

    @ApiModelProperty(value = "模版状态0/1")
    private String templateStatus;

    @ApiModelProperty(value = "是否创建模版")
    private Boolean createTemplate = false;

    @ApiModelProperty(value = "模版类型")
    private String configType;

    //@NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "表单项id-编辑表单时设置")
    private Long formId;

    @ApiModelProperty(value = "表单分类id-必须设置-->需求变更 可选项")
    private String groupName;

    @NotEmpty(message = "表单名称不能为空")
    @ApiModelProperty(value = "表单名称")
    private String formName;

    @NotEmpty(message = "表单code不能为空")
    @ApiModelProperty(value = "表单code")
    private String formCode;

    @ApiModelProperty(value = "表单配置信息")
    private String formConfig;

    @NotEmpty(message = "表单类型不能为空")
    @ApiModelProperty(value = "表单类型")
    private String formType;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "是否上传原始资料")
    private Boolean uploadResourceFile=true;

    @ApiModelProperty(value = "是否开启患者端")
    private Boolean openEpro = false;

    @ApiModelProperty(value = "是否全局表单")
    private Boolean globalScope = false;

    @ApiModelProperty(value = "是否患者专用表单")
    private Boolean testeeForm;

    @ApiModelProperty(value = "是否不良事件等定制表单")
    private Boolean customForm;
    
    @ApiModelProperty(value = "是否入组表单")
    private Boolean joinGroup = false;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @ApiModelProperty(value = "表单排序")
    private Integer sort;

    @ApiModelProperty(value = "操作人")
    private String createUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;
}
