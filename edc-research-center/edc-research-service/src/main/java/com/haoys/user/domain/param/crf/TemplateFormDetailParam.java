package com.haoys.user.domain.param.crf;

import com.haoys.user.common.constants.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@Data
public class TemplateFormDetailParam implements Serializable {


    @ApiModelProperty(value = "表单变量id -编辑数据时设置")
    private Long id;

    @ApiModelProperty(value = "模版id")
    private Long templateId;

    //@NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull(message = "表单id不能为空")
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @ApiModelProperty(value = "字典变量是否自动创建表单")
    private Boolean autoCreateForm = false;

    private Boolean createFormTemplate = false;

    @ApiModelProperty(value = "模版类型 1-全局模版 2-我的项目模板 3-系统模版")
    private String configType = Constants.PROJECT_TEMPLATE_CONFIG_TYPE_03;

    @NotEmpty(message = "控件类型不能为空")
    @ApiModelProperty(value = "控件类型")
    private String type;

    //@NotEmpty(message = "字段名称不能为空")
    @ApiModelProperty(value = "表单项变量名称")
    private String label;

    @ApiModelProperty(value = "字段英文名称")
    private String langValue;

    @ApiModelProperty(value = "字段标题")
    private String title;

    @ApiModelProperty(value = "字段内容")
    private String content;

    @ApiModelProperty(value = "组件是否隐藏 1-隐藏 0-显示")
    private Boolean hidden = false;

    @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
    private String requireType;

    @ApiModelProperty(value = "组件是否必填")
    private Boolean required = false;

    @ApiModelProperty(value = "输入提示文字")
    private String placeholder;

    @ApiModelProperty(value = "变量图标")
    private String icon;

    //@NotEmpty(message = "字段编码不能为空")
    @ApiModelProperty(value = "数据库字段code")
    private String fieldName;

    @ApiModelProperty(value = "绑定属性")
    private String model;

    @ApiModelProperty(value = "验证规则 JSON字符串格式")
    private String rules;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "是否显示字段名称0/1")
    private Boolean showTitle = true;

    @ApiModelProperty(value = "是否显示内容0/1")
    private Boolean showContent = true;

    //@NotEmpty(message = "扩展属性不能为空")
    @ApiModelProperty(value = "扩展属性字段")
    private String expand;

    @ApiModelProperty(value = "数据状态 0-有效 1-无效")
    private String status = "0";

    @ApiModelProperty(value = "下拉框数据")
    private String options;

    @ApiModelProperty(value = "字典来源(1-系统字典2-项目字典)")
    private String dicResource;

    @ApiModelProperty(value = "引用字典id")
    private String refDicId;

    @ApiModelProperty(value = "字典默认值")
    private String defaultDicValue;

    @ApiModelProperty(value = "组件默认值")
    private String defaultValue = "";

    @ApiModelProperty(value = "是否为定制表格")
    private Boolean customTable = false;

    @ApiModelProperty(value = "量表打分设置")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "计量单位")
    private String unitValue = "";

    @ApiModelProperty(value = "字段样式")
    private String styleData;

    @ApiModelProperty(value = "控件尺寸")
    private String panelSize;

    @ApiModelProperty(value = "参与者自定义标题")
    private Boolean customTestee = false;

    @ApiModelProperty(value = "字段组-模版定义")
    private List<TemplateFormDetailParam> groupTemplateBaseVariableList = new ArrayList<>();

    @Valid
    @ApiModelProperty(value = "表格Header动态字段描述")
    private List<TemplateTableConfigParam.TableHeadRowInfo> tableRowList = new ArrayList<>();

    @ApiModelProperty(value = "变量分组id")
    private Long groupId;

    @ApiModelProperty(value = "变量分组描述")
    private String groupInfo;

    @ApiModelProperty(value = "字段版本号")
    private String versionInfo;

    @ApiModelProperty(value = "扩展属性1")
    private String extData1;

    @ApiModelProperty(value = "扩展属性2")
    private String extData2;

    @ApiModelProperty(value = "扩展属性3")
    private String extData3;

    @ApiModelProperty(value = "扩展属性4")
    private String extData4;

    @ApiModelProperty(value = "扩展属性5")
    private String extData5;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;



}
