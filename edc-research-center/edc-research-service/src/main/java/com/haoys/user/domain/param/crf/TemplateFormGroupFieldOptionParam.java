package com.haoys.user.domain.param.crf;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TemplateFormGroupFieldOptionParam implements Serializable {


    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "父级id，新增选项值必须设置")
    private String parentId = "0";

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "分值")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "计量单位")
    private String unitValue;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "数据状态 0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

}
