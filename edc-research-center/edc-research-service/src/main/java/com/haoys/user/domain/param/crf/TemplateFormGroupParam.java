package com.haoys.user.domain.param.crf;


import com.haoys.user.model.TemplateFormGroupVariable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 参与者创建组数据
 */

@Data
public class TemplateFormGroupParam {

    /**
     * 参与者id
     */
    @ApiModelProperty(value = "参与者id",required = true)
    private Long testeeId;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id",required = true)
    private Long projectId;

    @ApiModelProperty(value = "方案Id",required = true)
    private Long planId;

    /**
     * 访视Id
     */
    @ApiModelProperty(value = "访视Id",required = true)
    private Long visitId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id",required = true)
    private Long formId;

    /**
     * 普通表单列
     */
    @ApiModelProperty(value = "普通表单列")
    List<TemplateFormGroupVariable> variableList;
}
