package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateFormLogicParam implements Serializable {

    @ApiModelProperty(value = "显示条件主记录id-编辑数据设置")
    private Long id;

    @ApiModelProperty(value = "模板id-可选项 不设置")
    private Long templateId;

    @NotNull(message = "访视id不能为空")
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @NotNull(message = "表单项id不能为空")
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @NotNull(message = "表单项详情id不能为空")
    @ApiModelProperty(value = "表单项详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "标签名称")
    private String label;

    @ApiModelProperty(value = "选项内容")
    private Object optionList;

    @ApiModelProperty(value = "条件选项 1-全部 2-任一条件")
    private String expressionId;

    @ApiModelProperty(value = "操作人")
    private String createUser;

    @ApiModelProperty(value = "扩展字段")
    private Object extands;

    @ApiModelProperty(value = "废弃-待使用")
    private Object conditionList;

    @Valid
    @ApiModelProperty(value = "逻辑条件集合")
    private List<TemplateFormLogicDataParam> dataList = new ArrayList<>();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TemplateFormLogicDataParam implements Serializable {

        @ApiModelProperty(value = "逻辑显示条件记录id-编辑时设置此参数")
        private Long id;

        @ApiModelProperty(value = "显示条件id")
        private Long logicId;

        @ApiModelProperty(value = "模板id")
        private Long templateId;

        @NotNull(message = "引用访视id不能为空")
        @ApiModelProperty(value = "引用访视id")
        private Long targetVisitId;

        @NotNull(message = "引用表单id不能为空")
        @ApiModelProperty(value = "引用表单id")
        private Long targetFormId;

        @NotNull(message = "引用变量id不能为空")
        @ApiModelProperty(value = "引用变量id")
        private Long targetDetailId;

        @NotBlank(message = "条件表达式不能为空")
        @ApiModelProperty(value = "条件表达式 示例{\"expression\": \"eq\", \"optionValue\": 1}\"")
        private String conditionValue;

        @ApiModelProperty(value = "数据状态")
        private String status;

        @ApiModelProperty(value = "扩展字段")
        private String extands;

        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @ApiModelProperty(value = "修改时间")
        private Date updateTime;

        @ApiModelProperty(value = "创建人")
        private String createUserId;

        @ApiModelProperty(value = "修改人")
        private String updateUserId;
    }


}
