package com.haoys.user.domain.param.crf;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TemplateTableConfigParam implements Serializable {

    @ApiModelProperty(value = "模板id-不是设定模板请勿设置此参数")
    private Long templateId;

    private Boolean createFormTemplate = false;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @NotNull(message = "表格id不能为空")
    @ApiModelProperty(value = "表单表格id")
    private Long formDetailId;

    @ApiModelProperty(value = "是否为定制表格")
    private Boolean customTable = false;

    @Valid
    @ApiModelProperty(value = "表格行动态字段描述")
    public List<TableHeadRowInfo> tableRowList = new ArrayList<>();

    @ApiModelProperty(value = "操作人id")
    private String createUserId;

    @Data
    public static class TableHeadRowInfo {

        @ApiModelProperty(value = "主键id-编辑Head行记录时设置")
        private Long id;

        //@NotBlank(message = "数据库字段code不能为空")
        @ApiModelProperty(value = "数据库字段code")
        private String fieldName;

        @ApiModelProperty(value = "绑定属性")
        private String model;

        @NotBlank(message = "字段类型不能为空")
        @ApiModelProperty(value = "字段类型 input radio date...")
        private String type;

        @NotBlank(message = "字段名称不能为空")
        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段标题")
        private String title;

        @ApiModelProperty(value = "字段内容")
        private String content;

        @ApiModelProperty(value = "字段提示语")
        private String placeholder;

        @ApiModelProperty(value = "控件尺寸")
        private String panelSize;

        @ApiModelProperty(value = "排序")
        private Integer sort;

        @ApiModelProperty(value = "是否隐藏，0-显示 1-隐藏")
        private Boolean hidden;

        @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
        private String requireType;

        @ApiModelProperty(value = "是否必填项0/1")
        private Boolean required;

        @ApiModelProperty(value = "是否显示字段名称0/1")
        private Boolean showTitle;

        @ApiModelProperty(value = "是否显示内容0/1")
        private Boolean showContent;

        @ApiModelProperty(value = "默认值")
        private String defaultValue = "";

        @ApiModelProperty(value = "扩展属性字段")
        private String expand;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;

        @ApiModelProperty(value = "字典来源(1-系统字典2-项目字典)")
        private String dicResource;

        @ApiModelProperty(value = "引用字典id")
        private String refDicId;

        @ApiModelProperty(value = "字典默认值")
        private String defaultDicValue;

        @ApiModelProperty(value = "数据选项")
        private String options;

        @ApiModelProperty(value = "扩展字段1")
        private String extData1;

        @ApiModelProperty(value = "扩展字段2")
        private String extData2;

        @ApiModelProperty(value = "扩展字段3")
        private String extData3;

        @ApiModelProperty(value = "扩展字段4")
        private String extData4;

        @ApiModelProperty(value = "所属变量id")
        private Long pointVariableId;

        @ApiModelProperty(value = "是否启用关联属性")
        private Boolean enableAssociate;

        @ApiModelProperty(value = "条件表达式")
        private String conditionExpression;

        @ApiModelProperty(value = "企业租户id")
        private String tenantId;

        @ApiModelProperty(value = "平台id")
        private String platformId;
    }


}
