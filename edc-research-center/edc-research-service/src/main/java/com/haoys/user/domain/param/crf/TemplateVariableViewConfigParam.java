package com.haoys.user.domain.param.crf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TemplateVariableViewConfigParam implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "表单id")
    private Long baseFormId;

    @ApiModelProperty(value = "表单变量id")
    private Long baseFormDetailId;

    @ApiModelProperty(value = "表格变量id")
    private Long baseFormTableId;

    @ApiModelProperty(value = "变量名称")
    private String baseVariableName;

    private Boolean enableTable = false;

    @ApiModelProperty(value = "选项名称")
    private String optionName;

    @ApiModelProperty(value = "选项值")
    private String optionValue;

    private List<TemplateVariableViewResultConfigParam> dataList = new ArrayList<>();

    @ApiModelProperty(value = "设置当前变量是否存在选项联动标识")
    private Boolean enableViewConfig = false;

    @ApiModelProperty(value = "设置当前变量默认选项联动")
    private Boolean defaultViewConfig = false;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "扩展字段")
    private String extands;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @Data
    public static class TemplateVariableViewResultConfigParam {

        @ApiModelProperty(value = "表单项id")
        private Long formId;

        @ApiModelProperty(value = "表单/表格变量id")
        private Long formDetailId;

        @ApiModelProperty(value = "表格列变量id")
        private Long formTableId;

        @ApiModelProperty(value = "字段组id")
        private Long groupId;

        @ApiModelProperty(value = "变量名称")
        private String label;

        @ApiModelProperty(value = "是否显示 1-显示 0-不显示")
        private String viewConfigResult;

    }

}