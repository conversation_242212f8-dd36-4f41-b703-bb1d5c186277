package com.haoys.user.domain.param.dict;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectDictParam extends BaseVo {

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "字典状态 0:正常 ，1:停用")
    private String status;

    @ApiModelProperty(value = "父id")
    private String parentId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "字典类型 1-项目表单字典 2-数据单位字典")
    private String dicType;
}
