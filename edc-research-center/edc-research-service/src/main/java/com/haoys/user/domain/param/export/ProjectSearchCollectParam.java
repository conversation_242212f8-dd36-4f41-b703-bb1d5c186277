package com.haoys.user.domain.param.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectSearchCollectParam implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "专病数据库id")
    private String dataBaseId;

    @ApiModelProperty(value = "专病库类型 RDR或者专病库编号")
    private String diseaseType;

    @ApiModelProperty(value = "系统使用类型-1搜索历史 2-搜藏条件")
    private String systemUseType;

    @ApiModelProperty(value = "专病库类型 1-全文检索 2-条件检索 3-高级检索")
    private String searchType;

    @ApiModelProperty(value = "设置收藏条件")
    private String conditionGroup;

    @ApiModelProperty(value = "收藏条件描述")
    private String description;

}
