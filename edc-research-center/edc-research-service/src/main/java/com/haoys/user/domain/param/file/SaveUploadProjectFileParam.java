package com.haoys.user.domain.param.file;

import lombok.Data;

/**
 * 保存上传项目文件参数类
 * 用于封装saveUploadProjectFile方法的所有参数
 * 
 * <AUTHOR>
 */
@Data
public class SaveUploadProjectFileParam {
    
    private String createUserId;
    private String projectId;
    private String visitId;
    private String formId;
    private String resourceId;
    private String tableId;
    private String rowNumber;
    private String testeeId;
    private String collect;
    private String taskDate;
    private String openOCR;
    private String batchUpload;
    private String medicalType;
    private String templateId;
    private String prePageNo;
    private String groupId;
    private String groupName;
    private String imageType;
    private String extendStruct;
    private String generalAccurate;
    private String mergeImage;
    private String mergeMethod;
    private String ifMontage;
    private String needMergeFileParam;
    private String batchOpenOcr;
    
    /**
     * 构建器模式，方便创建参数对象
     */
    public static SaveUploadProjectFileParam builder() {
        return new SaveUploadProjectFileParam();
    }
    
    public SaveUploadProjectFileParam createUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }
    
    public SaveUploadProjectFileParam projectId(String projectId) {
        this.projectId = projectId;
        return this;
    }
    
    public SaveUploadProjectFileParam visitId(String visitId) {
        this.visitId = visitId;
        return this;
    }
    
    public SaveUploadProjectFileParam formId(String formId) {
        this.formId = formId;
        return this;
    }
    
    public SaveUploadProjectFileParam resourceId(String resourceId) {
        this.resourceId = resourceId;
        return this;
    }
    
    public SaveUploadProjectFileParam tableId(String tableId) {
        this.tableId = tableId;
        return this;
    }
    
    public SaveUploadProjectFileParam rowNumber(String rowNumber) {
        this.rowNumber = rowNumber;
        return this;
    }
    
    public SaveUploadProjectFileParam testeeId(String testeeId) {
        this.testeeId = testeeId;
        return this;
    }
    
    public SaveUploadProjectFileParam collect(String collect) {
        this.collect = collect;
        return this;
    }
    
    public SaveUploadProjectFileParam taskDate(String taskDate) {
        this.taskDate = taskDate;
        return this;
    }
    
    public SaveUploadProjectFileParam openOCR(String openOCR) {
        this.openOCR = openOCR;
        return this;
    }
    
    public SaveUploadProjectFileParam batchUpload(String batchUpload) {
        this.batchUpload = batchUpload;
        return this;
    }
    
    public SaveUploadProjectFileParam medicalType(String medicalType) {
        this.medicalType = medicalType;
        return this;
    }
    
    public SaveUploadProjectFileParam templateId(String templateId) {
        this.templateId = templateId;
        return this;
    }
    
    public SaveUploadProjectFileParam prePageNo(String prePageNo) {
        this.prePageNo = prePageNo;
        return this;
    }
    
    public SaveUploadProjectFileParam groupId(String groupId) {
        this.groupId = groupId;
        return this;
    }
    
    public SaveUploadProjectFileParam groupName(String groupName) {
        this.groupName = groupName;
        return this;
    }
    
    public SaveUploadProjectFileParam imageType(String imageType) {
        this.imageType = imageType;
        return this;
    }
    
    public SaveUploadProjectFileParam extendStruct(String extendStruct) {
        this.extendStruct = extendStruct;
        return this;
    }
    
    public SaveUploadProjectFileParam generalAccurate(String generalAccurate) {
        this.generalAccurate = generalAccurate;
        return this;
    }
    
    public SaveUploadProjectFileParam mergeImage(String mergeImage) {
        this.mergeImage = mergeImage;
        return this;
    }
    
    public SaveUploadProjectFileParam mergeMethod(String mergeMethod) {
        this.mergeMethod = mergeMethod;
        return this;
    }
    
    public SaveUploadProjectFileParam ifMontage(String ifMontage) {
        this.ifMontage = ifMontage;
        return this;
    }
    
    public SaveUploadProjectFileParam needMergeFileParam(String needMergeFileParam) {
        this.needMergeFileParam = needMergeFileParam;
        return this;
    }
    
    public SaveUploadProjectFileParam batchOpenOcr(String batchOpenOcr) {
        this.batchOpenOcr = batchOpenOcr;
        return this;
    }
}
