package com.haoys.user.domain.param.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class FlowFormExpandParam {
    
    @ApiModelProperty(value = "序号")
    private Integer xh;
    
    @NotNull(message = "流程表单中的formSetId设置不能为空")
    @ApiModelProperty(value = "原始访视流程表单的id")
    private Long ownerBaseId;
    
    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    
    @ApiModelProperty(value = "计划id")
    private Long planId;
    
    @NotNull
    @ApiModelProperty(value = "流程id")
    private Long visitId;
    
    @NotNull
    @ApiModelProperty(value = "表单id")
    private Long formId;
    
    @NotNull
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;
    
    @ApiModelProperty(value = "数据状态0/1")
    private String status;
    
    @NotNull
    @ApiModelProperty(value = "是否删除扩展表单 删除-true")
    private Boolean removeFormExpand = false;
    
}
