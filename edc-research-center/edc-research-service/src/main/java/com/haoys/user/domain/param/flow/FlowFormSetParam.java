package com.haoys.user.domain.param.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FlowFormSetParam {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Long planId;
    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private Long formId;

    /**
     * 流程id集合
     */
    @ApiModelProperty(value = "流程id")
    private Long flowId;

    /**
     * 流程id集合
     */
    @ApiModelProperty(value = "是否选中：选中：true ,取消：false")
    private Boolean isCheck;


    @ApiModelProperty(value = "流程id集合")
    private List<Long> flowIds;


}
