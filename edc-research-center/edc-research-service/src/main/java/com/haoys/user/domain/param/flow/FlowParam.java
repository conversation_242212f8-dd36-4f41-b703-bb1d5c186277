package com.haoys.user.domain.param.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FlowParam {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @ApiModelProperty(value = "计划id")
    @NotNull(message = "计划id不能为空")
    private Long planId;

    @ApiModelProperty(value = "类型")
    private String visitType;

    @ApiModelProperty(value = "归属阶段")
    private String ownerPeroid;

    @NotBlank(message = "流程名称不能为空")
    @ApiModelProperty(value = "流程名称")
    private String visitName;

}
