package com.haoys.user.domain.param.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统监控查询参数
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class SystemMonitorParam {

    /**
     * 访问日志查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "访问日志查询参数")
    public static class AccessLogQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "用户ID")
        private String userId;

        @ApiModelProperty(value = "用户名")
        private String userName;

        @ApiModelProperty(value = "请求IP")
        private String requestIp;

        @ApiModelProperty(value = "请求URL")
        private String requestUrl;

        @ApiModelProperty(value = "请求方法")
        private String requestMethod;

        @ApiModelProperty(value = "状态码")
        private String statusCode;

        @ApiModelProperty(value = "访问类型")
        private String accessType;

        @ApiModelProperty(value = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;

        @ApiModelProperty(value = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;

        @ApiModelProperty(value = "页码", example = "1")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        @Min(value = 1, message = "每页大小不能小于1")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer pageSize = 20;

        @ApiModelProperty(value = "排序字段")
        private String orderBy = "access_time";

        @ApiModelProperty(value = "排序方向(ASC/DESC)")
        private String orderDirection = "DESC";
    }

    /**
     * 在线用户查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "在线用户查询参数")
    public static class OnlineUserQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "用户ID")
        private String userId;

        @ApiModelProperty(value = "用户名")
        private String userName;

        @ApiModelProperty(value = "登录IP")
        private String loginIp;

        @ApiModelProperty(value = "状态")
        private String status;

        @ApiModelProperty(value = "设备类型")
        private String deviceType;

        @ApiModelProperty(value = "页码", example = "1")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        @Min(value = 1, message = "每页大小不能小于1")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer pageSize = 20;

        @ApiModelProperty(value = "排序字段")
        private String orderBy = "last_access_time";

        @ApiModelProperty(value = "排序方向(ASC/DESC)")
        private String orderDirection = "DESC";
    }

    /**
     * 异常日志查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "异常日志查询参数")
    public static class ExceptionLogQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "用户ID")
        private String userId;

        @ApiModelProperty(value = "用户名")
        private String userName;

        @ApiModelProperty(value = "请求IP")
        private String requestIp;

        @ApiModelProperty(value = "请求URL")
        private String requestUrl;

        @ApiModelProperty(value = "异常类型")
        private String exceptionType;

        @ApiModelProperty(value = "异常消息关键字")
        private String exceptionMessage;

        @ApiModelProperty(value = "日志级别")
        private String logLevel;

        @ApiModelProperty(value = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;

        @ApiModelProperty(value = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;

        @ApiModelProperty(value = "页码", example = "1")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        @Min(value = 1, message = "每页大小不能小于1")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer pageSize = 20;

        @ApiModelProperty(value = "排序字段")
        private String orderBy = "create_time";

        @ApiModelProperty(value = "排序方向(ASC/DESC)")
        private String orderDirection = "DESC";
    }

    /**
     * 统计查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "统计查询参数")
    public static class StatisticsQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "开始日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date startDate;

        @ApiModelProperty(value = "结束日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date endDate;

        @ApiModelProperty(value = "统计类型(day/week/month)")
        private String statType = "day";

        @ApiModelProperty(value = "页码", example = "1")
        @Min(value = 1, message = "页码不能小于1")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        @Min(value = 1, message = "每页大小不能小于1")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer pageSize = 20;
    }
}
