package com.haoys.user.domain.param.participant;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectParticipantViewConfigParam {

    @ApiModelProperty(value = "参与者id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "性别 男、女")
    private String gender;

    @ApiModelProperty(value = "主管医生")
    private String ownerDoctorId;

    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @ApiModelProperty(value = "身份证号码")
    private String idcard;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;

    @ApiModelProperty(value = "知情日期")
    private Date informedDate;

    @ApiModelProperty(value = "所属表单")
    private Long resourceFormId;

    @ApiModelProperty(value = "参与者自定义字段")
    private List<FormVariableConfigVo> formVariableConfigArrayList = new ArrayList<>();

    @ApiModelProperty(value = "是否为医生自建病历 1-医生创建 0-患者端创建")
    private Boolean selfRecord;

    @ApiModelProperty(value = "项目绑定状态 1-绑定 0-未绑定")
    private Boolean bindResult;

    @ApiModelProperty(value = "患者绑定时间")
    private Date bindTime;

    @ApiModelProperty(value = "参与者是否需要审核 1-是 0-否")
    private Boolean reviewFlag;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "审核人")
    private String reviewUserId;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "项目患者来源 1-医生端创建 2-患者自建 3-其他来源")
    private String bindResource;

    @ApiModelProperty(value = "参与者研究状态 参照字典说明")
    private String researchStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @Data
    public static class FormVariableConfigVo{

        @ApiModelProperty(value = "字段id")
        private Long id;
        
        private Long testeeResultId;

        @ApiModelProperty(value = "表单id")
        private Long formId;

        @ApiModelProperty(value = "表单变量类型")
        private String type;

        @ApiModelProperty(value = "字段名称")
        private String label;
        
        @ApiModelProperty(value = "数据库字段")
        private String fieldName;

        @ApiModelProperty(value = "字段英文名称")
        private String langValue;

        @ApiModelProperty(value = "字段标题")
        private String title;

        @ApiModelProperty(value = "字段内容")
        private String content;

        @ApiModelProperty(value = "是否隐藏0/1")
        private Boolean hidden;

        @ApiModelProperty(value = "绑定属性")
        private String model;

        @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
        private String requireType;

        @ApiModelProperty(value = "是否必填项0/1")
        private Boolean required;

        @ApiModelProperty(value = "是否显示字段名称0/1")
        private Boolean showTitle;

        @ApiModelProperty(value = "是否显示内容0/1")
        private Boolean showContent;

        @ApiModelProperty(value = "文字提示")
        private String placeholder;

        @ApiModelProperty(value = "变量图标")
        private String icon;

        @ApiModelProperty(value = "验证规则")
        private String rules;

        @ApiModelProperty(value = "字典来源(1-系统字典2-表单字典 3-数据单位)")
        private String dicResource;

        @ApiModelProperty(value = "引用字典id")
        private String refDicId;

        @ApiModelProperty(value = "字典默认值")
        private String defaultDicValue;

        @ApiModelProperty(value = "变量默认值")
        private String defaultValue;

        @ApiModelProperty(value = "量表打分设置")
        private BigDecimal scoreValue;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;
        
        private String unitText;

        @ApiModelProperty(value = "字段样式")
        private String styleData;

        @ApiModelProperty(value = "控件尺寸")
        private String panelSize;

        @ApiModelProperty(value = "参与者自定义标题")
        private Boolean customTestee;

        @ApiModelProperty(value = "扩展字段")
        private String expand;

        @ApiModelProperty(value = "变量排序")
        private Integer sort;

        @ApiModelProperty(value = "数据状态0/1")
        private String status;

        @ApiModelProperty(value = "表单输入值")
        private String value;
        
        @ApiModelProperty(value = "表单文本值")
        private String textValue;

    }


}
