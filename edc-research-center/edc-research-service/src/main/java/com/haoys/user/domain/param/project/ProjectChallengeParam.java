package com.haoys.user.domain.param.project;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProjectChallengeParam implements Serializable {

    @ApiModelProperty(value = "主键id-编辑质疑时设置")
    private Long id;

    @NotNull
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @NotNull
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @NotNull
    @ApiModelProperty(value = "表单项名称")
    private String formName;

    @NotNull
    @ApiModelProperty(value = "表单详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "表格id")
    private Long formTableId;

    @ApiModelProperty(value = "提交表单结果id")
    private Long formResultId;

    @ApiModelProperty(value = "提交表格结果id-表格数据发起质疑必须设置")
    private Long formResultTableId;

    @ApiModelProperty(value = "表格行编号-表格数据发起质疑必须设置")
    private Long formResultTableRowno;

    @ApiModelProperty(value = "变量名称")
    private String formResultLabel;

    @ApiModelProperty(value = "变量key")
    private String formResultKey;

    @ApiModelProperty(value = "变量值")
    private String formResultValue;

    @ApiModelProperty(value = "变量原始值")
    private String formResultOriginalValue;

    @ApiModelProperty(value = "质疑内容")
    private String content;

    @ApiModelProperty(value = "质疑类型 参考字典项004")
    private String type;

    @ApiModelProperty(value = "是否系统生成质疑")
    private Boolean ifSystem = false;

    @NotEmpty(message = "发起人角色code不能为空")
    @ApiModelProperty(value = "发起人角色标识 system、CRC、DM等")
    private String userRoleCode;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "操作人")
    private String createUser;

    @ApiModelProperty(value = "DM等角色查看当前中心全部质疑")
    private boolean queryFlag = false;


}
