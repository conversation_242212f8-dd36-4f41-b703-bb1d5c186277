package com.haoys.user.domain.param.project;


import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProjectChallengeQueryParam extends BaseVo implements Serializable {


    @NotNull
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "质疑编号")
    private String code;
    @ApiModelProperty(value = "参与者编号或姓名")
    private String testeeSearcherName;
    @ApiModelProperty(value = "姓名")
    private String realName;
    @ApiModelProperty(value = "所属中心id")
    private String orgId;
    @ApiModelProperty(value = "研究状态")
    private String status;
    @ApiModelProperty(value = "质疑状态")
    private String replyCloseStatus;
    @ApiModelProperty(value = "各角色CRC CRA等质疑人")
    private String createUser;
    @ApiModelProperty(value = "查询标识")
    private boolean queryFlag = false;
    @ApiModelProperty(value = "质疑开始时间")
    private String startDate;
    @ApiModelProperty(value = "质疑结束时间")
    private String endDate;

}
