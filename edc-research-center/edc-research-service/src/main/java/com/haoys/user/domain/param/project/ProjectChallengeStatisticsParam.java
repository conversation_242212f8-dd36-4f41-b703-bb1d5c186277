package com.haoys.user.domain.param.project;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProjectChallengeStatisticsParam extends BaseVo {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "质疑状态")
    private String challengeType;

    @ApiModelProperty(value = "用户id：不用传递")
    private String userId;

    @ApiModelProperty(value = "用户角色：不用传递")
    private String roleCode;

    @ApiModelProperty(value = "用戶拥有所属中心的id集合，前端不用传递")
    private List<String> ids;

    @ApiModelProperty(value = "质疑审核状态")
    private String reviewStatus;


}
