package com.haoys.user.domain.param.project;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectCheckQueryParam extends BaseVo {


    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "查询的开始日期")
    private String startDate;
    @ApiModelProperty(value = "查询的截至日期")
    private String endDate;

    @ApiModelProperty(value = "表单变量名称")
    private String variableName;

    @ApiModelProperty(value = "参与者code")
    private String testeeCode;

    @ApiModelProperty(value = "操作类型")
    private String type;

    @ApiModelProperty(value = "操作人")
    private String createUserName;

}
