package com.haoys.user.domain.param.project;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectCloseChallengeParam implements Serializable {

    @ApiModelProperty(value = "质疑记录id")
    private Long id;
    @ApiModelProperty(value = "关闭原因类型")
    private String closeReasonId;
    @ApiModelProperty(value = "操作人")
    private String createUser;
    @ApiModelProperty(value = "修改人-系统质疑操作记录")
    private String updateUser;
    @ApiModelProperty(value = "发起人角色标识 system、CRC、DM等")
    private String userRoleCode;


}
