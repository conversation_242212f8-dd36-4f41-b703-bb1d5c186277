package com.haoys.user.domain.param.project;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectCloseVariableChallengeParam implements Serializable {

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "访视id")
    private String visitId;

    @ApiModelProperty(value = "表单id")
    private String formId;

    @ApiModelProperty(value = "表格或者变量id")
    private String formDetailId;

    @ApiModelProperty(value = "表格单元格id")
    private String formTableId;

    @ApiModelProperty(value = "表格行记录")
    private String rowNumber;

    @ApiModelProperty(value = "质疑查询方式")
    private String queryMethod = "";

    @ApiModelProperty(value = "关闭原因类型")
    private String closeReasonId;

    @ApiModelProperty(value = "操作人")
    private String createUser;

    @ApiModelProperty(value = "发起人角色标识 SYSTEM、CRC、DM等")
    private String userRoleCode = "SYSTEM";


}
