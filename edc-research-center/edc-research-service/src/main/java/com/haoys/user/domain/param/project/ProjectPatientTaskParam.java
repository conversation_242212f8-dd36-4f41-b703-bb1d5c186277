package com.haoys.user.domain.param.project;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectPatientTaskParam implements Serializable {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务描述")
    private String description;

    @ApiModelProperty(value = "任务类型")
    private String type;

    @ApiModelProperty(value = "是否上传证据-参照字典")
    private String uploadEvidence;

    @ApiModelProperty(value = "目标访视id")
    private Long visitId;

    @ApiModelProperty(value = "目标表单id")
    private Long formId;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "任务详情变量集合")
    private List<ProjectPatientTaskVariableParam> dataList = new ArrayList<>();

    @Data
    public static class ProjectPatientTaskVariableParam {

        @ApiModelProperty(value = "主键")
        private Long id;

        @ApiModelProperty(value = "项目id")
        private Long projectId;

        @ApiModelProperty(value = "任务id")
        private Long taskId;

        @ApiModelProperty(value = "表单变量id")
        private Long formDetailId;

        @ApiModelProperty(value = "数据状态")
        private String status;

    }


}
