package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectPrescriptionParam {

    @ApiModelProperty(value = "主键id-样本数据")
    private Long id;

    @ApiModelProperty(value = "患者样本id-对应参与者id")
    private Long sampleId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "出生日期yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "诊断描述")
    private String diagnosticDesc;

    @ApiModelProperty(value = "药方内容")
    private String content;

    @ApiModelProperty(value = "药方完整信息")
    private String cotentExt;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "平台id-多租户")
    private String platformId;

}
