package com.haoys.user.domain.param.project;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class ProjectReplyChallengeParam implements Serializable {


    @ApiModelProperty(value = "项目id-用作校验数据")
    private Long projectId;
    @ApiModelProperty(value = "质疑记录id")
    private Long challengeId;
    @ApiModelProperty(value = "回复记录-暂未使用")
    private Long parentd;
    @ApiModelProperty(value = "回复内容")
    private String content;
    @ApiModelProperty(value = "操作人")
    private String createUser;
    @NotEmpty(message = "回复人角色code不能为空")
    @ApiModelProperty(value = "发起人角色标识 system、CRC、DM等")
    private String userRoleCode;

}
