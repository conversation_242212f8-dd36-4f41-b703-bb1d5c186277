package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectTesteeParam implements Serializable {

    @NotNull
    @ApiModelProperty(value = "参与者申请项目id 必填项")
    private Long projectId;

    @ApiModelProperty(value = "参与者用户id")
    private Long id;

    @ApiModelProperty(value = "系统用户id")
    private Long userId;

    @ApiModelProperty(value = "bdp用户id")
    private String bdpUserId;

    //@NotEmpty
    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "性别 参数类型 男、女")
    private String gender;

    @ApiModelProperty(value = "主管医生-当前登录人用户id")
    private String ownerDoctorId;

    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @ApiModelProperty(value = "身份证号码")
    private String idcard;

    @ApiModelProperty(value = "出生日期yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "实际年龄")
    private Integer age;

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;

    @ApiModelProperty(value = "知情日期")
    private Date informedDate;

    @ApiModelProperty(value = "参与者研究状态 参照字典说明")
    private String researchStatus;

    @ApiModelProperty(value = "所属表单")
    private Long resourceFormId;

    @ApiModelProperty(value = "数据状态 正常-0 无效-1")
    private String status;

    @ApiModelProperty(value = "参与者扩展信息")
    private String expand;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "医生自建病历")
    private Boolean selfRecord = false;

    @ApiModelProperty(value = "是否参与者绑定")
    private Boolean bindTestee = false;

    @ApiModelProperty(value = "参与者是否需要审核")
    private Boolean bindTesteeCheck = false;

    @ApiModelProperty(value = "患者绑定结果")
    private Boolean bindResult = false;

    @ApiModelProperty(value = "绑定所属用户id")
    private String bindUserId;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "项目患者来源 1-医生端创建 2-患者自建 3-其他来源")
    private String bindResource;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "中心id")
    private String projectOrgId;
}
