package com.haoys.user.domain.param.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
public class ProjectTesteeResultParam implements Serializable {

    @NotNull
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull
    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @NotNull
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @NotNull
    @ApiModelProperty(value = "表单项目id")
    private Long formId;
    
    @ApiModelProperty(value = "表单扩展id")
    private Long formExpandId;

    @NotNull
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;
    
    @ApiModelProperty(value = "数据暂存和提交状态 0-提交 -1为暂存状态")
    private String status;

    @ApiModelProperty(value = "表单变量数据集合")
    List<TesteeFormResultValue> dataList = new ArrayList<>();

    @ApiModelProperty(value = "任务提交时间 格式yyyy-MM-dd HH:mm:ss")
    private Date taskSubmitTime;

    @ApiModelProperty(value = "录入访视时间 格式yyyy-MM-dd HH:mm:ss")
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followUpRealTime;

    @ApiModelProperty(value = "表单总计录入状态 1-未录入 2-录入中 3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "操作人")
    private String operator;
    
    private String tenantId;
    private String platformId;

    @Data
    public static class TesteeFormResultValue implements Serializable{

        @ApiModelProperty(value = "参与者提交结果记录id -编辑数据时必须设置")
        private Long testeeResultId;

        @ApiModelProperty(value = "参与者字段分组id")
        private Long testeeGroupId;

        @ApiModelProperty(value = "字段组模版变量id")
        private Long resourceVariableId;

        @ApiModelProperty(value = "表单变量id")
        private Long formDetailId;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段code")
        private String fieldName;

        @ApiModelProperty(value = "字段值-如果是表单字典和数据单位字典请设置value值")
        private String fieldValue;
        
        @ApiModelProperty(value = "字段文本值")
        private String fieldText;

        @ApiModelProperty(value = "排序")
        private Integer sort;

        @ApiModelProperty(value = "数据状态0/1")
        private String status;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;
        
        @ApiModelProperty(value = "单位文本值")
        private String unitText;
        
        @ApiModelProperty(value = "选项联动隐藏标识0/1")
        private Boolean optionHidden = false;
        
        @ApiModelProperty(value = "量表打分")
        private BigDecimal scoreValue;

        @ApiModelProperty(value = "字段类型")
        private String type;

    }

}

