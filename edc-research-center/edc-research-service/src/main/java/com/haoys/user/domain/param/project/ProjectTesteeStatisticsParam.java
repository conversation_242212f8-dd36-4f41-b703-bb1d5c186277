package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 访视统计查询参数类
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTesteeStatisticsParam  extends BaseVo {

    @ApiModelProperty(value = "当前登录人的用户id，不用传递")
    private String userId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "参与者编号")
    private String code;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "主管医生-当前登录人用户id")
    private String ownerDoctor;

    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @ApiModelProperty(value = "计划访视开始时间yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startDate;

    @ApiModelProperty(value = "计划访视结束时间yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDate;


    @ApiModelProperty(value = "用戶拥有所属中心的id集合，前端不用传递")
    private List<String> ids;

}
