package com.haoys.user.domain.param.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTesteeTableColumnParam implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;
    
    @ApiModelProperty(value = "表单扩展id")
    private Long formExpandId;

    @ApiModelProperty(value = "普通表单中表格id")
    private Long formDetailId;

    @ApiModelProperty(value = "表格所在列id")
    private Long formTableId;

    @ApiModelProperty(value = "参与者字段组id")
    private Long testeeGroupId;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "表格单元格数据行记录id-编辑数据时设置")
    private Long testeeResultId;

    @ApiModelProperty(value = "字段code")
    private String fieldName;

    @ApiModelProperty(value = "表格提交记录")
    private String fieldValue;

    @ApiModelProperty(value = "单位")
    private String unitValue;

}
