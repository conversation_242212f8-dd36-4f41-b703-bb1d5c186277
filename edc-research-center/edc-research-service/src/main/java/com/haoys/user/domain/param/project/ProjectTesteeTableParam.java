package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTesteeTableParam implements Serializable {

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单项id")
    private Long formId;
    
    @ApiModelProperty(value = "表单扩展id")
    private Long formExpandId;

    @ApiModelProperty(value = "普通表单中表格id")
    private Long formDetailId;

    @ApiModelProperty(value = "字段组模版表格id")
    private Long resourceVariableId;

    @ApiModelProperty(value = "参与者字段组id")
    private Long testeeGroupId;

    @ApiModelProperty(value = "记录行号，不设置则系统生成")
    private Long rowNo;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "是否OCR识别")
    private boolean ocrRecord = false;

    @ApiModelProperty(value = "多个字段组成的行记录")
    private List<TableRowData> tableRowDataList = new LinkedList<>();

    @ApiModelProperty(value = "新增行记录文件时设置")
    private List<Long> variableImageList = new ArrayList<>();

    @ApiModelProperty(value = "是否患者端提交")
    private Boolean patientSubmit = false;

    @ApiModelProperty(value = "是否使用系统排序规则")
    private boolean systemSortRule = false;

    @ApiModelProperty(value = "方案id")
    private Long patientPlanId;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务类型id")
    private String taskType;

    @ApiModelProperty(value = "任务日期(不指定则使用当天日期) 格式yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDate;

    @ApiModelProperty(value = "任务触发频率 每天、每周、每月、每年")
    private String taskRate;

    @ApiModelProperty(value = "任务提交时间 格式yyyy-MM-dd HH:mm:ss")
    private Date taskSubmitTime;

    @ApiModelProperty(value = "每日推送频率")
    private Integer pushRate;

    @ApiModelProperty(value = "患者端任务录入完成情况 1-待录入、2-已录入、3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "表格完成状态1-未录入 2-录入中 3-已完成")
    private String tableComplateStatus;

    @ApiModelProperty(value = "操作人id")
    private String createUserId;

    private int tableRowCellCount = 0;

    private int complateCount = 0;

    private int rowcount = 0;
    
    private String tenantId;
    private String platformId;

    @Data
    public static class TableRowData implements Serializable {

        @ApiModelProperty(value = "表格单元格数据行id")
        private Long rowNumber;

        @ApiModelProperty(value = "表格单元格数据行记录id-编辑数据时设置")
        private Long testeeResultId;

        @ApiModelProperty(value = "表格id")
        private Long formDetailId;

        @ApiModelProperty(value = "字段组模版表格id")
        private Long resourceVariableId;

        @ApiModelProperty(value = "字段组表格模版列id")
        private Long resourceTableId;

        @ApiModelProperty(value = "表格所在列id")
        private Long formTableId;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "字段英文名称")
        private String langValue;

        @ApiModelProperty(value = "字段code")
        private String fieldName;

        @ApiModelProperty(value = "表格提交记录")
        private String fieldValue;
        
        @ApiModelProperty(value = "字段文本值")
        private String fieldText;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;
        
        @ApiModelProperty(value = "单位文本值")
        private String unitText;
        
        @ApiModelProperty(value = "数据状态 0-正常 1-无效")
        private String status;
        
        @ApiModelProperty(value = "表格行记录排序")
        private int sort;

        private String complateStatus;

        private String type;

    }
}
