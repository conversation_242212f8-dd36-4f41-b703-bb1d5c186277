package com.haoys.user.domain.param.project;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectTesteeViewConfigParam {

    @ApiModelProperty(value = "参与者用户id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "参与者编号")
    private String code;

    @ApiModelProperty(value = "所属中心id")
    private String ownerOrgId;

    @ApiModelProperty(value = "所属中心名称")
    private String ownerOrgName;

    @ApiModelProperty(value = "性别 男、女")
    private String gender;

    @ApiModelProperty(value = "主管医生")
    private String ownerDoctor;

    @ApiModelProperty(value = "主管医生姓名")
    private String ownerDoctorName;

    @ApiModelProperty(value = "身份证号码")
    private String idcard;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "参与者动态字段集合")
    private List<ProjectTesteeViewConfigParam.FormDataParam> dataList = new ArrayList<>();

    @ApiModelProperty(value = "是否参与者审核")
    private Boolean reviewFlag;

    @ApiModelProperty(value = "是否参与者配置审核")
    private Boolean bindTesteeCheck = false;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "审核人")
    private String reviewUserId;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

    @Data
    public static class FormDataParam{

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "变量id-CRF变量id")
        private Long id;
        @ApiModelProperty(value = "字段标签名称")
        private String label;
        @ApiModelProperty(value = "组件类型 input checkbox select radio")
        private String type;
        @ApiModelProperty(value = "字段key")
        private String key;
        @ApiModelProperty(value = "下拉框、单选按钮等填充数据")
        private String options;
        @ApiModelProperty(value = "表单输入值")
        private String value;

    }


}
