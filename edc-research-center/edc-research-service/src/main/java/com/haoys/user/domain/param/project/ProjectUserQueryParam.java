package com.haoys.user.domain.param.project;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目成员用户dto
 */
@Data
public class ProjectUserQueryParam extends BaseVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目研究中心id")
    private String projectOrgId;

    @ApiModelProperty(value = "账户名（登录账号、手机号、邮箱）")
    private String accountName;

    @ApiModelProperty(value = "数据启用状态 0-启用 1-停用")
    private String status;

    @ApiModelProperty(value = "激活状态0/1")
    private String activeStatus;

    @ApiModelProperty(value = "注册用户来源")
    private String userType;

    @ApiModelProperty(value = "用户id")
    private String userId;

     @ApiModelProperty(value = "是否锁定")
    private Boolean lockStatus;



}
