package com.haoys.user.domain.param.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectVisitConfigParam implements Serializable {

    @ApiModelProperty(value = "访视id-数据编辑时设置")
    private Long id;

    @ApiModelProperty(value = "模板id-全局模版、系统模版等必须设置")
    private Long templateId;

    @ApiModelProperty(value = "是否创建模版")
    private Boolean autoCreateTemplate = false;

    @ApiModelProperty(value = "是否使用模版创建访视")
    private Boolean copyTemplate = false;

    @ApiModelProperty(value = "模板类型-全局模版、系统模版")
    private String configType = "";

    @ApiModelProperty(value = "项目id-设置模版时不设置")
    private Long projectId;

    @ApiModelProperty(value = "上一次访视id")
    private Long preVisitId = 0L;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @ApiModelProperty(value = "访视类型 1:一般访视 2:全局访视")
    private String visitType;

    @ApiModelProperty(value = "计划访视时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followUpTime;

    @ApiModelProperty(value = "实际访视时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followUpRealTime;

    @ApiModelProperty(value = "访视周期-统一换算为天为单位计算")
    private Integer followUpPeroid;

    @ApiModelProperty(value = "访视单位 天、周、月、年")
    private String followUpUnit;

    @NotNull(message = "视窗口期开始不能为空")
    @ApiModelProperty(value = "访视窗口期开始")
    private Integer visitWindowStart;

    @NotNull(message = "访视窗口期截止不能为空")
    @ApiModelProperty(value = "访视窗口期截止-下一个访视时间窗口")
    private Integer visitWindowEnd;

    @NotNull(message = "访视窗口单位不能为空")
    @ApiModelProperty(value = "访视窗口单位 天、周、月、年")
    private String visitWindowUnit;

    @ApiModelProperty(value = "访视定制配置项内容 示例 {'followUpPeroid':'3','followUpUnit':'月'}")
    private String configData;

    @ApiModelProperty(value = "当前排序")
    private Integer sort;

    @ApiModelProperty(value = "上一个排序值")
    private Integer preSortValue;

    @ApiModelProperty(value = "下一个排序值")
    private Integer nextSortValue;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "强制更新访视配置项")
    private Boolean forceUpdate = false;
}
