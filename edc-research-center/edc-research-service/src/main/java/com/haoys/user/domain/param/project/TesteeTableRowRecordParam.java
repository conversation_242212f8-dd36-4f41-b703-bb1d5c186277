package com.haoys.user.domain.param.project;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TesteeTableRowRecordParam implements Serializable {
    List<ProjectTesteeTableParam> projectTesteeTableParamList = new ArrayList<>();
    List<DelProjectTesteeTable> delProjectTesteeTableList = new ArrayList<>();
    @Data
    public static class DelProjectTesteeTable implements Serializable {
        private String rowNumber;
        private String projectId;
        private String planId;
        private String visitId;
        private String formId;
        private String testeeGroupId;
        private String testeeId;

    }
}
