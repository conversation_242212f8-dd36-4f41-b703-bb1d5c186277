package com.haoys.user.domain.param.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/4 14:09
 */
@Data
public class ModifyRandomizedParamsConfigParam {


    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "入组上限")
    private Integer joinGroupLimit;

    @ApiModelProperty(value = "变量名称")
    private String randomizedType;

    @ApiModelProperty(value = "变量code")
    private String randomizedMethod;

    @ApiModelProperty(value = "随机号规则")
    private String randomizedRule;

    @ApiModelProperty(value = "区组长度")
    private Integer groupSize;

    @ApiModelProperty(value = "随机号前缀")
    private String randomizedFrefix;

    @ApiModelProperty(value = "随机号位数")
    private Integer randomizedDigit;

    @ApiModelProperty(value = "内嵌中心code")
    private Boolean concatOrgCode;

    @ApiModelProperty(value = "随机种子")
    private Integer randomizedSeed;

    @ApiModelProperty(value = "随机分组-json格式")
    private String RandomizedGroupConfig;

    @ApiModelProperty(value = "分层因素-json格式")
    private String RandomizedLayerConfig;

}
