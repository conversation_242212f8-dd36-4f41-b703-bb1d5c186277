package com.haoys.user.domain.param.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/11 14:00
 */
@Data
public class SaveDrugParam {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "项目id不能为空")
    private Long projectId;

    @ApiModelProperty(value = "物资类型")
    @NotEmpty(message = "物资类型不能为空")
    private String material;

    @ApiModelProperty(value = "物资编码")
    @NotEmpty(message = "物资编码不能为空")
    private String materialCode;

    @ApiModelProperty(value = "产品名")
    private String productName;

    @ApiModelProperty(value = "商品名")
    private String merchandiseName;

    @ApiModelProperty(value = "厂家")
    private String manufacturer;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "状态")
    private String materialStatus;

    @ApiModelProperty(value = "物资数量")
    private Integer materialCount;

    @ApiModelProperty(value = "包装规格")
    private String materialSpecification;

    @ApiModelProperty(value = "包装单位-JSON格式")
    private String packageUnit;

    @ApiModelProperty(value = "最小单位数量")
    private Integer minPackageUnit;

    @ApiModelProperty(value = "	库存预警阈值")
    private Integer warningValue;

    @ApiModelProperty(value = "贮藏要求")
    private String storageRequirement;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(value = "有效期开始日期")
    private Date expirationStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(value = "有效期截至日期")
    private Date expirationEndDate;

    @ApiModelProperty(value = "超期预警阈值")
    private Integer expirationWarningValue;


}
