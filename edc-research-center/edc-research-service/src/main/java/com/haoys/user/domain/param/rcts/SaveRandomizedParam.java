package com.haoys.user.domain.param.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/6/3 13:29
 */
@Data
public class SaveRandomizedParam {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "项目id不能为空")
    private Long projectId;


    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    @NotEmpty(message = "访视id不能为空")
    private Long visitId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单id")
    @NotEmpty(message = "表单id不能为空")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "变量id")
    @NotEmpty(message = "变量id不能为空")
    private Long variableId;

    @ApiModelProperty(value = "变量名称")
    private String variableName;

    @ApiModelProperty(value = "变量code")
    @NotEmpty(message = "变量code不能为空")
    private String variableCode;

    @ApiModelProperty(value = "扩展参数")
    private String expand;

}
