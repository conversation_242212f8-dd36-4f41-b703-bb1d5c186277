package com.haoys.user.domain.param.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/14 9:23
 */
@Data
public class saveDrugDistributeParam {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "药品id")
    private Long drugId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "受试者id")
    private Long testeeId;

    @ApiModelProperty(value = "受试者code")
    private String testeeCode;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "发放数量")
    private Integer distributeCount;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "药物发放时间")
    private Date distributeTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "使用开始日期")
    private Date usageStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "使用截止日期")
    private Date usageEndTime;



}
