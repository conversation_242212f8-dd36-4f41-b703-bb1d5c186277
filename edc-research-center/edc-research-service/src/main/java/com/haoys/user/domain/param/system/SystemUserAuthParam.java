package com.haoys.user.domain.param.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.param.auth.ProjectRoleParam;
import com.haoys.user.domain.param.auth.ProjectUserAuthParam;
import com.haoys.user.model.ProjectResearchersInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SystemUserAuthParam {

    @ApiModelProperty(value = "用户id")
    private String systemUserId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户姓名")
    private String realName;

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "企业部门ID")
    private String enterprise;

    @NotEmpty(message = "注册方式不能为空")
    @ApiModelProperty(value = "注册方式 手机/100001 邮箱/100002")
    private String registerType;

    private String createUserId;

    @ApiModelProperty(value = "科室id-参照科室管理")
    private String department;

    @ApiModelProperty(value = "数据状态0/1")
    private Integer status;

    @ApiModelProperty(value = "职称id-参照字典")
    private String positional;

    @ApiModelProperty(value = "角色id")
    private String roleId;
    
    @ApiModelProperty(value = "默认密码")
    private String defaultPwd;
    
    @ApiModelProperty(value = "研究者信息")
    private ProjectResearchersInfo researchersInfo;

    @ApiModelProperty(value = "临床科研项目进行授权")
    private Boolean projectAuth = false;
    private List<ProjectUserAuthParam.ProjectOrgRoleInfo> projectOrgRoleList = new ArrayList<>();
    @ApiModelProperty(value = "是否授权全部项目")
    private Boolean ownerTotalAuth = false;
    private List<ProjectRoleParam> projectRoleList = new ArrayList<>();
    

    @ApiModelProperty(value = "临床科研项目企业管理员进行授权")
    private Boolean projectAdminAuth = false;
    @ApiModelProperty(value = "授权项目列表")
    private List<String> projectIds = new ArrayList<>();

    @ApiModelProperty(value = "专病库进行授权")
    private Boolean diseaseDataBaseAuth = false;
    @ApiModelProperty(value = "是否授权全部数据库")
    private Boolean ownerTotalDatabaseAuth = false;
    @ApiModelProperty(value = "数据库授权列表")
    private List<DiseaseDatabase> DiseaseDatabaseList = new ArrayList<>();

    
    /** PU角色授权管理 */
    @Data
    public static class ProjectOrgRoleInfo {
        private Long projectId;
        private Long roleId;
        private String roleName;
        private Boolean ownerTotalAuth = false;
        // 研究中心
        private List<ProjectUserAuthParam.ProjectOrgInfo> projectOrgList = new ArrayList<>();
    }

    @Data
    public static class ProjectOrgInfo {
        private Long projectId;
        private Long orgId;
        private Long projectOrgId;
        private String orgName;
        private String projectOrgCode;
    }

    @Data
    public static class DiseaseDatabase {
        @ApiModelProperty(value = "数据库id")
        private String databaseId;
        @ApiModelProperty(value = "数据库名称")
        private String databaseName;
        @ApiModelProperty(value = "授权开始日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date validateStartDate;
        @ApiModelProperty(value = "授权截止日期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date validateEndDate;
    }
}
