package com.haoys.user.domain.param.testee;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 参与者导出条件
 */
@Data
public class ProjectTesteeExportParam {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     * 研究中心id
     */
    @ApiModelProperty(value = "研究中心id")
    private String orgId;

     /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String exportName;

    /**
     * 当前登录人
     */
    @ApiModelProperty(value = "当前登录人")
    private String userId;


    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    /**
     * 参与者id
     */
    @ApiModelProperty(value = "参与者id")
    private List<String> testeeIds;


    /**
     * 导出类型的id
     */
    @ApiModelProperty(value = "导出类型的id")
    private Integer exportType;

    /**
     * 导出信息
     */
    @ApiModelProperty(value = "导出信息")
    private List<ProjectExportFlowParam2> exportList;


    /**
     * 导出生成的文件名(用户id+一个uuid字符串，防止重复)
     */
    private String fileUrlName;


    /**
     * 是否导出全部
     */
    private Boolean exportAll=false;

}
