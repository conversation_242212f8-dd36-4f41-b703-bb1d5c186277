package com.haoys.user.domain.param.testee;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参与者导出条件
 */
@Data
public class ProjectTesteeExportSelectParam {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 研究中心id
     */
    @ApiModelProperty(value = "研究中心id")
    private Long orgId;

    /**
     * 当前登录人
     */
    @ApiModelProperty(value = "当前登录人")
    private String userId;


    /**
     * 导出类型的id
     */
    @ApiModelProperty(value = "导出类型的id")
    private Integer exportType;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String exportName;


}
