package com.haoys.user.domain.param.testee;

import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectTesteeFillInfoParam extends BaseVo {

    /**
     * 参与者编号
     */
    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;
    /**
     * 状态
     */
    @ApiModelProperty(value = "数据状态")
    private String status;

    /**
     * 状态
     */
    @ApiModelProperty(value = "参与者状态")
    private String researchStatus;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 研究中心id
     */
    @ApiModelProperty(value = "研究中心id")
    private String projectOrgId;
}
