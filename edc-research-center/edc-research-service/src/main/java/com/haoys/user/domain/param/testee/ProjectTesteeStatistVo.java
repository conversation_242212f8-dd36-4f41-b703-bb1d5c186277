package com.haoys.user.domain.param.testee;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参与者管理-首页统计信息
 */

@Data
public class ProjectTesteeStatistVo {

    @ApiModelProperty(value = "总数量")
    private Integer total=0;

    @ApiModelProperty(value = "今日数量")
    private Integer toDayNum=0;

    @ApiModelProperty(value = "昨日数量")
    private Integer yeDayNum=0;

    @ApiModelProperty(value = "筛选中数量")
    private Integer screenNum=0;

    @ApiModelProperty(value = "筛选未通过数量")
    private Integer screenNotNum=0;

    @ApiModelProperty(value = "研究中数量")
    private Integer discussNum=0;

    @ApiModelProperty(value = "研究结束数量")
    private Integer discussCloseNum=0;

    @ApiModelProperty(value = "中止脱落")
    private Integer suspendNum=0;

    @ApiModelProperty(value = "入选率")
    private String selectionRate="0";

    @ApiModelProperty(value = "不良事件数量")
    private Integer rejectsNum=0;

    @ApiModelProperty(value = "不良事件发生率")
    private String rejectsRate="0";
}
