package com.haoys.user.domain.template;

import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class TemplateFormVariableWrapper implements Serializable {

    private String projectId;
    private String templateId;
    private String templateName;
    private String formId;
    private String formName;
    private String formDetailId;
    private String label;
    private String key;
    private String type;
    private String placeholder;
    private String langValue;
    private int sort;
    private Date createTime;

    private Boolean saveFormValue = false;

    @ApiModelProperty(value = "表格行记录数据")
    private List<TemplateTableVo> tableHeadRowList = new ArrayList<>();


}
