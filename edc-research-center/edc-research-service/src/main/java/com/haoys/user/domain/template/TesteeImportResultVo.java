package com.haoys.user.domain.template;


import lombok.Data;

import java.io.Serializable;

@Data
public class TesteeImportResultVo implements Serializable {


    private String status = "0";

    private String message;

    private int testeeErrorRowCount;

    private int visitCount;

    private int formCount;


    /*private String testeeId;
    private String testeeCode;

    private List<VisitFormDetail> visitImportList = new ArrayList<>();

    @Data
    public static class VisitFormDetail {

        private String visitId;

        private List<FormImportVo> formImportList = new ArrayList<>();

    }

    @Data
    public static class FormImportVo {

        private String formId;

        private String formName;

    }*/


}
