package com.haoys.user.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AI聊天响应VO
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiChatResponseVo", description = "AI聊天响应结果")
public class AiChatResponseVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "消息ID")
    private String messageId;
    
    @ApiModelProperty(value = "回复内容")
    private String content;
    
    @ApiModelProperty(value = "是否流式响应")
    private Boolean isStream;
    
    @ApiModelProperty(value = "响应状态(success,error,processing)")
    private String status;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
    @ApiModelProperty(value = "使用的模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "使用的模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "Token使用情况")
    private TokenUsage tokenUsage;
    
    @ApiModelProperty(value = "响应时间(毫秒)")
    private Integer responseTime;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @ApiModelProperty(value = "会话信息")
    private SessionInfo sessionInfo;
    
    @Data
    @ApiModel(value = "TokenUsage", description = "Token使用情况")
    public static class TokenUsage implements Serializable {
        
        @ApiModelProperty(value = "输入Token数")
        private Integer inputTokens;
        
        @ApiModelProperty(value = "输出Token数")
        private Integer outputTokens;
        
        @ApiModelProperty(value = "总Token数")
        private Integer totalTokens;
        
        @ApiModelProperty(value = "输入成本")
        private BigDecimal inputCost;
        
        @ApiModelProperty(value = "输出成本")
        private BigDecimal outputCost;
        
        @ApiModelProperty(value = "总成本")
        private BigDecimal totalCost;
    }
    
    @Data
    @ApiModel(value = "SessionInfo", description = "会话信息")
    public static class SessionInfo implements Serializable {
        
        @ApiModelProperty(value = "会话标题")
        private String title;
        
        @ApiModelProperty(value = "消息数量")
        private Integer messageCount;
        
        @ApiModelProperty(value = "总Token数")
        private Long totalTokens;
        
        @ApiModelProperty(value = "总成本")
        private BigDecimal totalCost;
        
        @ApiModelProperty(value = "最后消息时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date lastMessageTime;
    }

    @Data
    @ApiModel(value = "MessageInfo", description = "消息信息")
    public static class MessageInfo implements Serializable {

        @ApiModelProperty(value = "消息ID")
        private String messageId;

        @ApiModelProperty(value = "角色")
        private String role;

        @ApiModelProperty(value = "消息内容")
        private String content;

        @ApiModelProperty(value = "内容类型")
        private String contentType;

        @ApiModelProperty(value = "Token数")
        private Integer tokens;

        @ApiModelProperty(value = "消费金额")
        private BigDecimal cost;

        @ApiModelProperty(value = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
    }
}
