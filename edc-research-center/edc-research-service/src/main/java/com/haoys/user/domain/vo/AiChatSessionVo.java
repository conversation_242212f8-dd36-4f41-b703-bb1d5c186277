package com.haoys.user.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AI聊天会话VO
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel(value = "AiChatSessionVo", description = "AI聊天会话信息")
public class AiChatSessionVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "会话标题")
    private String title;
    
    @ApiModelProperty(value = "使用的模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "具体模型名称")
    private String modelName;
    
    @ApiModelProperty(value = "会话状态(0-已结束,1-进行中)")
    private Integer status;
    
    @ApiModelProperty(value = "消息数量")
    private Integer messageCount;
    
    @ApiModelProperty(value = "总消耗Token数")
    private Long totalTokens;
    
    @ApiModelProperty(value = "总消费金额")
    private BigDecimal totalCost;
    
    @ApiModelProperty(value = "最后消息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastMessageTime;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @ApiModelProperty(value = "最近消息列表")
    private List<MessageInfo> recentMessages;
    
    @Data
    @ApiModel(value = "MessageInfo", description = "消息信息")
    public static class MessageInfo implements Serializable {
        
        @ApiModelProperty(value = "消息ID")
        private String messageId;
        
        @ApiModelProperty(value = "角色")
        private String role;
        
        @ApiModelProperty(value = "消息内容")
        private String content;
        
        @ApiModelProperty(value = "内容类型")
        private String contentType;
        
        @ApiModelProperty(value = "Token数")
        private Integer tokens;
        
        @ApiModelProperty(value = "消费金额")
        private BigDecimal cost;
        
        @ApiModelProperty(value = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
    }
}
