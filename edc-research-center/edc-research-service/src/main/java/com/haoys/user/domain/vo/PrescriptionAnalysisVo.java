package com.haoys.user.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PrescriptionAnalysisVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "分析批次名称")
    private String title;

    @ApiModelProperty(value = "分析批次记录code")
    private String batchCode;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

    @ApiModelProperty(value = "诊断分析结果")
    private String diagnosticResult;

    @ApiModelProperty(value = "药方分析结果")
    private String prescriptionResult;
}
