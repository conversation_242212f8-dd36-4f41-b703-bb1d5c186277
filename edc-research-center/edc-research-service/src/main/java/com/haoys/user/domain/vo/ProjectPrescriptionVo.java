package com.haoys.user.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectPrescriptionVo {


    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id-样本数据")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "患者样本id-对应参与者id")
    private Long sampleId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "诊断描述")
    private String diagnosticDesc;

    @ApiModelProperty(value = "药方内容")
    private String content;

    @ApiModelProperty(value = "药方完整信息")
    private String cotentExt;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "平台id-多租户")
    private String platformId;

}
