package com.haoys.user.domain.vo;

import com.haoys.user.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuantitativeAnalysisExcelVo implements Serializable {

    @Excel(name = "诊断描述")
    private String participleText;
    @Excel(name = "气血失调证")
    private String var1;
    @Excel(name = "肝郁气滞证")
    private String var2;
    @Excel(name = "糖尿病")
    private String var3;
    @Excel(name = "胃胀")
    private String var4;
    @Excel(name = "脾肾两虚证")
    private String var5;
    @Excel(name = "乏力")
    private String var6;
    @Excel(name = "2型糖尿病")
    private String var7;
    @Excel(name = "脾肾亏虚证")
    private String var8;
    @Excel(name = "不寐病")
    private String var9;
    @Excel(name = "失眠")
    private String var10;
    @Excel(name = "心悸")
    private String var11;
    @Excel(name = "蛋白尿")
    private String var12;
    @Excel(name = "尿频")
    private String var13;
    @Excel(name = "胸闷")
    private String var14;
    @Excel(name = "气短")
    private String var15;
    @Excel(name = "月经不调")
    private String var16;
    @Excel(name = "呃逆")
    private String var17;
    @Excel(name = "腰痛")
    private String var18;
    @Excel(name = "湿热内蕴胃")
    private String var19;
    @Excel(name = "胃胀病")
    private String var20;

}
