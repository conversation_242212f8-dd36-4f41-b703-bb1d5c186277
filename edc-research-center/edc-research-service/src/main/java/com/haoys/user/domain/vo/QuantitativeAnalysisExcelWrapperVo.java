package com.haoys.user.domain.vo;


import com.haoys.user.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuantitativeAnalysisExcelWrapperVo implements Serializable {

    @Excel(name = "药方")
    private String participleText;

    @Excel(name = "山药,泽泻,白术,山茱萸,白芍")
    private String prescriptionA;
    @Excel(name = "山药,白术,山茱萸,白芍,茯苓")
    private String prescriptionB;
    @Excel(name = "山药,泽泻,白术,白芍,茯苓")
    private String prescriptionC;
    @Excel(name = "生地,泽泻,白术,白芍,茯苓")
    private String prescriptionD;

    @Excel(name = "生地,泽泻,白术,山茱萸,白芍")
    private String prescriptionE;
    @Excel(name = "泽泻,白术,山茱萸,白芍,茯苓")
    private String prescriptionF;
    @Excel(name = "生地,白术,山茱萸,白芍,茯苓")
    private String prescriptionG;
    @Excel(name = "山药,生地,泽泻,白术,白芍")
    private String prescriptionH;
    @Excel(name = "山药,生地,泽泻,白术,茯苓")
    private String prescriptionI;
    @Excel(name = "山药,生地,泽泻,白芍,茯苓")
    private String prescriptionJ;
    @Excel(name = "山药,生地,白术,白芍,茯苓")
    private String prescriptionK;

}
