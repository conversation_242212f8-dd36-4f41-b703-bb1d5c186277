package com.haoys.user.domain.vo.overview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class ProjectComprehensiveVo implements Serializable {


    @ApiModelProperty(value = "中心名称")
    private String orgName;

    @ApiModelProperty(value = "项目负责人数量")
    private int paCount = 0;

    @ApiModelProperty(value = "研究者数据量")
    private int piCount = 0;

    @ApiModelProperty(value = "医学助理数量")
    private int crcCount = 0;

    @ApiModelProperty(value = "质疑统计")
    private int changleCount = 0;

    @ApiModelProperty(value = "本月新增参与者")
    private int increasedCount = 0;

    @ApiModelProperty(value = "代办任务")
    private int plannedVisitCount = 0;

    @ApiModelProperty(value = "逾期访视")
    private int overdueCount = 0;



    
}
