package com.haoys.user.domain.vo.overview;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectOverViewVo implements Serializable {

    @ApiModelProperty(value = "访视质疑总量")
    private int visitChangleCount = 0;

    @ApiModelProperty(value = "本月新增参与者")
    private int testeeCount = 0;

    @ApiModelProperty(value = "代办任务-实际添加访视时间数量")
    private int realVisitCount = 0;

    @ApiModelProperty(value = "代办任务-计划访视时间数量")
    private int plannedCount = 0;

    @ApiModelProperty(value = "本月逾期未访视")
    private int overdueCount = 0;

}
