package com.haoys.user.domain.vo.participant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.common.api.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ParticipantHeadRowViewVo {

    @ApiModelProperty(value = "参与者列表")
    private CommonPage<ProjectParticipantViewConfigVo> projectParticipantList;

    @ApiModelProperty(value = "参与者列表自定义标题配置描述")
    private List<VariableHeadRowConfig> variableHeadRowList = new ArrayList<>();

    @ApiModelProperty(value = "最新流程发布状态")
    private Boolean projectFlowPublishStatus = false;

    @Data
    public static class VariableHeadRowConfig {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "字段名称")
        private Long id;

        @ApiModelProperty(value = "表单变量类型")
        private String type;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "数据库字段")
        private String fieldName;

        @ApiModelProperty(value = "是否筛查数据字段查询")
        private Boolean queryField = false;

        @ApiModelProperty(value = "数据字段")
        private String queryFieldValue = "";

        @ApiModelProperty(value = "绑定属性")
        private String model;

        @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
        private String requireType;

        @ApiModelProperty(value = "是否必填项0/1")
        private Boolean required;

        @ApiModelProperty(value = "字典来源(1-系统字典2-表单字典 3-数据单位)")
        private String dicResource;

        @ApiModelProperty(value = "引用字典id")
        private String refDicId;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;

        @ApiModelProperty(value = "参与者自定义标题")
        private Boolean customTestee;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "源映射字段id")
        private Long baseVariableId;

        @ApiModelProperty(value = "扩展字段")
        private String expand;

        @ApiModelProperty(value = "变量排序")
        private Integer sort;
    }
}
