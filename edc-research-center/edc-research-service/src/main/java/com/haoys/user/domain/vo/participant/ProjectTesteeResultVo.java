package com.haoys.user.domain.vo.participant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectTesteeResultVo{
    private String id;
    private String fieldValue;
    private String dicResource;
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "表单变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "字段组模版变量id")
    private Long resourceVariableId;

    @ApiModelProperty(value = "字段组分组id")
    private Long groupId;

    private String type;

}
