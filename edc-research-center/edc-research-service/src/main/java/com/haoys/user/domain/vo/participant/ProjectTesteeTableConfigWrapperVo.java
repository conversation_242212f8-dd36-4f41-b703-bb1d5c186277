package com.haoys.user.domain.vo.participant;

import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormTableConfigVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectTesteeTableConfigWrapperVo {

    private List<ProjectTesteeTableVo> projectTesteeTableRowRecord = new ArrayList<>();

    private ProjectTesteeFormTableConfigVo projectTesteeFormTableConfigVo;
}
