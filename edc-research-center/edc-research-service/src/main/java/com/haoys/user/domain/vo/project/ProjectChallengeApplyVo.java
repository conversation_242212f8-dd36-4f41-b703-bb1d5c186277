package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectChallengeApplyVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "质疑id")
    private Long challengeId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "回复id")
    private Long parentId;

    @ApiModelProperty(value = "发起人角色类型 CRC CRA DM")
    private String userRoleCode;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "创建人id")
    private String createUser;

    @ApiModelProperty(value = "回复人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "回复内容")
    private String content;

    @ApiModelProperty(value = "是否是质疑人")
    private boolean ifChallengeUser;


}
