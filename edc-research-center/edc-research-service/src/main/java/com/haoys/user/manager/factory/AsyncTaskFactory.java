package com.haoys.user.manager.factory;

import cn.hutool.http.useragent.UserAgentUtil;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.ServletUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.model.SystemExceptionLog;
import com.haoys.user.model.SystemLoginLog;
import com.haoys.user.model.SystemPointLog;
import com.haoys.user.model.SystemRequestLog;
import com.haoys.user.model.SystemRequestRecord;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.SendMessageService;
import com.haoys.user.service.SystemExceptionLogService;
import com.haoys.user.service.SystemLoginLogService;
import com.haoys.user.service.SystemPointLogService;
import com.haoys.user.service.SystemRequestLogService;
import com.haoys.user.service.SystemUserInfoService;
import eu.bitwalker.useragentutils.UserAgent;
import net.dreamlu.mica.ip2region.core.IpInfo;
import net.dreamlu.mica.ip2region.impl.Ip2regionSearcherImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.TimerTask;

public class AsyncTaskFactory {

    private static final Logger log = LoggerFactory.getLogger(AsyncTaskFactory.class);

    public static TimerTask recordSystemloginLogInfo(HttpServletRequest request, final String username, final String status, final String message, final String operateType, final Object... args) {
        UserAgent userAgentOld;
        String userAgentValue = "";
        if(request == null){
            userAgentValue = ServletUtils.getRequest().getHeader("User-Agent");
            userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
            request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
        }else {
            userAgentValue = request.getHeader("User-Agent");
            userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
        }
        
        String ipAddress = RequestIpUtils.getIpAddress(request);
        //String ipAdrress = RequestIpUtils.getIp(request);
        final UserAgent userAgent = userAgentOld;
        String finalUserAgentValue = userAgentValue;
        return new TimerTask() {
            @Override
            public void run() {
                SystemUserInfoVo systemUserInfoVo = SpringUtils.getBean(SystemUserInfoService.class).getSystemUserInfoByAccountName(username);
                Ip2regionSearcherImpl ip2regionSearcher  = SpringUtils.getBean(Ip2regionSearcherImpl.class);
                IpInfo ipInfo = ip2regionSearcher.memorySearch(ipAddress);
                String address = ipInfo.getAddressAndIsp();
                String browser = userAgent.getBrowser().getName();
                SystemLoginLog systemLoginLog = new SystemLoginLog();
                systemLoginLog.setUserName(username);
                systemLoginLog.setRequestIp(ipAddress);
                systemLoginLog.setLocation(address);
                systemLoginLog.setBrowser(browser);
                systemLoginLog.setOs(userAgent.getOperatingSystem().getName());
                systemLoginLog.setMessage(message);
                systemLoginLog.setLoginTime(new Date());
                systemLoginLog.setOperateType(operateType);
                if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
                {
                    systemLoginLog.setStatus(Constants.SUCCESS);
                }
                else if (Constants.LOGIN_FAIL.equals(status))
                {
                    systemLoginLog.setStatus(Constants.FAIL);
                }
                if(systemUserInfoVo != null){systemLoginLog.setRealName(systemUserInfoVo.getRealName());}
                SpringUtils.getBean(SystemLoginLogService.class).insertSystemUserLoginLog(systemLoginLog);
                if(systemUserInfoVo != null){
                    SystemUserInfo systemUserInfo = new SystemUserInfo();
                    BeanUtils.copyProperties(systemUserInfoVo, systemUserInfo);
                    systemUserInfo.setLoginTime(new Date());
                    cn.hutool.http.useragent.UserAgent userAgentInfo = UserAgentUtil.parse(finalUserAgentValue);
                    String platform = userAgentInfo.getPlatform().toString();
                    String browserValue = userAgentInfo.getBrowser().toString();//Chrome
                    String version = userAgentInfo.getVersion();//14.0.835.163
                    String engine = userAgentInfo.getEngine().toString();//Webkit
                    String engineVsersion = userAgentInfo.getEngineVersion();//535.1
                    String os = userAgentInfo.getOs().toString();//Windows 7
                    systemUserInfo.setLoginDevice(platform.concat("/").concat(os).concat("/").concat(browserValue)
                            .concat("/").concat(version).concat("/").concat(engine)
                            .concat("/").concat(engineVsersion));
                    SpringUtils.getBean(SystemUserInfoService.class).updateByPrimaryKeySelective(systemUserInfo);
                }
            }
        };
    }

    public static TimerTask saveSystemRequestLog(final SystemRequestLog operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SystemRequestLogService.class).insertOperlog(operLog);
            }
        };
    }

    public static TimerTask insertSystemExceptionLog(SystemExceptionLog systemExceptionLog) {
        return new TimerTask() {
            @Override
            public void run() {
                systemExceptionLog.setId(SnowflakeIdWorker.getUuid());
                SpringUtils.getBean(SystemExceptionLogService.class).insertSystemExceptionlog(systemExceptionLog);
            }
        };
    }

    public static TimerTask insertSystemPointLog(String userIdValue, String username, String className, String methodName, String params, String result) {
        return new TimerTask() {
            @Override
            public void run() {
                SystemPointLog systemPointLog = new SystemPointLog();
                systemPointLog.setId(SnowflakeIdWorker.getUuid());
                systemPointLog.setClassName(className);
                systemPointLog.setMethodName(methodName);
                systemPointLog.setParams(params);
                systemPointLog.setResult(result);
                systemPointLog.setUserId(userIdValue);
                systemPointLog.setUserName(username);
                systemPointLog.setCreateTime(new Date());
                SpringUtils.getBean(SystemPointLogService.class).insertSystemPointlog(systemPointLog);
            }
        };

    }
    
    public static TimerTask updateProjectTesteeBaseInfoVariableInputValues(String projectId, String testeeId, String operator, String systemTenantId, String systemPlatformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeBaseInfoVariableInputValues(projectId, testeeId, operator, systemTenantId, systemPlatformId);
            }
        };
    }

    public static TimerTask updateProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId, String status, String operator, String complateStatus, String tenantId, String platformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId, status, complateStatus,operator, tenantId, platformId);
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeTableProcess(projectId, projectOrgId, planId, visitId, testeeId, status, operator);
            }
        };
    }

    public static TimerTask initProjectOrgRole(ProjectOrgParam projectOrgParam, Long projectOrgId, String message, String systemLogLogin) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(OrganizationService.class).initProjectOrgRole(projectOrgParam, projectOrgId);
            }
        };
    }
    
    public static TimerTask insertflowFormSetExpandForTemplate(String projectId, String planId, String visitId, String formId, String testeeId, String systemTenantId, String systemPlatformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(FlowFormSetService.class).insertflowFormSetExpandForTemplate(projectId, planId, visitId, formId, testeeId, systemTenantId, systemPlatformId);
            }
        };
    }
    
    public static TimerTask insertSendMessageRecord(String messageAccount, String code, String requestId, String responseText) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SendMessageService.class).insertSendMessageRecord(messageAccount, code, requestId, responseText);
            }
        };
    }
    
    public static TimerTask updateSystemUserValue(SystemUserInfo systemUserInfo) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SystemUserInfoService.class).updateSystemUser(systemUserInfo);
            }
        };
    }

    // ==================== 新版系统访问记录相关任务 ====================

    /**
     * 保存系统访问记录（新增方法）
     *
     * @param record 访问记录
     * @return TimerTask
     */
    public static TimerTask saveSystemRequestRecord(final SystemRequestRecord record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record == null) {
                        log.warn("系统访问记录为空，跳过保存");
                        return;
                    }

                    // 获取服务实例并保存记录
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object result = recordService.getClass().getMethod("saveRecord", SystemRequestRecord.class).invoke(recordService, record);
                        if (result instanceof Boolean && !(Boolean) result) {
                            log.warn("保存系统访问记录失败: traceId={}", record.getTraceId());
                        }
                    } else {
                        log.error("无法获取 SystemRequestRecordService 实例");
                    }
                } catch (Exception e) {
                    log.error("异步保存系统访问记录失败: traceId={}",
                             record != null ? record.getTraceId() : "unknown", e);
                }
            }
        };
    }

    /**
     * 批量保存系统访问记录
     *
     * @param records 访问记录列表
     * @return TimerTask
     */
    public static TimerTask batchSaveSystemRequestRecords(final java.util.List<SystemRequestRecord> records) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (records == null || records.isEmpty()) {
                        log.warn("系统访问记录列表为空，跳过批量保存");
                        return;
                    }

                    // 获取服务实例并批量保存记录
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object result = recordService.getClass().getMethod("batchSaveRecords", java.util.List.class).invoke(recordService, records);
                        if (result instanceof Boolean && !(Boolean) result) {
                            log.warn("批量保存系统访问记录失败，记录数: {}", records.size());
                        } else {
                            log.debug("批量保存系统访问记录成功，记录数: {}", records.size());
                        }
                    } else {
                        log.error("无法获取 SystemRequestRecordService 实例");
                    }
                } catch (Exception e) {
                    log.error("异步批量保存系统访问记录失败，记录数: {}",
                             records != null ? records.size() : 0, e);
                }
            }
        };
    }

    /**
     * 清理过期的系统访问记录
     *
     * @param retentionDays 保留天数
     * @return TimerTask
     */
    public static TimerTask cleanupExpiredSystemRequestRecords(final int retentionDays) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object result = recordService.getClass().getMethod("deleteExpiredRecords", int.class).invoke(recordService, retentionDays);
                        if (result instanceof Integer) {
                            log.info("清理过期系统访问记录完成，删除记录数: {}, 保留天数: {}", result, retentionDays);
                        }
                    }
                } catch (Exception e) {
                    log.error("清理过期系统访问记录失败", e);
                }
            }
        };
    }

    /**
     * 优化系统访问记录表
     *
     * @return TimerTask
     */
    public static TimerTask optimizeSystemRequestRecordTable() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        recordService.getClass().getMethod("optimizeTable").invoke(recordService);
                        log.info("优化系统访问记录表完成");
                    }
                } catch (Exception e) {
                    log.error("优化系统访问记录表失败", e);
                }
            }
        };
    }

    /**
     * 生成系统访问记录报告
     *
     * @return TimerTask
     */
    public static TimerTask generateSystemRequestRecordReport() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object reportData = recordService.getClass().getMethod("generateReport").invoke(recordService);
                        if (reportData instanceof java.util.Map) {
                            log.info("生成系统访问记录报告完成，数据项数: {}", ((java.util.Map<?, ?>) reportData).size());
                        }
                    }
                } catch (Exception e) {
                    log.error("生成系统访问记录报告失败", e);
                }
            }
        };
    }

    /**
     * 收集系统访问记录统计数据
     *
     * @return TimerTask
     */
    public static TimerTask collectSystemRequestRecordStats() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        // 修复：调用正确的方法名getRealTimeStatistics
                        Object statsData = recordService.getClass().getMethod("getRealTimeStatistics").invoke(recordService);
                        if (statsData instanceof java.util.Map) {
                            log.info("收集系统访问记录统计数据完成，统计项数: {}", ((java.util.Map<?, ?>) statsData).size());
                        }
                    }
                } catch (Exception e) {
                    log.error("收集系统访问记录统计数据失败", e);
                }
            }
        };
    }

    /**
     * 发送系统访问记录告警
     *
     * @param alertType 告警类型
     * @param message 告警消息
     * @return TimerTask
     */
    public static TimerTask sendSystemRequestRecordAlert(final String alertType, final String message) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    log.warn("系统访问记录告警 [{}]: {}", alertType, message);
                    // 这里可以添加具体的告警发送逻辑，比如发送邮件、短信或推送到监控系统
                } catch (Exception e) {
                    log.error("发送系统访问记录告警失败", e);
                }
            }
        };
    }

    /**
     * 归档系统访问记录
     *
     * @param retentionDays 保留天数
     * @return TimerTask
     */
    public static TimerTask archiveSystemRequestRecords(final int retentionDays) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object result = recordService.getClass().getMethod("archiveRecords", int.class).invoke(recordService, retentionDays);
                        if (result instanceof Integer) {
                            log.info("归档系统访问记录完成，归档记录数: {}, 保留天数: {}", result, retentionDays);
                        }
                    }
                } catch (Exception e) {
                    log.error("归档系统访问记录失败", e);
                }
            }
        };
    }

    /**
     * 优化系统访问记录索引
     *
     * @return TimerTask
     */
    public static TimerTask optimizeSystemRequestRecordIndexes() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        recordService.getClass().getMethod("optimizeIndexes").invoke(recordService);
                        log.info("优化系统访问记录索引完成");
                    }
                } catch (Exception e) {
                    log.error("优化系统访问记录索引失败", e);
                }
            }
        };
    }

    /**
     * 清理系统访问记录缓存
     *
     * @return TimerTask
     */
    public static TimerTask cleanupSystemRequestRecordCache() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        recordService.getClass().getMethod("cleanupCache").invoke(recordService);
                        log.info("清理系统访问记录缓存完成");
                    }
                } catch (Exception e) {
                    log.error("清理系统访问记录缓存失败", e);
                }
            }
        };
    }

    /**
     * 处理慢请求告警
     *
     * @param record 慢请求记录
     * @return TimerTask
     */
    public static TimerTask handleSlowRequestAlert(final SystemRequestRecord record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record != null && record.getResponseTime() != null) {
                        log.warn("慢请求告警: URL={}, 方法={}, 响应时间={}ms, 用户={}, IP={}",
                                record.getRequestUrl(),
                                record.getMethodName(),
                                record.getResponseTime(),
                                record.getUserName(),
                                record.getRequestIp());

                        // 这里可以添加告警通知逻辑，比如发送邮件、短信或推送到监控系统
                    }
                } catch (Exception e) {
                    log.error("处理慢请求告警失败", e);
                }
            }
        };
    }

    /**
     * 处理异常请求告警
     *
     * @param record 异常请求记录
     * @return TimerTask
     */
    public static TimerTask handleErrorRequestAlert(final SystemRequestRecord record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record != null && !record.getIsSuccess()) {
                        log.error("异常请求告警: URL={}, 方法={}, 错误信息={}, 用户={}, IP={}",
                                record.getRequestUrl(),
                                record.getMethodName(),
                                record.getErrorMessage(),
                                record.getUserName(),
                                record.getRequestIp());

                        // 这里可以添加告警通知逻辑
                    }
                } catch (Exception e) {
                    log.error("处理异常请求告警失败", e);
                }
            }
        };
    }
}
