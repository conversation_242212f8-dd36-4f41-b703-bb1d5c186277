package com.haoys.user.mapper;

import com.haoys.user.domain.entity.AiChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI聊天消息Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Mapper
public interface AiChatMessageMapper {
    
    /**
     * 插入消息记录
     */
    int insert(AiChatMessage message);
    
    /**
     * 根据ID查询消息
     */
    AiChatMessage selectById(@Param("id") Long id);
    
    /**
     * 根据消息ID查询消息
     */
    AiChatMessage selectByMessageId(@Param("messageId") String messageId);
    
    /**
     * 根据会话ID查询消息列表
     */
    List<AiChatMessage> selectBySessionId(@Param("sessionId") String sessionId,
                                         @Param("offset") Integer offset,
                                         @Param("limit") Integer limit);
    
    /**
     * 根据会话ID查询消息数量
     */
    int countBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 根据用户ID查询消息列表
     */
    List<AiChatMessage> selectByUserId(@Param("userId") String userId,
                                      @Param("offset") Integer offset,
                                      @Param("limit") Integer limit);
    
    /**
     * 更新消息内容
     */
    int updateContent(@Param("messageId") String messageId, @Param("content") String content);
    
    /**
     * 更新消息Token和成本
     */
    int updateTokensAndCost(@Param("messageId") String messageId,
                           @Param("tokens") Integer tokens,
                           @Param("cost") java.math.BigDecimal cost);
    
    /**
     * 更新响应时间
     */
    int updateResponseTime(@Param("messageId") String messageId, @Param("responseTime") Integer responseTime);
    
    /**
     * 更新错误信息
     */
    int updateErrorInfo(@Param("messageId") String messageId, @Param("errorInfo") String errorInfo);
    
    /**
     * 删除消息
     */
    int deleteByMessageId(@Param("messageId") String messageId);
    
    /**
     * 根据会话ID删除所有消息
     */
    int deleteBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 获取会话的上下文消息(最近N条)
     */
    List<AiChatMessage> selectContextMessages(@Param("sessionId") String sessionId, 
                                             @Param("limit") Integer limit);
}
