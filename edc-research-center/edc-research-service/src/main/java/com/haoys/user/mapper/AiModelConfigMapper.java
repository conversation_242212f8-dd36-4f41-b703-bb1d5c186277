package com.haoys.user.mapper;

import com.haoys.user.domain.entity.AiModelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI模型配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Mapper
public interface AiModelConfigMapper {
    
    /**
     * 插入模型配置
     */
    int insert(AiModelConfig config);
    
    /**
     * 根据ID查询配置
     */
    AiModelConfig selectById(@Param("id") Long id);
    
    /**
     * 根据模型类型和名称查询配置
     */
    AiModelConfig selectByTypeAndName(@Param("modelType") String modelType, 
                                     @Param("modelName") String modelName);
    
    /**
     * 查询所有启用的模型配置
     */
    List<AiModelConfig> selectEnabledConfigs();
    
    /**
     * 根据模型类型查询配置列表
     */
    List<AiModelConfig> selectByType(@Param("modelType") String modelType, 
                                    @Param("enabled") Boolean enabled);
    
    /**
     * 查询所有配置(分页)
     */
    List<AiModelConfig> selectAll(@Param("offset") Integer offset, 
                                 @Param("limit") Integer limit);
    
    /**
     * 统计配置数量
     */
    int count();
    
    /**
     * 更新配置
     */
    int updateById(AiModelConfig config);
    
    /**
     * 更新启用状态
     */
    int updateEnabled(@Param("id") Long id, @Param("enabled") Boolean enabled);
    
    /**
     * 删除配置
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据优先级查询最佳模型
     */
    AiModelConfig selectBestModel(@Param("modelType") String modelType);
}
