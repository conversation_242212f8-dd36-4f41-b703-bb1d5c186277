package com.haoys.user.mapper;

import com.haoys.user.model.FlowFormSetExpand;
import com.haoys.user.model.FlowFormSetExpandExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FlowFormSetExpandMapper {
    long countByExample(FlowFormSetExpandExample example);

    int deleteByExample(FlowFormSetExpandExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlowFormSetExpand record);

    int insertSelective(FlowFormSetExpand record);

    List<FlowFormSetExpand> selectByExample(FlowFormSetExpandExample example);

    FlowFormSetExpand selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FlowFormSetExpand record, @Param("example") FlowFormSetExpandExample example);

    int updateByExample(@Param("record") FlowFormSetExpand record, @Param("example") FlowFormSetExpandExample example);

    int updateByPrimaryKeySelective(FlowFormSetExpand record);

    int updateByPrimaryKey(FlowFormSetExpand record);
    
    List<FlowFormSetExpand> getFormSetExpandList(String projectId, String planId, String visitId, String formId, String testeeId);
    
    FlowFormSetExpand getFormSetExpandTemplateCopy(String projectId, String planId, String visitId, String formId, String testeeId);
    
    FlowFormSetExpand getFormExpandByFormSetId(String projectId, String formSetId, String testeeId);
}