package com.haoys.user.mapper;

import com.haoys.user.domain.expand.FlowFormSetValueExpand;
import com.haoys.user.domain.vo.flow.ProjectFlowFormVo;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowFormSetExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FlowFormSetMapper {
    long countByExample(FlowFormSetExample example);

    int deleteByExample(FlowFormSetExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlowFormSet record);

    int insertSelective(FlowFormSet record);

    List<FlowFormSet> selectByExample(FlowFormSetExample example);

    FlowFormSet selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FlowFormSet record, @Param("example") FlowFormSetExample example);

    int updateByExample(@Param("record") FlowFormSet record, @Param("example") FlowFormSetExample example);

    int updateByPrimaryKeySelective(FlowFormSet record);

    int updateByPrimaryKey(FlowFormSet record);

    /**
     * 批量保存
     * @param list 数据集合
     * @return 保存的条数
     */
    int batchSave(List<FlowFormSet> list);

    /**
     * 获取配置研究流程的列表
     * @param flowId 流程id
     * @return
     */
    List<FlowFormSet> listByFlowId(Long flowId);


    /**
     * 获取配置研究流程的列表同时根据权限进行过滤
     * @param formSet 流程id
     * @return
     */
    List<ProjectFlowFormVo> listByFlowIdAndFilterPer(FlowFormSet formSet);

    /**
     * 更新pc和移动端权限
     * @param formSet
     * @return
     */
    int UpdatePer(FlowFormSet formSet);

    FlowFormSetValueExpand getFormConfigListByProjectIdAndFormId(String projectId, String planId, String visitId, String formId);

    List<FlowFormSetValueExpand> getFlowFormSetValueExpandByFlowId(Long flowId);
}
