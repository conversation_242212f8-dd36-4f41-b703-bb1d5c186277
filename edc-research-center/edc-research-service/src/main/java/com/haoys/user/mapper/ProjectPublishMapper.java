package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPublish;
import com.haoys.user.model.ProjectPublishExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPublishMapper {
    long countByExample(ProjectPublishExample example);

    int deleteByExample(ProjectPublishExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPublish record);

    int insertSelective(ProjectPublish record);

    List<ProjectPublish> selectByExample(ProjectPublishExample example);

    ProjectPublish selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPublish record, @Param("example") ProjectPublishExample example);

    int updateByExample(@Param("record") ProjectPublish record, @Param("example") ProjectPublishExample example);

    int updateByPrimaryKeySelective(ProjectPublish record);

    int updateByPrimaryKey(ProjectPublish record);
}