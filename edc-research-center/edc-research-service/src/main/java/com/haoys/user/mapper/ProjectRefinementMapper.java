package com.haoys.user.mapper;

import com.haoys.user.model.ProjectRefinement;
import com.haoys.user.model.ProjectRefinementExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectRefinementMapper {
    long countByExample(ProjectRefinementExample example);

    int deleteByExample(ProjectRefinementExample example);

    int insert(ProjectRefinement record);

    int insertSelective(ProjectRefinement record);

    List<ProjectRefinement> selectByExampleWithBLOBs(ProjectRefinementExample example);

    List<ProjectRefinement> selectByExample(ProjectRefinementExample example);

    int updateByExampleSelective(@Param("record") ProjectRefinement record, @Param("example") ProjectRefinementExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectRefinement record, @Param("example") ProjectRefinementExample example);

    int updateByExample(@Param("record") ProjectRefinement record, @Param("example") ProjectRefinementExample example);
}