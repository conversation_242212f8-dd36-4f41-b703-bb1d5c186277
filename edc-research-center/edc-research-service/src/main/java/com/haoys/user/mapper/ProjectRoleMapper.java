package com.haoys.user.mapper;

import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectRoleExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProjectRoleMapper {
    long countByExample(ProjectRoleExample example);

    int deleteByExample(ProjectRoleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectRole record);

    int insertSelective(ProjectRole record);

    List<ProjectRole> selectByExample(ProjectRoleExample example);

    ProjectRole selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectRole record, @Param("example") ProjectRoleExample example);

    int updateByExample(@Param("record") ProjectRole record, @Param("example") ProjectRoleExample example);

    int updateByPrimaryKeySelective(ProjectRole record);

    int updateByPrimaryKey(ProjectRole record);


    /**
     * 按照指定条件查询项目角色
     * @param projectRoleQuery
     * @return
     */
    List<ProjectRoleQuery> selectProjectRoleListByQueryCondition(ProjectRoleQuery projectRoleQuery);

    /**
     * 校验角色名称是否唯一
     * @return 角色信息
     */
    ProjectRole getProjectRoleInfoByRoleName(@Param("projectId")String projectId, @Param("roleName")String roleName);

    /**
     * 通过项目角色ID查询角色使用数量
     * @param roleId 角色ID
     * @return 结果
     */
    int countProjectUserRoleByRoleId(@Param("roleId")Long roleId);


    List<ProjectRoleVo> getProjectRoleListByUserId(@Param("projectId")String projectId, @Param("userId")String userId);
    
    ProjectRole selectOne(ProjectRoleQuery projectRoleQuery);

    int update(ProjectRoleQuery projectRoleQuery);

    /**
     * 校验角色英文名称是否唯一
     * @param enname 角色英文名称
     * @return 角色信息
     */
    ProjectRole getProjectRoleInfoByEnName(@Param("projectId")String projectId, @Param("enname")String enname);

    /**
     * 查询模版角色信息
     * @param projectRoleCode
     * @return
     */
    ProjectRole getProjectTemplateRoleInfoByRoleCode(String projectRoleCode);
    
    ProjectRole getProjectManageRoleInfoByProjectRoleCode(String projectId, String code, String tenantId);
}
