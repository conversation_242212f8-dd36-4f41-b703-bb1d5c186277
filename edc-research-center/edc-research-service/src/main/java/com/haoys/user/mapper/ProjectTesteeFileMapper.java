package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectTesteeFileVo;
import com.haoys.user.model.ProjectTesteeFile;
import com.haoys.user.model.ProjectTesteeFileExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeFileMapper {
    long countByExample(ProjectTesteeFileExample example);

    int deleteByExample(ProjectTesteeFileExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeFile record);

    int insertSelective(ProjectTesteeFile record);

    List<ProjectTesteeFile> selectByExample(ProjectTesteeFileExample example);

    ProjectTesteeFile selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeFile record, @Param("example") ProjectTesteeFileExample example);

    int updateByExample(@Param("record") ProjectTesteeFile record, @Param("example") ProjectTesteeFileExample example);

    int updateByPrimaryKeySelective(ProjectTesteeFile record);

    int updateByPrimaryKey(ProjectTesteeFile record);

    List<ProjectTesteeFile> getProjectTesteeThumbnailImageByFileId(String projectId, String fileId);

    /**
     * 查询患者上传文件列表-综合查询 支持多条件
     * @param params
     * @return
     */
    List<ProjectTesteeFile> getProjectTesteeFormImageList(Map<String, Object> params);
    
    List<ProjectTesteeFileVo> getProjectFileListForPage(String projectId, String resourceType, String searchValue, String sortField, String sortType, Integer pageNum, Integer pageSize);
    
    ProjectTesteeFile checkFileRecordRepeat(String projectId, String fileName, String fileNumber, String version, String createUserId);
}