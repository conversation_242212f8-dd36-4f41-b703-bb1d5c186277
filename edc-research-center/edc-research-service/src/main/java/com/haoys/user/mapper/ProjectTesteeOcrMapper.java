package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeOcr;
import com.haoys.user.model.ProjectTesteeOcrExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeOcrMapper {
    long countByExample(ProjectTesteeOcrExample example);

    int deleteByExample(ProjectTesteeOcrExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeOcr record);

    int insertSelective(ProjectTesteeOcr record);

    List<ProjectTesteeOcr> selectByExample(ProjectTesteeOcrExample example);

    ProjectTesteeOcr selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeOcr record, @Param("example") ProjectTesteeOcrExample example);

    int updateByExample(@Param("record") ProjectTesteeOcr record, @Param("example") ProjectTesteeOcrExample example);

    int updateByPrimaryKeySelective(ProjectTesteeOcr record);

    int updateByPrimaryKey(ProjectTesteeOcr record);

    ProjectTesteeOcr selectByFileId(String fileId);

    ProjectTesteeOcr getProjectTesteeNextImageByFileId(String fileId);
}