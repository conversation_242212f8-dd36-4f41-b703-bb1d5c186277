package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeSign;
import com.haoys.user.model.ProjectTesteeSignExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeSignMapper {
    long countByExample(ProjectTesteeSignExample example);

    int deleteByExample(ProjectTesteeSignExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeSign record);

    int insertSelective(ProjectTesteeSign record);

    List<ProjectTesteeSign> selectByExampleWithBLOBs(ProjectTesteeSignExample example);

    List<ProjectTesteeSign> selectByExample(ProjectTesteeSignExample example);

    ProjectTesteeSign selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeSign record, @Param("example") ProjectTesteeSignExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectTesteeSign record, @Param("example") ProjectTesteeSignExample example);

    int updateByExample(@Param("record") ProjectTesteeSign record, @Param("example") ProjectTesteeSignExample example);

    int updateByPrimaryKeySelective(ProjectTesteeSign record);

    int updateByPrimaryKeyWithBLOBs(ProjectTesteeSign record);

    int updateByPrimaryKey(ProjectTesteeSign record);

    String getMaxTesteeCode(Long projectId);
    
    ProjectTesteeSign getProjectTesteeSign(Long projectId, String testeeCode);
}
