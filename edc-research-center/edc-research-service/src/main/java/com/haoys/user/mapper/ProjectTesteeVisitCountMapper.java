package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeVisitCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目参与者访视统计 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper
public interface ProjectTesteeVisitCountMapper {

    /**
     * 根据主键查询记录
     */
    ProjectTesteeVisitCount selectByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(ProjectTesteeVisitCount record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ProjectTesteeVisitCount record);

    /**
     * 选择性更新记录（根据主键）
     */
    int updateByPrimaryKeySelective(ProjectTesteeVisitCount record);

    /**
     * 根据主键更新记录
     */
    int updateByPrimaryKey(ProjectTesteeVisitCount record);

    /**
     * 根据主键删除记录
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 批量插入记录
     */
    int batchInsert(@Param("list") List<ProjectTesteeVisitCount> list);

    /**
     * 批量更新记录
     */
    int batchUpdate(@Param("list") List<ProjectTesteeVisitCount> list);

    /**
     * 根据项目ID删除记录
     */
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 多条件查询
     */
    List<ProjectTesteeVisitCount> listByMultipleConditions(@Param("conditions") Map<String, Object> conditions);

    /**
     * 复杂查询
     */
    List<ProjectTesteeVisitCount> complexQuery(@Param("queryParams") Map<String, Object> queryParams);

    /**
     * 分页复杂查询
     */
    List<ProjectTesteeVisitCount> complexPageQuery(@Param("queryParams") Map<String, Object> queryParams);

    /**
     * 根据条件查询参与者访视统计列表
     */
    List<ProjectTesteeVisitCount> queryTesteesByConditions(
            @Param("testeeCode") String testeeCode,
            @Param("submitStatus") String submitStatus,
            @Param("reviewStatus") String reviewStatus,
            @Param("projectId") Long projectId);

    /**
     * 查询未审核的参与者列表（未提交或已提交但未完成审核）
     */
    List<ProjectTesteeVisitCount> queryUnreviewedTestees(@Param("projectId") Long projectId, @Param("testeeCode") String testeeCode);

    /**
     * 根据项目ID和参与者ID查询记录
     */
    ProjectTesteeVisitCount selectByProjectAndTesteeAndVisit(
            @Param("projectId") Long projectId,
            @Param("testeeId") Long testeeId,
            @Param("visitId") Long visitId);

    /**
     * 统计记录数
     */
    long countByConditions(@Param("conditions") Map<String, Object> conditions);

    /**
     * 获取医学审核管理列表 - 高性能查询
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @param submitStatus 提交状态（可选）
     * @param reviewStatus 审核状态（可选）
     * @param unreviewedOnly 是否只查询未审核的记录
     * @return 医学审核管理列表
     */
    List<ProjectTesteeVisitCount> getMedicalReviewList(@Param("projectId") Long projectId,
                                                       @Param("testeeCode") String testeeCode,
                                                       @Param("submitStatus") String submitStatus,
                                                       @Param("reviewStatus") String reviewStatus,
                                                       @Param("unreviewedOnly") Boolean unreviewedOnly);
}
