package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.TesteeInfoVo;
import com.haoys.user.model.RctsTesteeInfo;
import com.haoys.user.model.RctsTesteeInfoExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface RctsTesteeInfoMapper {
    long countByExample(RctsTesteeInfoExample example);

    int deleteByExample(RctsTesteeInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsTesteeInfo record);

    int insertSelective(RctsTesteeInfo record);

    List<RctsTesteeInfo> selectByExample(RctsTesteeInfoExample example);

    RctsTesteeInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsTesteeInfo record, @Param("example") RctsTesteeInfoExample example);

    int updateByExample(@Param("record") RctsTesteeInfo record, @Param("example") RctsTesteeInfoExample example);

    int updateByPrimaryKeySelective(RctsTesteeInfo record);

    int updateByPrimaryKey(RctsTesteeInfo record);


    List<TesteeInfoVo> getTesteeInfoPage(Map<String, Object> params);

    RctsTesteeInfo getRctsTesteeInfo(Long projectId, Long projectOrgId, Long testeeId);

    @Select(" SELECT COUNT(*) FROM rcts_testee_info WHERE project_id = #{projectId} AND testee_id = #{testeeId} ")
    int getTesteeCountByTesteeId(@Param("projectId") Long projectId, @Param("testeeId") Long testeeId);

    @Select(" SELECT * FROM rcts_testee_info WHERE project_id = #{projectId} AND testee_id = #{testeeId} ")
    RctsTesteeInfo getTesteeInfo(@Param("projectId") Long projectId, @Param("testeeId") Long testeeId);

    @Select(" SELECT concat(testee_id,'') testeeId, testee_code testeeCode, acronym FROM rcts_testee_info  WHERE project_id = #{projectId} AND testee_code LIKE CONCAT('%',#{testeeCode},'%') ")
    List<Map<String, String>> getTesteeSelection(@Param("projectId") Long projectId, @Param("testeeCode") String testeeCode);

}