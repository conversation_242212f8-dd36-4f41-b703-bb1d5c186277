package com.haoys.user.mapper;

import com.haoys.user.domain.vo.system.SendMessageRecordVo;
import com.haoys.user.model.SendMessageRecord;
import com.haoys.user.model.SendMessageRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SendMessageRecordMapper {
    long countByExample(SendMessageRecordExample example);

    int deleteByExample(SendMessageRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SendMessageRecord record);

    int insertSelective(SendMessageRecord record);

    List<SendMessageRecord> selectByExample(SendMessageRecordExample example);

    SendMessageRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SendMessageRecord record, @Param("example") SendMessageRecordExample example);

    int updateByExample(@Param("record") SendMessageRecord record, @Param("example") SendMessageRecordExample example);

    int updateByPrimaryKeySelective(SendMessageRecord record);

    int updateByPrimaryKey(SendMessageRecord record);
    
    List<SendMessageRecordVo> getSendMessageListForPage(String mobile);
}