package com.haoys.user.mapper;

import com.haoys.user.domain.entity.SystemLoginLogQuery;
import com.haoys.user.domain.vo.system.SystemLoginLogVo;
import com.haoys.user.model.SystemLoginLog;
import com.haoys.user.model.SystemLoginLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemLoginLogMapper {
    long countByExample(SystemLoginLogExample example);

    int deleteByExample(SystemLoginLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemLoginLog record);

    int insertSelective(SystemLoginLog record);

    List<SystemLoginLog> selectByExample(SystemLoginLogExample example);

    SystemLoginLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemLoginLog record, @Param("example") SystemLoginLogExample example);

    int updateByExample(@Param("record") SystemLoginLog record, @Param("example") SystemLoginLogExample example);

    int updateByPrimaryKeySelective(SystemLoginLog record);

    int updateByPrimaryKey(SystemLoginLog record);

    int deleteLogininforByIds(Long[] ids);

    int cleanLogininfor();

    List<SystemLoginLogVo> selectList(SystemLoginLogQuery logininfor);

    List<SystemLoginLog> selectSystemLoginLogList(SystemLoginLogQuery logininfor);

}