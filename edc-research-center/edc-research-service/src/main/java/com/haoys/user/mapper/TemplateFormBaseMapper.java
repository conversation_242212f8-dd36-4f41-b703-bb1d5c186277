package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormConfig;

import java.util.List;
import java.util.Map;

public interface TemplateFormBaseMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormConfig record);

    int insertSelective(TemplateFormConfig record);

    TemplateFormConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKey(TemplateFormConfig record);
    
    Map<String, Object> getSystemQueryReturnRecord(String queryConditionValue);
    
    List<Map<String, Object>> getSystemQueryReturnTableRecord(String queryConditionValue);
}