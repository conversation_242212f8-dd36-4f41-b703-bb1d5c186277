package com.haoys.user.mapper;

import com.haoys.user.domain.expand.TemplateLabConfigExpand;
import com.haoys.user.domain.param.lab.TemplateLabConfigParam;
import com.haoys.user.domain.vo.lab.TemplateLabConfigVo;
import com.haoys.user.model.TemplateLabConfig;
import com.haoys.user.model.TemplateLabConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateLabConfigMapper {
    long countByExample(TemplateLabConfigExample example);

    int deleteByExample(TemplateLabConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateLabConfig record);

    int insertSelective(TemplateLabConfig record);

    List<TemplateLabConfig> selectByExample(TemplateLabConfigExample example);

    TemplateLabConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateLabConfig record, @Param("example") TemplateLabConfigExample example);

    int updateByExample(@Param("record") TemplateLabConfig record, @Param("example") TemplateLabConfigExample example);

    int updateByPrimaryKeySelective(TemplateLabConfig record);

    int updateByPrimaryKey(TemplateLabConfig record);
    
    List<TemplateLabConfigVo> getProjectTemplateLabConfigListForPage(String projectId, String variableName, String labType, String systemTenantId);
    
    TemplateLabConfigExpand getTemplateLabConfigByVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByVariableIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId, String tableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId);
    
    TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId, String tableId);
    
    TemplateLabConfig checkTemplateLabConfigParam(TemplateLabConfigParam templateLabConfigParam);
}