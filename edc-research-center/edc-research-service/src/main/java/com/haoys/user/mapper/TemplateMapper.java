package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateVo;
import com.haoys.user.model.Template;
import com.haoys.user.model.TemplateExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface TemplateMapper {
    long countByExample(TemplateExample example);

    int deleteByExample(TemplateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Template record);

    int insertSelective(Template record);

    List<Template> selectByExample(TemplateExample example);

    Template selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Template record, @Param("example") TemplateExample example);

    int updateByExample(@Param("record") Template record, @Param("example") TemplateExample example);

    int updateByPrimaryKeySelective(Template record);

    int updateByPrimaryKey(Template record);

    /**
     * 查询项目模版分页列表
     * @param params
     * @return
     */
    List<TemplateVo> getProjectTemplateListForPage(Map<String, Object> params);

    Template getTemplateBaseInfoByTemplateName(String projectId, String templateName);
}