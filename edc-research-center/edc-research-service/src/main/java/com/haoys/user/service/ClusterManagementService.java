package com.haoys.user.service;

import com.haoys.user.common.config.ClusterConfig;
import com.haoys.user.domain.cluster.ServiceInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 集群管理服务
 * 参照Nacos的NamingService实现
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
public class ClusterManagementService {

    @Autowired
    private ClusterConfig clusterConfig;

    @Value("${spring.application.name:edc-research-api}")
    private String applicationName;

    @Value("${server.port:8000}")
    private Integer serverPort;

    @Value("${spring.profiles.active:local}")
    private String activeProfile;

    /**
     * 服务实例注册表
     * Key: serviceName, Value: Map<instanceId, ServiceInstance>
     */
    private final Map<String, Map<String, ServiceInstance>> serviceRegistry = new ConcurrentHashMap<>();

    /**
     * 心跳检测线程池
     */
    private ScheduledExecutorService heartbeatExecutor;

    /**
     * 健康检查线程池
     */
    private ScheduledExecutorService healthCheckExecutor;

    /**
     * 当前节点实例
     */
    private ServiceInstance currentInstance;

    /**
     * 主节点实例
     */
    private ServiceInstance masterInstance;

    @PostConstruct
    public void init() {
        if (!clusterConfig.isEnabled()) {
            log.info("集群模式未启用，跳过集群管理服务初始化");
            return;
        }

        log.info("================================================================================");
        log.info("🚀 初始化集群管理服务");
        log.info("================================================================================");

        // 初始化线程池
        initThreadPools();

        // 初始化当前节点
        initCurrentNode();

        // 注册当前实例
        registerCurrentInstance();

        // 初始化集群节点
        initClusterNodes();

        // 启动心跳检测
        startHeartbeatTask();

        // 启动健康检查
        startHealthCheckTask();

        log.info("✅ 集群管理服务初始化完成");
        log.info("📊 当前节点: {} ({})", currentInstance.getDisplayName(), currentInstance.getNodeType());
        log.info("🔗 集群节点数: {}", clusterConfig.getNodes() != null ? clusterConfig.getNodes().size() : 0);
        log.info("================================================================================");
    }

    /**
     * 初始化线程池
     */
    private void initThreadPools() {
        heartbeatExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "cluster-heartbeat");
            thread.setDaemon(true);
            return thread;
        });

        healthCheckExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "cluster-health-check");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 初始化当前节点
     */
    private void initCurrentNode() {
        try {
            ClusterConfig.NodeConfig currentNodeConfig = clusterConfig.getCurrent();
            
            // 自动检测IP地址
            if (currentNodeConfig.getIp() == null || currentNodeConfig.getIp().trim().isEmpty()) {
                currentNodeConfig.setIp(InetAddress.getLocalHost().getHostAddress());
            }
            
            // 设置端口
            if (currentNodeConfig.getPort() == null) {
                currentNodeConfig.setPort(serverPort);
            }
            
            // 生成节点ID
            if (currentNodeConfig.getNodeId() == null || currentNodeConfig.getNodeId().trim().isEmpty()) {
                currentNodeConfig.setNodeId(generateNodeId());
            }
            
            // 设置节点名称
            if (currentNodeConfig.getNodeName() == null || currentNodeConfig.getNodeName().trim().isEmpty()) {
                currentNodeConfig.setNodeName(String.format("%s-%s", applicationName, currentNodeConfig.getNodeId()));
            }

            // 创建当前实例
            currentInstance = createServiceInstance(currentNodeConfig);
            currentInstance.setServiceName(applicationName);
            currentInstance.setEnvironment(activeProfile);
            currentInstance.setRegisterTime(LocalDateTime.now());
            currentInstance.updateHeartbeat();

            log.info("🔧 当前节点配置: {}", currentNodeConfig);
        } catch (Exception e) {
            log.error("初始化当前节点失败", e);
            throw new RuntimeException("初始化当前节点失败", e);
        }
    }

    /**
     * 生成节点ID
     */
    private String generateNodeId() {
        return String.format("%s-%d-%s", 
            applicationName, 
            System.currentTimeMillis() % 100000,
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 创建服务实例
     */
    private ServiceInstance createServiceInstance(ClusterConfig.NodeConfig nodeConfig) {
        ServiceInstance instance = new ServiceInstance();
        instance.setInstanceId(nodeConfig.getNodeId());
        instance.setNodeId(nodeConfig.getNodeId());
        instance.setNodeName(nodeConfig.getNodeName());
        instance.setNodeType(nodeConfig.getNodeType());
        instance.setIp(nodeConfig.getIp());
        instance.setPort(nodeConfig.getPort());
        // 简化配置，移除weight字段
        instance.setEnabled(nodeConfig.isEnabled());
        // 简化配置，移除不必要的字段
        
        return instance;
    }

    /**
     * 注册当前实例
     */
    private void registerCurrentInstance() {
        registerInstance(currentInstance);
        log.info("✅ 当前实例已注册: {}", currentInstance.getUniqueKey());
    }

    /**
     * 初始化集群节点
     */
    private void initClusterNodes() {
        if (clusterConfig.getNodes() == null || clusterConfig.getNodes().isEmpty()) {
            log.warn("⚠️ 未配置集群节点列表");
            return;
        }

        for (ClusterConfig.NodeConfig nodeConfig : clusterConfig.getNodes()) {
            if (!nodeConfig.isEnabled()) {
                continue;
            }

            ServiceInstance instance = createServiceInstance(nodeConfig);
            instance.setServiceName(applicationName);
            instance.setEnvironment(activeProfile);
            instance.setRegisterTime(LocalDateTime.now());
            
            // 如果是主节点，记录主节点实例
            if (nodeConfig.getNodeType() == ClusterConfig.NodeType.MASTER) {
                masterInstance = instance;
                log.info("🎯 发现主节点: {}", instance.getDisplayName());
            }
            
            // 注册节点实例
            registerInstance(instance);
            log.info("📝 注册集群节点: {} ({})", instance.getDisplayName(), instance.getNodeType());
        }
    }

    /**
     * 注册服务实例
     */
    public void registerInstance(ServiceInstance instance) {
        String serviceName = instance.getServiceName();
        String instanceId = instance.getInstanceId();
        
        serviceRegistry.computeIfAbsent(serviceName, k -> new ConcurrentHashMap<>())
                     .put(instanceId, instance);
        
        log.debug("📝 注册服务实例: {} -> {}", serviceName, instanceId);
    }

    /**
     * 注销服务实例
     */
    public void deregisterInstance(String serviceName, String instanceId) {
        Map<String, ServiceInstance> instances = serviceRegistry.get(serviceName);
        if (instances != null) {
            ServiceInstance removed = instances.remove(instanceId);
            if (removed != null) {
                log.info("🗑️ 注销服务实例: {} -> {}", serviceName, instanceId);
            }
        }
    }

    /**
     * 获取服务实例列表
     */
    public List<ServiceInstance> getInstances(String serviceName) {
        Map<String, ServiceInstance> instances = serviceRegistry.get(serviceName);
        if (instances == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(instances.values());
    }

    /**
     * 获取健康的服务实例列表
     */
    public List<ServiceInstance> getHealthyInstances(String serviceName) {
        return getInstances(serviceName).stream()
                .filter(ServiceInstance::isOnline)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有服务名称
     */
    public Set<String> getAllServiceNames() {
        return new HashSet<>(serviceRegistry.keySet());
    }

    /**
     * 获取集群概览信息
     */
    public Map<String, Object> getClusterOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 统计所有实例
        int totalInstances = 0;
        int onlineInstances = 0;
        int healthyInstances = 0;
        int masterInstances = 0;
        int slaveInstances = 0;
        
        for (Map<String, ServiceInstance> instances : serviceRegistry.values()) {
            for (ServiceInstance instance : instances.values()) {
                totalInstances++;
                
                if (instance.isOnline()) {
                    onlineInstances++;
                }
                
                if (instance.isHealthy()) {
                    healthyInstances++;
                }
                
                if (instance.getNodeType() == ClusterConfig.NodeType.MASTER) {
                    masterInstances++;
                } else {
                    slaveInstances++;
                }
            }
        }
        
        overview.put("totalInstances", totalInstances);
        overview.put("onlineInstances", onlineInstances);
        overview.put("healthyInstances", healthyInstances);
        overview.put("masterInstances", masterInstances);
        overview.put("slaveInstances", slaveInstances);
        overview.put("serviceCount", serviceRegistry.size());
        overview.put("currentNode", currentInstance);
        overview.put("masterNode", masterInstance);
        overview.put("clusterEnabled", clusterConfig.isEnabled());
        overview.put("lastUpdateTime", LocalDateTime.now());
        
        return overview;
    }

    /**
     * 启动心跳检测任务
     */
    private void startHeartbeatTask() {
        if (!clusterConfig.getHeartbeat().isEnabled()) {
            log.info("心跳检测已禁用");
            return;
        }

        int interval = clusterConfig.getHeartbeat().getInterval();
        heartbeatExecutor.scheduleWithFixedDelay(this::performHeartbeat, 
            interval, interval, TimeUnit.SECONDS);
        
        log.info("💓 心跳检测任务已启动，间隔: {}秒", interval);
    }

    /**
     * 执行心跳检测
     */
    private void performHeartbeat() {
        try {
            // 更新当前实例心跳
            currentInstance.updateHeartbeat();
            currentInstance.markAsUp(); // 确保当前实例始终在线

            // 检查其他实例心跳超时
            checkHeartbeatTimeout();

            log.debug("💓 心跳检测完成");
        } catch (Exception e) {
            log.error("心跳检测失败", e);
        }
    }

    /**
     * 检查心跳超时 - 修复版本，通过HTTP检查其他节点状态
     */
    private void checkHeartbeatTimeout() {
        for (Map<String, ServiceInstance> instances : serviceRegistry.values()) {
            for (ServiceInstance instance : instances.values()) {
                // 跳过当前实例
                if (instance.equals(currentInstance)) {
                    continue;
                }

                // 通过HTTP检查其他节点是否在线
                boolean isNodeOnline = checkNodeOnlineStatus(instance);

                if (isNodeOnline) {
                    // 节点在线，更新状态
                    instance.updateHeartbeat();
                    instance.markAsUp();
                    if (instance.getStatus() == ServiceInstance.InstanceStatus.DOWN) {
                        log.info("✅ 节点恢复在线: {}", instance.getDisplayName());
                    }
                } else {
                    // 节点离线，标记为下线
                    if (instance.getStatus() != ServiceInstance.InstanceStatus.DOWN) {
                        instance.markAsDown();
                        log.warn("⚠️ 节点离线，标记为下线: {}", instance.getDisplayName());
                    }
                }
            }
        }
    }

    /**
     * 检查节点在线状态 - 通过HTTP请求检查
     */
    private boolean checkNodeOnlineStatus(ServiceInstance instance) {
        try {
            // 修复：使用基本API端点而不是actuator（工作节点没有actuator）
            String healthUrl = String.format("http://%s:%d/api/",
                instance.getIp(), instance.getPort());

            // 简单的HTTP连接检查（超时时间短）
            java.net.URL url = new java.net.URL(healthUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(2000); // 2秒连接超时
            connection.setReadTimeout(2000);    // 2秒读取超时

            int responseCode = connection.getResponseCode();
            connection.disconnect();

            // 200-299范围内的响应码认为是在线
            boolean isOnline = responseCode >= 200 && responseCode < 300;
            log.debug("节点 {} 健康检查: {} (响应码: {})", instance.getDisplayName(),
                isOnline ? "在线" : "离线", responseCode);
            return isOnline;

        } catch (Exception e) {
            // 连接失败认为节点离线
            log.debug("节点 {} 健康检查失败: {}", instance.getDisplayName(), e.getMessage());
            return false;
        }
    }

    /**
     * 启动健康检查任务
     */
    private void startHealthCheckTask() {
        int interval = clusterConfig.getDiscovery().getHealthCheckInterval();
        healthCheckExecutor.scheduleWithFixedDelay(this::performHealthCheck, 
            interval, interval, TimeUnit.SECONDS);
        
        log.info("🏥 健康检查任务已启动，间隔: {}秒", interval);
    }

    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            // 这里可以实现具体的健康检查逻辑
            // 例如：HTTP健康检查、数据库连接检查等
            log.debug("🏥 健康检查完成");
        } catch (Exception e) {
            log.error("健康检查失败", e);
        }
    }

    /**
     * 获取当前实例
     */
    public ServiceInstance getCurrentInstance() {
        return currentInstance;
    }

    /**
     * 获取主节点实例
     */
    public ServiceInstance getMasterInstance() {
        return masterInstance;
    }

    /**
     * 是否为主节点
     */
    public boolean isMasterNode() {
        return currentInstance != null && 
               currentInstance.getNodeType() == ClusterConfig.NodeType.MASTER;
    }

    @PreDestroy
    public void destroy() {
        log.info("🛑 关闭集群管理服务");
        
        if (heartbeatExecutor != null) {
            heartbeatExecutor.shutdown();
        }
        
        if (healthCheckExecutor != null) {
            healthCheckExecutor.shutdown();
        }
        
        // 注销当前实例
        if (currentInstance != null) {
            deregisterInstance(currentInstance.getServiceName(), currentInstance.getInstanceId());
        }
        
        log.info("✅ 集群管理服务已关闭");
    }
}
