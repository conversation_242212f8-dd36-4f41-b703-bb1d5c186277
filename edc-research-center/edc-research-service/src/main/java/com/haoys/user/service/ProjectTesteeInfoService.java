package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.expand.ProjectVisitUserExpand;
import com.haoys.user.domain.param.crf.ProjectTesteeFormProcessParam;
import com.haoys.user.domain.param.project.ProjectTesteeBatchUploadParam;
import com.haoys.user.domain.param.project.ProjectTesteeCustomTableParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableColumnParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.project.ProjectTesteeViewConfigParam;
import com.haoys.user.domain.param.testee.ProjectTesteeFillInfoParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.participant.ParticipantHeadRowViewVo;
import com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeVisitPercentVo;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeBaseVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportExceptionVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeImportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeViewConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.domain.vo.testee.TesteeChallengeVo;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeProcess;
import com.haoys.user.model.ProjectVisitUser;

import java.util.List;
import java.util.Map;

public interface ProjectTesteeInfoService {


    /**
     * 编辑保存参与者信息
     * @param projectTesteeParam
     * @return
     */
    CustomResult saveProjectTesteeBaseInfo(ProjectTesteeParam projectTesteeParam);

    /**
     * 查询参与者基本信息
     * @param   projectId   项目id
     * @param   testeeId    参与者id
     * @return
     */
    ProjectTesteeVo getProjectTesteeBaseInfo(String projectId, String testeeId);

    /**
     * 查询参与者分页列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeVo> getProjectTesteeListForPage(String projectId, String code, String realName, String orgId,
                                                            String ownerDoctor, String status, String reviewStatus,
                                                            String sortField, String sortType, Integer pageNum, Integer pageSize);


    /**
     * 查询参与者基本信息分页列表
     * @param projectId
     * @param projectOrgId  研究中心id
     * @param testeeCode    参与者编号
     * @param status        研究状态
     * @param sortField     排序字段 create_time。。。
     * @param sortType      排序方式 DESC ASC
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeVo> getProjectTesteeBaseListForPage(String projectId, String projectOrgId, String testeeCode, String status,
                                                                String sortField, String sortType, Integer pageNum, Integer pageSize);


    /**
     * 查询参与者列表
     * @param projectId
     * @param testeeCode
     * @param projectOrgId
     * @return
     */
    List<ProjectTesteeWrapperVo> getProjectTesteeBaseInfoList(String projectId, String testeeCode, String projectOrgId);



    /**
     * 参与者列表图形视图专用，[注意]禁止和参与者列表共用逻辑
     * @param projectId
     * @param testeeCode
     * @param projectOrgId
     * @param status
     * @param sortField
     * @param sortType
     * @return
     */
    List<ProjectTesteeWrapperVo> getProjectTesteeBaseDataViewList(String projectId, String testeeCode, String projectOrgId, String status,String researchStatus,
                                                                  String sortField, String sortType);


    /**
     * 查询参与者管理分页列表
     *
     * @param participantHeadRowViewVo
     * @param projectId
     * @param projectOrgId
     * @param testeeCode
     * @param realName
     * @param enableAppDevice
     * @param inheritor
     * @param Instructor
     * @param Batchnumber
     * @param REDATE
     * @param status
     * @param researchStatus
     * @param sortType
     * @param sortField
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectParticipantViewConfigVo> getProjectParticipantListForPage(ParticipantHeadRowViewVo participantHeadRowViewVo, String projectId, String projectOrgId, String testeeCode, String realName, String enableAppDevice, String inheritor, String Instructor, String Batchnumber, String REDATE, String status, String researchStatus, String sortType, String sortField, Integer pageNum, Integer pageSize);

    /**
     * 查询参与者基本信息分页列表
     * @param projectId
     * @param projectOrgId
     * @param testeeCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeVo> getProjectTesteeBaseInfoListForPage(String projectId, String projectOrgId, String testeeCode, Integer pageNum, Integer pageSize);



    /**
     * 导出参与者分页列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param conditionValue
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeExportViewVo> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String status,
                                                                            String sortField, String sortType, String conditionValue, Integer pageNum, Integer pageSize);


    /**
     * 查询项目患者分页列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param conditionValue
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeExportViewVo> getExportProjectTesteeListForPageVersion2(String projectId, String code, String realName, String orgId, String ownerDoctor, String status,
                                                                                    String sortField, String sortType, String conditionValue, Integer pageNum, Integer pageSize);


    /**
     * 参与者审核列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param reviewStatus
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectTesteeViewConfigVo> getTesteeReviewListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String reviewStatus, Integer pageNum, Integer pageSize);


    /**
     * 查询对应访视表单项列表
     * @param templateId
     * @param projectId
     * @param planId
     * @param visitId
     * @return
     */
    List<TemplateFormConfigVo> getTesteeVisitFormList(String templateId, String projectId, String planId, String visitId);

    /**
     * 查询表单项所有表单组件列表及详情信息
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param variableTableId
     * @param projectOrgId
     * @param testeeId
     * @param tableSort
     * @param enableAppDevice
     * @return
     */
    List<TemplateFormDetailVo> getTesteeVisitFormDetail(String projectId, String planId, String visitId, String formId, String formExpandId, String variableTableId, String projectOrgId, String testeeId, String tableSort, String enableAppDevice);

    /**
     * 查询表格详情记录
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param variableGroupTableId
     * @param variableTableId
     * @param testeeId
     * @param tableSort
     * @return
     */
    List<TemplateFormDetailVo> getTesteeVisitFormTableDetailForSync(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String variableId, String variableGroupTableId, String variableTableId, String testeeId, String enableAppDevice, String tableSort);

    /**
     * 保存参与者普通表单变量录入数据
     * @param projectTesteeResultParam
     * @return
     */
    CustomResult saveTesteeVisitFormDetail(ProjectTesteeResultParam projectTesteeResultParam);

    /**
     * 保存编辑表格行记录数据
     * @param projectTesteeTableParam
     * @return
     */
    CustomResult saveProjectTesteeTableRowRecord(ProjectTesteeTableParam projectTesteeTableParam);


    /**
     * 批量保存表格列数据
     * @param projectTesteeTableColumnParam
     * @return
     */
    CustomResult saveProjectTesteeTableColumnRecord(ProjectTesteeTableColumnParam projectTesteeTableColumnParam);


    /**
     * 更新表单录入进度
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param testeeId
     * @param status
     * @param operator
     * @param complateStatus
     * @param formComplateStatus
     * @param changleCount
     * @param tanantId
     * @param platformId
     * @return
     */
    String saveProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId,
                                        String status, String operator, String complateStatus, String formComplateStatus, String changleCount, String tanantId, String platformId);

    /**
     * 更新参与者表单录入进度
     * @param projectTesteeFormProcessParam
     * @return
     */
    CustomResult modifyTesteeVisitFormProcess(ProjectTesteeFormProcessParam projectTesteeFormProcessParam);

    /**
     * 查询参与者表单项提交结果列表-科研数据分析平台
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeVo> getTesteeFormResultList(String projectId, String visitId, String testeeId);

    /**
     * 查询数据分析平台患者样本数据
     * @param projectId         项目id
     * @param planId            方案
     * @param projectOrgId      中心id-支持多中心
     * @return
     */
    List<ProjectTesteeVo> getProjectTesteeAnalysisListByProjectId(String projectId, String planId, String projectOrgId);

    /**
     * 查询访视表格详情内CRF图片
     * @param projectId
     * @param visitId
     * @param testeeId
     * @param formDetailId
     * @return
     */
    String getProjectTesteeTableCRF(String projectId, String visitId, String testeeId, String formDetailId);


    /**
     * 查询参与者访视质疑状态
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    TesteeChallengeVo getProjectVisitChallengeStatus(String projectId, String visitId, String testeeId);

    /**
     * 查询参与者表单项质疑状态
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    TesteeChallengeVo getProjectFormChallengeStatus(String projectId, String visitId, String formId, String testeeId);


    /**
     * 查询参与者表单变量质疑状态
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    TesteeChallengeVo getProjectFormDetailChallengeStatus(String projectId, String visitId, String formId, String formDetailId, String testeeId);


    /**
     * 更新参与者研究状态
     * @param projectId
     * @param ownerOrgId
     * @param testeeIds
     * @param researchStatus
     * @return
     */
    CustomResult updateProjectTesteeStatus(String projectId, String ownerOrgId, String testeeIds, String researchStatus);
    
    
    
    CustomResult updateProjectTesteeJoinGroupStatus(String projectId, String projectOrgId, String testeeId);
    
    
    CustomResult getTesteeJoinGroupInfo(String projectId, String projectOrgId, String testeeId);

    /**
     * 删除项目参与者
     * @param projectId
     * @param testeeId
     * @return
     */
    CustomResult removeTesteeBaseInfo(String projectId, String testeeId);


    /**
     * 查询参与者表单完成情况
     * @param projectId
     * @param testeeId
     * @return
     */
    CustomResult getTesteeProjectFormAndTableResult(String projectId, String testeeId);

    /**
     * 患者注册同步参与者基本信息
     * @param userId    系统用户id
     * @param mobile    参与者手机号
     * @param realName  参与者姓名
     */
    CustomResult saveProjectTesteeUserInfo(Long userId, String mobile, String realName);

    /**
     * 根据手机号查询参与者基本信息
     * @param projectId
     * @param mobile
     * @return
     */
    ProjectTesteeVo getProjectTesteeBaseInfoByMobile(String projectId, String mobile);

    /**
     * 参与者绑定审核
     * @param projectId
     * @param testeeId
     * @param testeeCode
     * @param status
     * @param bindUserId
     * @return
     */
    CustomResult updateTesteeReviewStatus(String projectId, String testeeId, String testeeCode, String status, String bindUserId);

    /**
     * 保存参与者基本信息绑定配置
     * @param projectTesteeViewConfigParam
     * @return
     */
    CustomResult saveTesteeReviewConfig(ProjectTesteeViewConfigParam projectTesteeViewConfigParam);

    /**
     * 患者扫码帮绑定
     * @param projectId         项目id
     * @param userId            用户id 操作人
     * @param testeeId          参与者id
     * @param realName          姓名
     * @param ownerOrgId        所属中心
     * @param ownerDoctorId     所属医生id
     * @param selfRecord        是否为自建病历
     * @return
     */
    CustomResult saveBindTesteeQRCode(String projectId, String userId, String testeeId, String realName, String ownerOrgId, String ownerDoctorId, String selfRecord);

    /**
     * 查询项目参与者信息
     *
     * @param projectId    项目id
     * @param projectOrgId
     * @param testeeId     参与者id
     * @return
     */
    ProjectVisitUser getProjectTesteeUserInfo(String projectId, String projectOrgId, String testeeId);
    
    /**
     * 查询项目参与者绑定列表
     *
     * @param projectId
     * @param testeeCode
     * @return
     */
    List<ProjectVisitUser> getProjectVisitUserList(String projectId, String testeeCode);

    /**
     * 项目绑定患者列表
     * @return
     */
    List<ProjectVisitUser> getProjectPatientUserList();

    /**
     * 患者随访目前产品约定只绑定一个项目，是否设置关联多项目和解绑操作 待需求更新
     * @param id
     * @return
     */
    ProjectVisitUser getPatientTaskProjectId(Long id);

    /**
     * 性别分布状态统计
     * @param projectId
     * @param operator
     * @return
     */
    List<Map<Integer, Object>> getProjectGenderData(String projectId, String operator);

    /**
     * 年龄区间分布统计
     * @param projectId
     * @param operator
     * @return
     */
    List<Map<Integer, Object>> getProjectAgeDistrbuteData(String projectId, String operator);

    /**
     * 查询项目参与者总量
     * @param projectId
     * @param userId
     * @param orgIds
     * @param queryOwnerOrgId
     * @return
     */
    int getProjectTesteeCount(String projectId, String userId, String orgIds, boolean queryOwnerOrgId);


    /**
     * 查询访视进度列表
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeProcess> getProjectTesteeProcessList(String projectId, String visitId, String testeeId);

    /**
     * 查询表单完成进度
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param testeeId
     * @return
     */
    ProjectTesteeProcess getProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId);

    /**
     * 查询访视完成进度
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param testeeId
     * @return
     */
    ProjectTesteeVisitPercentVo getProjectVisitTesteeComplateStatus(String projectId, String projectOrgId, String planId, String visitId, String testeeId);


    /**
     * 保存参与者Table表格初始化记录
     * @param projectTesteeCustomTableParam
     * @return
     */
    CustomResult saveProjectTesteeCustomTableRowRecord(ProjectTesteeCustomTableParam projectTesteeCustomTableParam);

    /**
     * 批量导入参与者
     * @param dataList
     * @param projectId
     * @param oprator
     * @param errorList
     * @return
     */
    String saveBatchProjectTesteeInfo(List<ProjectTesteeImportVo> dataList, String projectId, String oprator, List<ProjectTesteeExportExceptionVo> errorList);

    /**
     * 根据参与者code查询参与者基本信息
     * @param projectId
     * @param testeeCode
     * @return
     */
    ProjectTesteeVo getTesteeBaseInfoByTesteeCode(String projectId, String testeeCode);

    /**
     * 管理端-批量上传表单数据(图片)
     * @param projectTesteeResultParam
     * @param operator
     * @return
     */
    CustomResult saveBatchTesteeVisitFormDetail(List<ProjectTesteeBatchUploadParam> projectTesteeResultParam, String operator);

    /**
     * 计算普通表单录入情况
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param testeeId
     * @param status
     * @param operator
     * @param tenantId
     * @param platformId
     */
    void updateProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId, String status, String complateStatus, String operator, String tenantId, String platformId);

    /**
     * 更新参与者表格进度
     *
     * @param projectId
     * @param visitId
     * @param testeeId
     * @param status
     * @param operator
     */
    void updateProjectTesteeTableProcess(String projectId, String projectOrgId, String planId, String visitId, String testeeId, String status, String operator);

    /**
     * 根据变量记录id删除录入值
     * @param ids
     * @param opreator
     * @return
     */
    CustomResult deleteTesteeFormDetailById(String ids, String opreator);

    /**
     * 根据变量记录id更新详情信息
     * @param id
     * @param fieldValue
     * @param opreator
     * @return
     */
    CustomResult updateTesteeFormDetailById(String id, String fieldValue, String opreator);


    /**
     * 保存参与者专用表单
     * @param projectId
     */
    void initTesteeFormBaseInfo(String projectId);

    /**
     * 更新参与者表单变量-基本字段信息
     *
     * @param projectId
     * @param testeeId
     * @param operator
     * @param systemTenantId
     * @param systemPlatformId
     */
    void updateProjectTesteeBaseInfoVariableInputValues(String projectId, String testeeId, String operator, String systemTenantId, String systemPlatformId);

    /**
     * 获取参与者信息列表
     * @param param 搜索参数
     * @return 参与者信息列表
     */
    CommonPage<ProjectTesteeWrapperVo> getProjectTesteeDataViewList(ProjectTesteeFillInfoParam param);

    /**
     * 保存参与者基本信息
     * @param testeeInfo
     * @return
     */
    int saveProjectTesteeInfo(ProjectTesteeInfo testeeInfo);

    /**
     * 查询项目表单研究中心详情
     * @param projectId
     * @param projectOrgId
     * @param testeeId
     * @return
     */
    ProjectTesteeOrgVo getTesteeProjectOrgDetail(String projectId, String projectOrgId, String testeeId);

    List<ProjectTesteeInfo> getProjectTesteeListByIds(String projectId, String projectOrgId, List<String> testeeIds);

    /**
     * 移动端--编辑保存参与者信息
     * @param projectTesteeParam
     * @return
     */
    CustomResult saveMobileProjectTesteeBaseInfo(ProjectTesteeParam projectTesteeParam);

    List<ProjectTesteeOrgVo> getTesteeJoinOrgListByUserId(String projectId, String userId);
    
    ProjectTesteeBaseVo selectProjectTesteeInfoByUserId(String userId, String tenantId, String platformId);

    ProjectVisitUser getMobileProjectVisitUser(String projectId, String ownerOrgId, String testeeId);
    
    List<ProjectVisitUser> getProjectUncheckTesteeUserList(String projectId, String testeeCode);
    
    
    List<ProjectVisitUserExpand> getProjectTesteeUserListForBoRui(String projectId, String testeeCode, String realName);

    /**
     * 获取项目参与者列表（包含参与者姓名）- 用于访视统计
     */
    List<Map<String, Object>> getProjectTesteeUserListWithName(String projectId, String testeeCode);
}
