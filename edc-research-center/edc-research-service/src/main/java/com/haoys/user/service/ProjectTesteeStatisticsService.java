package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo;
import com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.expand.ProjectUserExpand;
import com.haoys.user.model.ProjectVisitConfig;

import java.util.List;

/**
 * 访视统计service
 */
public interface ProjectTesteeStatisticsService {
    
    
    CommonPage<ProjectTesteeStatisticsVo> list(ProjectTesteeStatisticsParam param);
    
    ProjectTesteeProcessCountVo getProjectTesteeProcessCount(String projectId, String visitId, String createUserId);
    
    ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditCount(String projectId, String visitId, String createUserId);
    
    int getProjectTesteeCount(String projectId, String createUserId);
    
    CommonPage<ProjectUserExpand> getProjectResearchUserListForPage(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String enterpriseId, String group, String realName, String username, Integer status, Boolean activeStatus, Boolean lockStatus, Integer pageNum, Integer pageSize);
    
    /**
     * 医学审核管理列表 - 默认查询未审核访视数据
     */
    CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize);

    /**
     * 医学审核管理列表 - 支持查询标识参数
     * @param projectVisitConfigList 访视配置列表
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param queryAll 查询标识：true-查询全部访视数据，false-仅查询未审核访视数据（默认）
     * @return 医学审核管理列表
     */
    CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize, Boolean queryAll);

    CommonPage<ProjectUserExpand> getProjectTesteeListForBoRui(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, String realName, Integer pageNum, Integer pageSize);
}
