package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.ProjectTesteeVisitCount;

import java.util.List;

/**
 * 项目参与者访视统计服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface ProjectTesteeVisitCountService {

    /**
     * 刷新项目医学审核列表统计数据
     * 计算每个参与者每次访视的提交状态和审核状态
     * 
     * @param projectId 项目ID
     * @return 操作结果
     */
    CommonResult<String> refreshProjectMedicalReviewList(String projectId);

    /**
     * 根据条件查询参与者访视统计列表
     * 
     * @param testeeCode 参与者编号
     * @param submitStatus 提交状态
     * @param reviewStatus 审核状态
     * @param projectId 项目ID
     * @return 查询结果
     */
    CommonResult<List<ProjectTesteeVisitCount>> queryTesteesByConditions(
            String testeeCode, String submitStatus, String reviewStatus, String projectId);

    /**
     * 查询未审核的参与者列表
     * 只查询整体访视未提交或者访视已经提交同时未完成审核的访视记录
     * 
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @return 未审核的参与者列表
     */
    List<ProjectTesteeVisitCount> queryUnreviewedTestees(String projectId, String testeeCode);

    /**
     * 根据主键查询记录
     * 
     * @param id 主键ID
     * @return 记录详情
     */
    ProjectTesteeVisitCount selectByPrimaryKey(Long id);

    /**
     * 插入记录
     * 
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(ProjectTesteeVisitCount record);

    /**
     * 选择性插入记录
     * 
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(ProjectTesteeVisitCount record);

    /**
     * 选择性更新记录
     * 
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ProjectTesteeVisitCount record);

    /**
     * 根据项目ID删除记录
     * 
     * @param projectId 项目ID
     * @return 影响行数
     */
    int deleteByProjectId(Long projectId);

    /**
     * 批量插入记录
     * 
     * @param list 记录列表
     * @return 影响行数
     */
    int batchInsert(List<ProjectTesteeVisitCount> list);

    /**
     * 批量更新记录
     *
     * @param list 记录列表
     * @return 影响行数
     */
    int batchUpdate(List<ProjectTesteeVisitCount> list);

    /**
     * 获取医学审核管理列表 - 高性能查询
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @param submitStatus 提交状态（可选）
     * @param reviewStatus 审核状态（可选）
     * @param unreviewedOnly 是否只查询未审核的记录
     * @return 医学审核管理列表
     */
    List<ProjectTesteeVisitCount> getMedicalReviewList(String projectId, String testeeCode,
                                                       String submitStatus, String reviewStatus,
                                                       Boolean unreviewedOnly);

    /**
     * 获取医学审核管理列表 - 支持全部访视提交状态和审核状态数据查询
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @param submitStatus 提交状态（可选）
     * @param reviewStatus 审核状态（可选）
     * @return 医学审核管理列表
     */
    List<ProjectTesteeVisitCount> getMedicalReviewListAll(String projectId, String testeeCode,
                                                          String submitStatus, String reviewStatus);
}
