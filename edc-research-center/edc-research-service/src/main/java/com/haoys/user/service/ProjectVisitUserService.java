package com.haoys.user.service;

import com.haoys.user.domain.param.testee.ProjectTesteeStatistVo;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.ProjectVisitUserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectVisitUserService {

    void insert(ProjectVisitUser projectVisitUser);

    void updateProjectVisitUserById(ProjectVisitUser projectVisitUserInfo);

    void updateByExample(ProjectVisitUser projectVisitUserVo, ProjectVisitUserExample example);

    void updateByExampleSelective(ProjectVisitUser projectVisitUser, ProjectVisitUserExample example);

    long countByExample(ProjectVisitUserExample example);

    List<ProjectVisitUser> selectByExample(ProjectVisitUserExample example);

    void deleteByExample(ProjectVisitUserExample example);

    ProjectTesteeStatistVo testeeStatist(Long projectId, Long orgId);


    Integer testeeStatistByDate(Long projectId, Long orgId,String createTime);

    ProjectVisitUser getProjectVisitUserInfo(String projectId, String ownerOrgId, String testeeId);

    String getMaxtesteeCode(@Param("projectId") Long projectId, @Param("orgId") Long orgId, @Param("prefix") String prefix);

    ProjectVisitUser getMobileProjectVisitUser(String projectId, String ownerOrgId, String testeeId);
    
    ProjectVisitUser getProjectTesteeUserByUserId(String projectId, String userId, String tenantId, String platformId);
    
    ProjectVisitUser getProjectDeleteVisitUserInfo(String projectId, String ownerOrgId, String testeeId);
    
    String getMaxTesteeCodeForBoRui(Long projectId);

    /**
     * 获取项目最大参与者编号（使用行锁确保并发安全）
     *
     * @param projectId 项目ID
     * @return 最大参与者编号
     */
    String getMaxTesteeCodeForBoRuiWithLock(Long projectId);
}
