package com.haoys.user.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.lock.DistributedLockAnnotation;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.ProjectTesteeSignMapper;
import com.haoys.user.model.ProjectTesteeSign;
import com.haoys.user.service.ProjectTesteeSignService;
import com.haoys.user.service.ProjectVisitUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
public class ProjectTesteeSignServiceImpl implements ProjectTesteeSignService {

    @Autowired
    private ProjectTesteeSignMapper projectTesteeSignMapper;
    @Autowired
    private ProjectVisitUserService projectVisitUserService;

    @Override
    public ProjectTesteeSign create(ProjectTesteeSign projectTesteeSign) {
        projectTesteeSign.setId(SnowflakeIdWorker.getUuid());
        projectTesteeSign.setCreateTime(new Date());
        projectTesteeSign.setCreateUser(SecurityUtils.getUserIdValue());
        projectTesteeSign.setTenantId(SecurityUtils.getSystemTenantId());
        projectTesteeSign.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectTesteeSign.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeSign.setTesteeCode(getNextTesteeCode(projectTesteeSign.getProjectId()));
        projectTesteeSignMapper.insertSelective(projectTesteeSign);
        return projectTesteeSign;
    }

    @Override
    public int update(ProjectTesteeSign projectTesteeSign) {
        ProjectTesteeSign projectTesteeSignValue = projectTesteeSignMapper.selectByPrimaryKey(projectTesteeSign.getId());
        BeanUtil.copyProperties(projectTesteeSign, projectTesteeSignValue);
        projectTesteeSignValue.setUpdateUser(SecurityUtils.getUserIdValue());
        projectTesteeSignValue.setUpdateTime(new Date());
        projectTesteeSignMapper.updateByPrimaryKeyWithBLOBs(projectTesteeSignValue);
        return 0;
    }

    @Override
    public int delete(Long signId) {
        return projectTesteeSignMapper.deleteByPrimaryKey(signId);
    }

    @Override
    public ProjectTesteeSign getProjectTesteeSignInfo(String projectId, String testeeCode) {
        return projectTesteeSignMapper.getProjectTesteeSign(NumberUtil.parseLong(projectId), testeeCode);
    }


    /**
     * 生成下一个参与者编号（线程安全，支持高并发）
     *
     * 重构说明：
     * 1. 使用分布式锁确保并发安全
     * 2. 使用数据库事务确保数据一致性
     * 3. 添加重试机制和异常处理
     * 4. 保证code连续性和位数一致性
     *
     * @param projectId 项目ID
     * @return 格式化的参与者编号（4位数字，如：0001, 0002）
     */
    @DistributedLockAnnotation(
        key = "testee:code:generation:#{#projectId}",
        waitTime = 10,
        leaseTime = 30,
        failMessage = "参与者编号生成繁忙，请稍后重试"
    )
    @Transactional(rollbackFor = Exception.class)
    public String getNextTesteeCode(Long projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        try {
            log.debug("开始生成参与者编号，项目ID: {}", projectId);

            // 获取当前最大编号（使用FOR UPDATE锁定记录）
            String maxTesteeCode = projectVisitUserService.getMaxTesteeCodeForBoRuiWithLock(projectId);

            // 处理空值情况
            if (maxTesteeCode == null || maxTesteeCode.trim().isEmpty()) {
                maxTesteeCode = "0";
            }

            // 解析当前最大编号
            int currentMaxCode;
            try {
                // 移除可能的前导零和非数字字符
                String cleanCode = maxTesteeCode.replaceAll("^0+", "");
                if (cleanCode.isEmpty()) {
                    cleanCode = "0";
                }
                currentMaxCode = Integer.parseInt(cleanCode);
            } catch (NumberFormatException e) {
                log.warn("解析最大参与者编号失败，使用默认值0，原值: {}", maxTesteeCode);
                currentMaxCode = 0;
            }

            // 生成下一个编号
            int nextCode = currentMaxCode + 1;

            // 格式化为4位数字（保证位数一致）
            String formattedCode = String.format("%04d", nextCode);

            log.debug("参与者编号生成成功，项目ID: {}, 当前最大: {}, 生成编号: {}",
                projectId, maxTesteeCode, formattedCode);

            return formattedCode;

        } catch (Exception e) {
            log.error("生成参与者编号失败，项目ID: {}", projectId, e);
            throw new RuntimeException("生成参与者编号失败: " + e.getMessage(), e);
        }
    }
}
