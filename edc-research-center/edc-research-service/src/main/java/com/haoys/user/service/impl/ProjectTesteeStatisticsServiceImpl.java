package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.domain.vo.testee.ProjectMedicalReviewVo;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.expand.ProjectVisitUserExpand;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo;
import com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeVisitPercentVo;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.expand.ProjectUserExpand;
import com.haoys.user.mapper.ProjectTesteeInfoMapper;
import com.haoys.user.mapper.ProjectUserOrgMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.ProjectTesteeVisitCount;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.ProjectResearchersInfoService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeStatisticsService;
import com.haoys.user.service.ProjectTesteeVisitCountService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectTesteeStatisticsServiceImpl  extends BaseService implements ProjectTesteeStatisticsService {

    @Autowired
    private ProjectTesteeInfoMapper projectTesteeInfoMapper;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;

    @Autowired
    private ProjectTesteeVisitCountService projectTesteeVisitCountService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private SystemDepartmentService systemDepartmentService;
    @Autowired
    private ProjectUserOrgMapper projectUserOrgMapper;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private ProjectResearchersInfoService projectResearchersInfoService;
    @Autowired
    private FlowFormSetService flowFormSetService;
    
    
    
    /**
     * 获取访视统计列表
     * @param param 搜索参数
     * @return 分页；列表
     */
    @Override
    public CommonPage<ProjectTesteeStatisticsVo> list(ProjectTesteeStatisticsParam param) {
        ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(param.getProjectId(), "", param.getUserId());
        Page<Object> page = null;
        List<ProjectTesteeStatisticsVo> projectCheckList=new ArrayList<>();
        if(projectUserDataVo != null){
            //String ename = projectUserDataVo.getRoleCode();
            String userId = param.getUserId();
            /*if(ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*){
                userId=null;
            }*/
            List<String> ids =  projectUserOrgMapper.getOrgIdsByProjectIdAndUserId(param.getProjectId(), userId, "");
            page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
            if (CollectionUtil.isNotEmpty(ids)){
                param.setIds(ids);
                // 获取用户拥有的所属中心的数据
                // 获取参与者和参与者关联的访视信息（一个参与者有多个访视信息）
                projectCheckList = projectTesteeInfoMapper.list(param);
                if (CollectionUtil.isNotEmpty(projectCheckList)) {
                    // 根据参与者获取访视访id集合
                    projectCheckList.forEach(vo -> {
                        // 查询访视进度
                        ProjectTesteeVisitPercentVo projectTesteeVisitPercent = projectTesteeInfoService.getProjectVisitTesteeComplateStatus(vo.getProjectId().toString(), "", "", vo.getVisitId().toString(), vo.getId().toString());
                        if(StringUtils.isNotEmpty(projectTesteeVisitPercent.getComplateVisitStatus())){
                            // 设置访视进度
                            vo.setViewPlan(FormVariableComplateStatus.getValue(projectTesteeVisitPercent.getComplateVisitStatus()));
                        }
                    });
                }
            }
        }
        return commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, projectCheckList);
    }
    
    @Override
    public ProjectTesteeProcessCountVo getProjectTesteeProcessCount(String projectId, String visitId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeProcessCount(projectId, visitId, createUserId);
    }
    
    @Override
    public ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditCount(String projectId, String visitId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeFormAuditCount(projectId, visitId, createUserId);
    }
    
    @Override
    public int getProjectTesteeCount(String projectId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeInputCount(projectId, createUserId);
    }
    
    /**
     * @param projectVisitConfigList
     * @param projectId
     * @param enterpriseId
     * @param group
     * @param realName
     * @param userName
     * @param status
     * @param activeStatus
     * @param lockStatus
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectResearchUserListForPage(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String enterpriseId, String group, String realName, String userName, Integer status, Boolean activeStatus, Boolean lockStatus, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
        
        if(StringUtils.isNotEmpty(userName)){
            systemUserInfoParam.setUsername(userName);
        }
        if(StringUtils.isNotEmpty(realName)){
            systemUserInfoParam.setRealName(realName);
        }
        if(StringUtils.isNotEmpty(enterpriseId)){
            systemUserInfoParam.setEnterprise(enterpriseId);
        }
        if(StringUtils.isNotEmpty(group)){
            systemUserInfoParam.setRoleDesc(group);
        }
        if (activeStatus != null) {
            systemUserInfoParam.setActiveStatus(activeStatus);
        }
        if (lockStatus != null) {
            if (lockStatus) {
                systemUserInfoParam.setLockStatus(lockStatus);
            }
        }
        if (lockStatus == null && status != null) {
            systemUserInfoParam.setStatus(status);
        }
        systemUserInfoParam.setCompanyOwnerUser(false);
        systemUserInfoParam.setRegisterFrom(Constants.USER_TYPE_VALUE_04);
        systemUserInfoParam.setSealFlag(false);
        systemUserInfoParam.setTenantId(SecurityUtils.getSystemTenantId());
        systemUserInfoParam.setPlatformId(SecurityUtils.getSystemPlatformId());
        List<SystemUserInfo> systemUserInfoList = systemUserInfoService.getSystemUserListForPage(systemUserInfoParam);
        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();
        for (SystemUserInfo systemUserInfo : systemUserInfoList) {
            String createUserId = systemUserInfo.getId().toString();
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            BeanUtils.copyProperties(systemUserInfo, projectUserExpand);
            if (StringUtils.isNotEmpty(projectUserExpand.getMobile())) {
                projectUserExpand.setMobile(DesensitizeUtil.aesDecrypt(projectUserExpand.getMobile()));
            }
            
            if(StringUtils.isNotEmpty(systemUserInfo.getRoleDesc())){
                Dictionary dictionaryGroupInfo = dictionaryService.getDictionaryInfo(systemUserInfo.getRoleDesc());
                if(dictionaryGroupInfo != null){
                    projectUserExpand.setGroupName(dictionaryGroupInfo.getName());
                }
            }
            
            if(StringUtils.isNotEmpty(systemUserInfo.getEnterprise())){
                SysDepartmentVo sysDepartmentVo = systemDepartmentService.getDepartment(NumberUtil.parseLong(systemUserInfo.getEnterprise()));
                if(sysDepartmentVo != null){
                    projectUserExpand.setRegionName(sysDepartmentVo.getName());
                }
            }
            
            ProjectResearchersInfo projectResearcherInfo = projectResearchersInfoService.getProjectResearcherInfo(createUserId);
            if(projectResearcherInfo != null){
                com.haoys.user.domain.entity.ProjectResearchersInfo projectResearchersInfo = new com.haoys.user.domain.entity.ProjectResearchersInfo();
                BeanUtils.copyProperties(projectResearcherInfo, projectResearchersInfo);
                projectUserExpand.setProjectResearchersInfo(projectResearchersInfo);
            }
            
            int projectTesteeCount = this.getProjectTesteeCount(projectId, createUserId);
            projectUserExpand.setTestee_count(projectTesteeCount);
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                String visitId = projectVisitConfig.getId().toString();
                
                // 患者统计
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitId);
                dynamicColumnHeadRowData.setVisitName(projectVisitConfig.getVisitName());
                
                // 查询当前访视下的参与者列表
                List<Integer> submitCount  = new ArrayList<>();
                List<Integer> checkCount  = new ArrayList<>();
                List<ProjectVisitUser> projectVisitUserList = projectTesteeInfoMapper.getProjectResearchOwnerTesteeList(projectId, visitId, createUserId);
                for (ProjectVisitUser projectVisitUser : projectVisitUserList) {
                    String testeeId = projectVisitUser.getTesteeId().toString();
                    ProjectTesteeProcessCountVo projectTesteeProcessCountVo = this.getProjectTesteeProcessCount(projectId, visitId, testeeId);
                    if(projectTesteeProcessCountVo != null){
                        submitCount.add(projectTesteeProcessCountVo.getSubmitCount());
                    }
                    ProjectTesteeFormAduitCountVo projectTesteeFormAduitCountVo = this.getProjectTesteeFormAuditCount(projectId, visitId, testeeId);
                    if(projectTesteeFormAduitCountVo != null){
                        checkCount.add(projectTesteeFormAduitCountVo.getCheckCount());
                    }
                }
                
                // 计算submitCount和checkCount的总和
                int submitCountSum = submitCount.stream().mapToInt(Integer::intValue).sum();
                int checkCountSum = checkCount.stream().mapToInt(Integer::intValue).sum();
                
                dynamicColumnHeadRowData.setSubmit_count(submitCountSum);
                dynamicColumnHeadRowData.setCheck_count(checkCountSum);
                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }
            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }
    
    /**
     * 医学审核管理列表 - 重构使用高性能查询
     * 基于预计算的统计数据，支持多种筛选条件
     *
     * @param projectVisitConfigList 访视配置列表
     * @param projectId 项目ID
     * @param testeeCode 参与者编号（可选）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param queryAll 查询标识：true-查询全部访视数据，false-仅查询未审核访视数据（默认）
     * @return 医学审核管理列表
     */
    public CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize, Boolean queryAll) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);

        // 根据查询标识决定查询模式
        List<ProjectTesteeVisitCount> statisticsList;
        if (queryAll != null && queryAll) {
            // 查询全部访视数据
            statisticsList = projectTesteeVisitCountService.getMedicalReviewListAll(
                    projectId, testeeCode, null, null);
        } else {
            // 默认查询未审核访视数据：不查询访视未提交和访视审核通过的数据
            // 即只查询：访视已提交但未审核的数据（排除未提交和已审核通过的）
            statisticsList = projectTesteeVisitCountService.getMedicalReviewList(
                    projectId, testeeCode, null, null, true);
        }

        // 按参与者分组统计数据，确保参与者在主列表中不重复
        Map<String, List<ProjectTesteeVisitCount>> testeeGroupMap = statisticsList.stream()
                .collect(Collectors.groupingBy(ProjectTesteeVisitCount::getTesteeCode));

        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();

        // 遍历每个参与者，构建参与者维度的数据
        for (Map.Entry<String, List<ProjectTesteeVisitCount>> entry : testeeGroupMap.entrySet()) {
            String currentTesteeCode = entry.getKey();
            List<ProjectTesteeVisitCount> testeeStatistics = entry.getValue();

            if (testeeStatistics.isEmpty()) {
                continue;
            }

            // 获取参与者基本信息（从第一条记录中获取）
            ProjectTesteeVisitCount firstRecord = testeeStatistics.get(0);
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            projectUserExpand.setTesteeCode(currentTesteeCode);
            projectUserExpand.setTesteeId(firstRecord.getTesteeId().toString());
            projectUserExpand.setProjectOrgId(firstRecord.getOwnerOrgId());
            projectUserExpand.setRealName(firstRecord.getTesteeName());

            // 构建该参与者的所有访视数据列表
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();

            // 为每个访视配置创建数据行，确保包含所有访视
            for (ProjectVisitConfig visitConfig : projectVisitConfigList) {
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitConfig.getId().toString());
                dynamicColumnHeadRowData.setVisitName(visitConfig.getVisitName());

                // 查找该参与者在该访视下的统计数据
                ProjectTesteeVisitCount matchedStatistics = testeeStatistics.stream()
                        .filter(stat -> stat.getVisitId().equals(visitConfig.getId()))
                        .findFirst()
                        .orElse(null);

                if (matchedStatistics != null) {
                    // 有统计数据，设置实际值
                    // 提交状态：1-已提交，0-未提交
                    int submitStatus = "SUBMITTED".equals(matchedStatistics.getSubmitStatus()) ? 1 : 0;
                    dynamicColumnHeadRowData.setSubmit_count(submitStatus);

                    // 审核状态：1-审核通过，0-未审核
                    int reviewStatus = "APPROVED".equals(matchedStatistics.getReviewStatus()) ? 1 : 0;
                    dynamicColumnHeadRowData.setCheck_count(reviewStatus);

                    // 设置详细统计信息
                    dynamicColumnHeadRowData.setTotal_count(matchedStatistics.getTotalForms() != null ? matchedStatistics.getTotalForms() : 0);
                    dynamicColumnHeadRowData.setTotalForms(matchedStatistics.getTotalForms());
                    dynamicColumnHeadRowData.setSubmittedForms(matchedStatistics.getSubmittedForms());
                    dynamicColumnHeadRowData.setApprovedForms(matchedStatistics.getApprovedForms());
                    dynamicColumnHeadRowData.setRejectedForms(matchedStatistics.getRejectedForms());
                    dynamicColumnHeadRowData.setPendingForms(matchedStatistics.getPendingForms());
                } else {
                    // 没有统计数据，设置默认值（表示该访视没有数据或不符合查询条件）
                    dynamicColumnHeadRowData.setSubmit_count(0);
                    dynamicColumnHeadRowData.setCheck_count(0);
                    dynamicColumnHeadRowData.setTotal_count(0);
                    dynamicColumnHeadRowData.setTotalForms(0);
                    dynamicColumnHeadRowData.setSubmittedForms(0);
                    dynamicColumnHeadRowData.setApprovedForms(0);
                    dynamicColumnHeadRowData.setRejectedForms(0);
                    dynamicColumnHeadRowData.setPendingForms(0);
                }

                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }

            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }

        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }

    /**
     * 医学审核管理列表 - 兼容性方法（保持原有接口不变）
     * 默认查询未审核访视数据
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize) {
        // 默认查询未审核访视数据
        return getProjectMedicalReviewList(projectVisitConfigList, projectId, testeeCode, pageNum, pageSize, false);
    }
    
    /**
     * @param projectVisitConfigList
     * @param projectId
     * @param testeeCode
     * @param realName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectTesteeListForBoRui(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, String realName, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectVisitUserExpand> projectVisitUserList = projectTesteeInfoService.getProjectTesteeUserListForBoRui(projectId, testeeCode, realName);
        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();
        for (ProjectVisitUserExpand projectVisitUser : projectVisitUserList) {
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            String testeeId = projectVisitUser.getTesteeId().toString();
            projectUserExpand.setTesteeCode(projectVisitUser.getTesteeCode());
            projectUserExpand.setTesteeId(testeeId);
            projectUserExpand.setAcronym(projectVisitUser.getAcronym());
            projectUserExpand.setProjectOrgId(projectVisitUser.getOwnerOrgId());
            
            projectUserExpand.setArea(projectVisitUser.getArea());
            projectUserExpand.setGroupInfo(projectVisitUser.getGroupInfo());
            projectUserExpand.setHospital(projectVisitUser.getHospital());
            projectUserExpand.setDept(projectVisitUser.getDept());
            projectUserExpand.setRealName(projectVisitUser.getRealName());
            projectUserExpand.setProjectVolunteer(projectVisitUser.getProjectVolunteer());
            
            if(StringUtils.isNotEmpty(projectUserExpand.getGroupInfo())){
                Dictionary dictionaryGroupInfo = dictionaryService.getDictionaryInfo(projectUserExpand.getGroupInfo());
                if(dictionaryGroupInfo != null){
                    projectUserExpand.setGroupName(dictionaryGroupInfo.getName());
                }
            }
            
            SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectVisitUser.getProjectResearchUserId());
            if(systemUserInfoExtendVo != null){
                if(StringUtils.isNotEmpty(systemUserInfoExtendVo.getEnterprise())){
                    SysDepartmentVo sysDepartmentVo = systemDepartmentService.getDepartment(NumberUtil.parseLong(systemUserInfoExtendVo.getEnterprise()));
                    if(sysDepartmentVo != null){
                        projectUserExpand.setRegionName(sysDepartmentVo.getName());
                    }
                }
            }
            
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                String visitId = projectVisitConfig.getId().toString();
                
                // 患者统计
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitId);
                dynamicColumnHeadRowData.setVisitName(projectVisitConfig.getVisitName());
                List<FlowFormSet> flowFormSetList = flowFormSetService.getProjectVisitListByVisitId(visitId);
                dynamicColumnHeadRowData.setTotal_count(flowFormSetList.size());
                
                ProjectTesteeProcessCountVo projectTesteeProcessCountVo = this.getProjectTesteeProcessValue(projectId, visitId, testeeId);
                if(projectTesteeProcessCountVo != null){
                    int visitCount = projectTesteeProcessCountVo.getSubmitCount() == projectTesteeProcessCountVo.getTotalCount() ? 1 : 0;
                    dynamicColumnHeadRowData.setSubmit_count(visitCount);
                }
                ProjectTesteeFormAduitCountVo projectTesteeFormAduitCountVo = this.getProjectTesteeFormAuditValue(projectId, visitId, testeeId);
                if(projectTesteeFormAduitCountVo != null){
                    int visitCount = projectTesteeFormAduitCountVo.getCheckCount() == projectTesteeFormAduitCountVo.getTotalCount() ? 1 : 0;
                    dynamicColumnHeadRowData.setCheck_count(visitCount);
                }
                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }
            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }
    
    
    public ProjectTesteeProcessCountVo getProjectTesteeProcessValue(String projectId, String visitId, String testeeId) {
        return projectTesteeInfoMapper.getProjectTesteeProcessValue(projectId, visitId, testeeId);
    }
    
    public ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditValue(String projectId, String visitId, String testeeId) {
        return projectTesteeInfoMapper.getProjectTesteeFormAuditValue(projectId, visitId, testeeId);
    }
    
    
}
