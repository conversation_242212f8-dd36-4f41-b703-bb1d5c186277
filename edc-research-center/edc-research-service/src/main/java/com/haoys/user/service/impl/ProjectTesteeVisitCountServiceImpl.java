package com.haoys.user.service.impl;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectFormAuditMapper;
import com.haoys.user.mapper.ProjectTesteeProcessMapper;
import com.haoys.user.mapper.ProjectTesteeVisitCountMapper;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowFormSetExample;
import com.haoys.user.model.ProjectFormAudit;
import com.haoys.user.model.ProjectFormAuditExample;
import com.haoys.user.model.ProjectTesteeProcess;
import com.haoys.user.model.ProjectTesteeProcessExample;
import com.haoys.user.model.ProjectTesteeVisitCount;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeVisitCountService;
import com.haoys.user.service.ProjectVisitConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目参与者访视统计服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
public class ProjectTesteeVisitCountServiceImpl implements ProjectTesteeVisitCountService {

    @Autowired
    private ProjectTesteeVisitCountMapper projectTesteeVisitCountMapper;

    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;

    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;

    @Autowired
    private FlowFormSetMapper flowFormSetMapper;

    @Autowired
    private ProjectTesteeProcessMapper projectTesteeProcessMapper;

    @Autowired
    private ProjectFormAuditMapper projectFormAuditMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> refreshProjectMedicalReviewList(String projectId) {
        try {
            if (StringUtils.isEmpty(projectId)) {
                return CommonResult.failed("项目ID不能为空");
            }

            Long projectIdLong = Long.valueOf(projectId);
            log.info("开始刷新项目 {} 的医学审核列表统计数据", projectId);

            // 1. 删除项目的旧统计数据
            int deletedCount = projectTesteeVisitCountMapper.deleteByProjectId(projectIdLong);
            log.info("删除项目 {} 的旧统计数据 {} 条", projectId, deletedCount);

            // 2. 获取项目的所有参与者（包含参与者姓名）
            List<Map<String, Object>> testeeList = projectTesteeInfoService.getProjectTesteeUserListWithName(projectId, null);

            if (testeeList.isEmpty()) {
                log.info("项目 {} 没有找到参与者", projectId);
                return CommonResult.success("刷新完成，项目没有参与者");
            }

            // 3. 获取项目的所有访视配置
            List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitConfigListByProjectId(projectId);

            if (visitList.isEmpty()) {
                log.info("项目 {} 没有找到访视配置", projectId);
                return CommonResult.success("刷新完成，项目没有访视配置");
            }

            // 4. 为每个参与者的每个访视计算统计数据
            List<ProjectTesteeVisitCount> statisticsList = new ArrayList<>();
            Date currentTime = new Date();

            for (Map<String, Object> testee : testeeList) {
                for (ProjectVisitConfig visit : visitList) {
                    ProjectTesteeVisitCount statistics = calculateVisitStatistics(
                            projectIdLong, testee, visit, currentTime);
                    if (statistics != null) {
                        statisticsList.add(statistics);
                    }
                }
            }

            // 5. 分批插入统计数据，避免连接泄漏
            if (!statisticsList.isEmpty()) {
                int totalInserted = 0;
                int batchSize = 100; // 每批处理100条记录

                for (int i = 0; i < statisticsList.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, statisticsList.size());
                    List<ProjectTesteeVisitCount> batch = statisticsList.subList(i, endIndex);

                    try {
                        int insertCount = projectTesteeVisitCountMapper.batchInsert(batch);
                        totalInserted += insertCount;
                        log.debug("项目 {} 批次插入 {} 条记录，总计 {}/{}", projectId, insertCount, totalInserted, statisticsList.size());
                    } catch (Exception e) {
                        log.error("项目 {} 批次插入失败，批次大小: {}", projectId, batch.size(), e);
                        throw e;
                    }
                }

                log.info("项目 {} 插入统计数据 {} 条", projectId, totalInserted);
            }

            log.info("项目 {} 医学审核列表统计数据刷新完成", projectId);
            return CommonResult.success("刷新完成，处理了 " + testeeList.size() + " 个参与者，" +
                    visitList.size() + " 个访视，生成 " + statisticsList.size() + " 条统计记录");

        } catch (Exception e) {
            log.error("刷新项目 {} 医学审核列表统计数据失败", projectId, e);
            return CommonResult.failed("刷新失败: " + e.getMessage());
        }
    }

    /**
     * 计算单个参与者单个访视的统计数据
     */
    private ProjectTesteeVisitCount calculateVisitStatistics(Long projectId, Map<String, Object> testee,
            ProjectVisitConfig visit, Date currentTime) {
        try {
            // 从 Map 中获取参与者信息
            Long testeeId = Long.valueOf(testee.get("testee_id").toString());
            String testeeCode = (String) testee.get("testee_code");
            String testeeName = (String) testee.get("testee_name");
            String ownerOrgId = (String) testee.get("owner_org_id");

            // 1. 获取该访视下的所有表单配置
            FlowFormSetExample formSetExample = new FlowFormSetExample();
            formSetExample.createCriteria()
                    .andProjectIdEqualTo(projectId)
                    .andVisitIdEqualTo(visit.getId())
                    .andStatusEqualTo("0"); // 正常状态
            List<FlowFormSet> formSetList = flowFormSetMapper.selectByExample(formSetExample);

            if (formSetList.isEmpty()) {
                log.debug("访视 {} 没有配置表单", visit.getId());
                return null;
            }

            // 2. 统计表单提交状态和审核状态
            int totalForms = formSetList.size();
            int submittedForms = 0;
            int approvedForms = 0;
            int rejectedForms = 0;
            int pendingForms = 0;
            Date lastSubmitTime = null;
            Date lastReviewTime = null;

            for (FlowFormSet formSet : formSetList) {
                // 检查表单提交状态 (status=0为提交，status=-1为暂存状态)
                ProjectTesteeProcessExample processExample = new ProjectTesteeProcessExample();
                processExample.createCriteria()
                        .andProjectIdEqualTo(projectId)
                        .andVisitIdEqualTo(visit.getId())
                        .andFormIdEqualTo(formSet.getFormId())
                        .andTesteeIdEqualTo(testeeId)
                        .andStatusEqualTo("0"); // 提交状态
                List<ProjectTesteeProcess> processList = projectTesteeProcessMapper.selectByExample(processExample);

                if (!processList.isEmpty()) {
                    submittedForms++;
                    // 获取最后提交时间
                    for (ProjectTesteeProcess process : processList) {
                        if (lastSubmitTime == null || (process.getCreateTime() != null &&
                                process.getCreateTime().after(lastSubmitTime))) {
                            lastSubmitTime = process.getCreateTime();
                        }
                    }
                }

                // 检查表单审核状态 (audit_status=0待审核、audit_status=1审核通过、audit_status=-1审核驳回)
                ProjectFormAuditExample auditExample = new ProjectFormAuditExample();
                auditExample.createCriteria()
                        .andProjectIdEqualTo(projectId)
                        .andVisitIdEqualTo(visit.getId())
                        .andFormIdEqualTo(formSet.getFormId())
                        .andTesteeIdEqualTo(testeeId);
                List<ProjectFormAudit> auditList = projectFormAuditMapper.selectByExample(auditExample);

                if (!auditList.isEmpty()) {
                    for (ProjectFormAudit audit : auditList) {
                        if ("1".equals(audit.getAuditStatus())) {
                            approvedForms++;
                        } else if ("-1".equals(audit.getAuditStatus())) {
                            rejectedForms++;
                        } else if ("0".equals(audit.getAuditStatus())) {
                            pendingForms++;
                        }

                        // 获取最后审核时间
                        if (lastReviewTime == null || (audit.getAuditTime() != null &&
                                audit.getAuditTime().after(lastReviewTime))) {
                            lastReviewTime = audit.getAuditTime();
                        }
                    }
                }
            }

            // 3. 计算访视状态
            // 当访视下的所有表单都是提交状态则访视状态为已提交，否则是未提交
            String submitStatus = (submittedForms == totalForms) ?
                    ProjectTesteeVisitCount.SUBMIT_STATUS_SUBMITTED :
                    ProjectTesteeVisitCount.SUBMIT_STATUS_NOT_SUBMITTED;

            // 访视下所有的表单审核状态都是审核通过则访视状态为审核通过否则为未审核
            String reviewStatus = (approvedForms == totalForms) ?
                    ProjectTesteeVisitCount.REVIEW_STATUS_APPROVED :
                    ProjectTesteeVisitCount.REVIEW_STATUS_NOT_REVIEWED;

            // 4. 创建统计记录
            ProjectTesteeVisitCount statistics = new ProjectTesteeVisitCount();
            statistics.setId(SnowflakeIdWorker.getUuid());
            statistics.setProjectId(projectId);
            statistics.setPlanId(visit.getPlanId());
            statistics.setVisitId(visit.getId());
            statistics.setTesteeId(testeeId);
            statistics.setTesteeCode(testeeCode);
            statistics.setTesteeName(testeeName);
            statistics.setOwnerOrgId(ownerOrgId);
            statistics.setVisitName(visit.getVisitName());
            statistics.setTotalForms(totalForms);
            statistics.setSubmittedForms(submittedForms);
            statistics.setApprovedForms(approvedForms);
            statistics.setRejectedForms(rejectedForms);
            statistics.setPendingForms(pendingForms);
            statistics.setSubmitStatus(submitStatus);
            statistics.setReviewStatus(reviewStatus);
            statistics.setLastSubmitTime(lastSubmitTime);
            statistics.setLastReviewTime(lastReviewTime);
            statistics.setCreateUserId("SYSTEM");
            statistics.setCreateTime(currentTime);

            return statistics;

        } catch (Exception e) {
            log.error("计算参与者 {} 访视 {} 统计数据失败", testee.get("testee_id"), visit.getId(), e);
            return null;
        }
    }

    @Override
    public CommonResult<List<ProjectTesteeVisitCount>> queryTesteesByConditions(
            String testeeCode, String submitStatus, String reviewStatus, String projectId) {
        try {
            // 使用灵活的查询方式
            Map<String, Object> conditions = new HashMap<>();
            if (StringUtils.isNotEmpty(projectId)) {
                conditions.put("projectId", Long.valueOf(projectId));
            }
            if (StringUtils.isNotEmpty(testeeCode)) {
                conditions.put("testeeCode", testeeCode);
            }
            if (StringUtils.isNotEmpty(submitStatus)) {
                conditions.put("submitStatus", submitStatus);
            }
            if (StringUtils.isNotEmpty(reviewStatus)) {
                conditions.put("reviewStatus", reviewStatus);
            }

            List<ProjectTesteeVisitCount> result = projectTesteeVisitCountMapper.listByMultipleConditions(conditions);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("根据条件查询参与者列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<ProjectTesteeVisitCount> queryUnreviewedTestees(String projectId, String testeeCode) {
        try {
            // 查询未审核的参与者列表：只查询整体访视未提交或者访视已经提交同时未完成审核的访视记录
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("projectId", Long.valueOf(projectId));
            if (StringUtils.isNotEmpty(testeeCode)) {
                conditions.put("testeeCode", testeeCode);
            }
            // 未提交或者已提交但未完成审核
            conditions.put("unreviewedOnly", true);

            return projectTesteeVisitCountMapper.complexQuery(conditions);
        } catch (Exception e) {
            log.error("查询未审核参与者列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public ProjectTesteeVisitCount selectByPrimaryKey(Long id) {
        return projectTesteeVisitCountMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProjectTesteeVisitCount> getMedicalReviewList(String projectId, String testeeCode,
                                                              String submitStatus, String reviewStatus,
                                                              Boolean unreviewedOnly) {
        try {
            Long projectIdLong = Long.valueOf(projectId);
            return projectTesteeVisitCountMapper.getMedicalReviewList(projectIdLong, testeeCode,
                    submitStatus, reviewStatus, unreviewedOnly);
        } catch (Exception e) {
            log.error("获取医学审核管理列表失败，项目ID: {}", projectId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProjectTesteeVisitCount> getMedicalReviewListAll(String projectId, String testeeCode,
                                                                 String submitStatus, String reviewStatus) {
        try {
            Long projectIdLong = Long.valueOf(projectId);
            return projectTesteeVisitCountMapper.getMedicalReviewList(projectIdLong, testeeCode,
                    submitStatus, reviewStatus, false);
        } catch (Exception e) {
            log.error("获取医学审核管理列表（全部）失败，项目ID: {}", projectId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int insert(ProjectTesteeVisitCount record) {
        return projectTesteeVisitCountMapper.insert(record);
    }

    @Override
    public int insertSelective(ProjectTesteeVisitCount record) {
        return projectTesteeVisitCountMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectTesteeVisitCount record) {
        return projectTesteeVisitCountMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteByProjectId(Long projectId) {
        return projectTesteeVisitCountMapper.deleteByProjectId(projectId);
    }

    @Override
    public int batchInsert(List<ProjectTesteeVisitCount> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        return projectTesteeVisitCountMapper.batchInsert(list);
    }

    @Override
    public int batchUpdate(List<ProjectTesteeVisitCount> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        return projectTesteeVisitCountMapper.batchUpdate(list);
    }
}
