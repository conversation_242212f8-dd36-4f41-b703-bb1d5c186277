<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="bdp_user_id" jdbcType="VARCHAR" property="bdpUserId" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="acronym" jdbcType="VARCHAR" property="acronym" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="idcard" jdbcType="VARCHAR" property="idcard" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="contant" jdbcType="VARCHAR" property="contant" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="height" jdbcType="DOUBLE" property="height" />
    <result column="weight" jdbcType="DOUBLE" property="weight" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="input_value1" jdbcType="VARCHAR" property="inputValue1" />
    <result column="input_value2" jdbcType="VARCHAR" property="inputValue2" />
    <result column="input_value3" jdbcType="VARCHAR" property="inputValue3" />
    <result column="input_value4" jdbcType="VARCHAR" property="inputValue4" />
    <result column="input_value5" jdbcType="VARCHAR" property="inputValue5" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, bdp_user_id, real_name, acronym, code, gender, idcard, birthday, contant, 
    age, height, weight, status, expand, input_value1, input_value2, input_value3, input_value4, 
    input_value5, create_time, update_time, create_user, update_user, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeInfoExample">
    delete from project_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeInfo">
    insert into project_testee_info (id, user_id, bdp_user_id, 
      real_name, acronym, code, 
      gender, idcard, birthday, 
      contant, age, height, 
      weight, status, expand, 
      input_value1, input_value2, input_value3, 
      input_value4, input_value5, create_time, 
      update_time, create_user, update_user, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{bdpUserId,jdbcType=VARCHAR}, 
      #{realName,jdbcType=VARCHAR}, #{acronym,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{gender,jdbcType=VARCHAR}, #{idcard,jdbcType=VARCHAR}, #{birthday,jdbcType=TIMESTAMP}, 
      #{contant,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER}, #{height,jdbcType=DOUBLE}, 
      #{weight,jdbcType=DOUBLE}, #{status,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{inputValue1,jdbcType=VARCHAR}, #{inputValue2,jdbcType=VARCHAR}, #{inputValue3,jdbcType=VARCHAR}, 
      #{inputValue4,jdbcType=VARCHAR}, #{inputValue5,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeInfo">
    insert into project_testee_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="bdpUserId != null">
        bdp_user_id,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="acronym != null">
        acronym,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="idcard != null">
        idcard,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="contant != null">
        contant,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="inputValue1 != null">
        input_value1,
      </if>
      <if test="inputValue2 != null">
        input_value2,
      </if>
      <if test="inputValue3 != null">
        input_value3,
      </if>
      <if test="inputValue4 != null">
        input_value4,
      </if>
      <if test="inputValue5 != null">
        input_value5,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="bdpUserId != null">
        #{bdpUserId,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="idcard != null">
        #{idcard,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="contant != null">
        #{contant,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="inputValue1 != null">
        #{inputValue1,jdbcType=VARCHAR},
      </if>
      <if test="inputValue2 != null">
        #{inputValue2,jdbcType=VARCHAR},
      </if>
      <if test="inputValue3 != null">
        #{inputValue3,jdbcType=VARCHAR},
      </if>
      <if test="inputValue4 != null">
        #{inputValue4,jdbcType=VARCHAR},
      </if>
      <if test="inputValue5 != null">
        #{inputValue5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeInfoExample" resultType="java.lang.Long">
    select count(*) from project_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.bdpUserId != null">
        bdp_user_id = #{record.bdpUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.acronym != null">
        acronym = #{record.acronym,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.idcard != null">
        idcard = #{record.idcard,jdbcType=VARCHAR},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contant != null">
        contant = #{record.contant,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        age = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=DOUBLE},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=DOUBLE},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue1 != null">
        input_value1 = #{record.inputValue1,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue2 != null">
        input_value2 = #{record.inputValue2,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue3 != null">
        input_value3 = #{record.inputValue3,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue4 != null">
        input_value4 = #{record.inputValue4,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue5 != null">
        input_value5 = #{record.inputValue5,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_info
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      bdp_user_id = #{record.bdpUserId,jdbcType=VARCHAR},
      real_name = #{record.realName,jdbcType=VARCHAR},
      acronym = #{record.acronym,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=VARCHAR},
      idcard = #{record.idcard,jdbcType=VARCHAR},
      birthday = #{record.birthday,jdbcType=TIMESTAMP},
      contant = #{record.contant,jdbcType=VARCHAR},
      age = #{record.age,jdbcType=INTEGER},
      height = #{record.height,jdbcType=DOUBLE},
      weight = #{record.weight,jdbcType=DOUBLE},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      input_value1 = #{record.inputValue1,jdbcType=VARCHAR},
      input_value2 = #{record.inputValue2,jdbcType=VARCHAR},
      input_value3 = #{record.inputValue3,jdbcType=VARCHAR},
      input_value4 = #{record.inputValue4,jdbcType=VARCHAR},
      input_value5 = #{record.inputValue5,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeInfo">
    update project_testee_info
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="bdpUserId != null">
        bdp_user_id = #{bdpUserId,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        acronym = #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="idcard != null">
        idcard = #{idcard,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="contant != null">
        contant = #{contant,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="inputValue1 != null">
        input_value1 = #{inputValue1,jdbcType=VARCHAR},
      </if>
      <if test="inputValue2 != null">
        input_value2 = #{inputValue2,jdbcType=VARCHAR},
      </if>
      <if test="inputValue3 != null">
        input_value3 = #{inputValue3,jdbcType=VARCHAR},
      </if>
      <if test="inputValue4 != null">
        input_value4 = #{inputValue4,jdbcType=VARCHAR},
      </if>
      <if test="inputValue5 != null">
        input_value5 = #{inputValue5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeInfo">
    update project_testee_info
    set user_id = #{userId,jdbcType=BIGINT},
      bdp_user_id = #{bdpUserId,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      acronym = #{acronym,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=VARCHAR},
      idcard = #{idcard,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=TIMESTAMP},
      contant = #{contant,jdbcType=VARCHAR},
      age = #{age,jdbcType=INTEGER},
      height = #{height,jdbcType=DOUBLE},
      weight = #{weight,jdbcType=DOUBLE},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      input_value1 = #{inputValue1,jdbcType=VARCHAR},
      input_value2 = #{inputValue2,jdbcType=VARCHAR},
      input_value3 = #{inputValue3,jdbcType=VARCHAR},
      input_value4 = #{inputValue4,jdbcType=VARCHAR},
      input_value5 = #{inputValue5,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!--查询受试者基本信息列表 受试者审核列表-->
  <select id="getProjectTesteeListForPage" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo">
    SELECT
      project_testee_info.id,
      project_testee_info.real_name realName,
      project_testee_info.acronym,
      project_testee_info.gender,
      project_visit_user.testee_code code,
      project_visit_user.testee_code testeeCode,
      project_visit_user.owner_org_id ownerOrgId,
      project_visit_user.owner_org_name ownerOrgName,
      project_visit_user.owner_doctor_id ownerDoctorId,
      project_visit_user.status,
      project_visit_user.testee_config testeeConfig,
      project_visit_user.review_status reviewStatus,
      project_visit_user.review_user_id reviewUserId,
      project_visit_user.review_time reviewTime,
      project_visit_user.self_record selfRecord,
      project_visit_user.research_status researchStatus
    FROM
        project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId} and project_visit_user.status = '0'
    <if test="realName != null">
      AND project_testee_info.real_name LIKE CONCAT('%',#{realName},'%')
    </if>
    <if test="orgId != null">
      AND project_visit_user.owner_org_id IN (${orgId})
    </if>
    <if test="ownerDoctor != null">
      AND project_visit_user.owner_doctor_id IN (${ownerDoctorId})
    </if>
    <if test="code != null">
      AND project_visit_user.testee_code  like concat('%', #{code}, '%')
    </if>
    <if test="status != null">
      AND project_visit_user.status = #{status}
    </if>
    <if test="researchStatus != null">
     and project_visit_user.research_status = #{researchStatus}
    </if>
    <if test="reviewFlag != null">
      AND project_visit_user.review_flag = #{reviewFlag}
    </if>
    <if test="reviewStatus != null">
      AND (project_visit_user.review_status = #{reviewStatus} /*OR project_visit_user.self_record = true*/)
    </if>
    <if test="reviewStatusValue != null">
      AND project_visit_user.review_status = #{reviewStatusValue}
    </if>
    ORDER BY ${sortField} ${sortType}
  </select>

  <!-- 数据分析平台样本数据列表 -->
  <select id="getProjectTesteeAnalysisListByProjectId" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeVo">
    SELECT
      project_testee_info.id,
      project_testee_info.idcard,
      TIMESTAMPDIFF(YEAR, project_testee_info.birthday, CURDATE()) AS age,
      project_testee_info.real_name realName,
      project_testee_info.gender,
      project_testee_info.contant,
      project_testee_info.height,
      project_testee_info.weight,
      project_testee_info.expand,
      project_visit_user.testee_code testeeCode,
      project_visit_user.owner_org_id ownerOrgId,
      project_visit_user.owner_org_name ownerOrgName,
      project_visit_user.owner_doctor_id ownerDoctorId,
      project_visit_user.status
    FROM
        project_testee_info
    LEFT JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId} AND project_visit_user.status = '0'
    <if test="orgId != null and orgId != ''">
      AND project_visit_user.owner_org_id IN (${orgId})
    </if>
    ORDER BY project_testee_info.create_time desc
  </select>

  <!--根据项目id和编号查询受试者-->
  <select id="getTesteeCodeByProjectId" resultType="com.haoys.user.model.ProjectTesteeInfo">
    SELECT project_testee_info.id,
           <!--project_testee_info.code,-->
           project_testee_info.real_name realName,
           project_testee_info.gender,
           project_testee_info.contant,
           project_testee_info.height,
           project_testee_info.weight,
           project_testee_info.expand,
           project_visit_user.testee_code code,
           project_visit_user.research_status researchStatus
    FROM project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId} AND project_visit_user.testee_code = #{testeeCode}
    <if test="ownerOrgId != null">
      and owner_org_id = #{ownerOrgId}
    </if>
  </select>

  <!--查询参与者分页列表-->
  <select id="getProjectParticipantListForPage" resultType="com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo">
    SELECT
      project_testee_info.id,
      project_testee_info.code,
      project_testee_info.real_name realName,
      project_testee_info.acronym,
      project_visit_user.project_id projectId,
      project_visit_user.testee_code testeeCode,
      project_visit_user.visit_card_no visitCardNo,
      project_visit_user.informed_date informedDate,
      project_visit_user.owner_org_id ownerOrgId,
      project_visit_user.owner_org_name ownerOrgName,
      project_visit_user.owner_doctor_id ownerDoctorId,
      project_visit_user.resource_form_id resourceFormId,
      project_visit_user.status,
      project_visit_user.research_status researchStatus,
      project_visit_user.self_record selfRecord,
      project_visit_user.testee_config testeeConfig,
      project_visit_user.review_status reviewStatus,
      project_visit_user.review_user_id reviewUserId,
      project_visit_user.review_time reviewTime,
      project_visit_user.create_time createTime
    FROM
        project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId}
    and project_visit_user.status = #{status}
    <if test="testeeCode != null and testeeCode !=''">
      AND project_visit_user.testee_code LIKE CONCAT('%',#{testeeCode},'%')
    </if>
    <if test="realName != null and realName != ''">
      AND project_testee_info.real_name LIKE CONCAT('%',#{realName},'%')
    </if>

    <if test="inheritor != null and inheritor != ''">
      AND project_testee_info.input_value1 LIKE CONCAT('%',#{inheritor},'%')
    </if>
    <if test="Instructor != null and Instructor != ''">
      AND project_testee_info.input_value2 = #{Instructor}
    </if>
    <if test="Batchnumber != null and Batchnumber != ''">
      AND project_testee_info.input_value3 = #{Batchnumber}
    </if>
    <if test="REDATE != null and REDATE != ''">
      AND project_testee_info.input_value4 = #{REDATE}
    </if>

    <if test="projectOrgId != null and projectOrgId !=''">
      AND project_visit_user.owner_org_id = #{projectOrgId}
    </if>
    <if test="ownerDoctorId != null and ownerDoctorId !=''">
      AND project_visit_user.create_user_id = #{ownerDoctorId}
    </if>
    <if test="researchStatus != null and researchStatus !=''">
      AND project_visit_user.research_status = #{researchStatus}
    </if>
    ORDER BY ${sortField} ${sortType}
  </select>


  <!--根据手机号查询受试者信息-->
  <select id="getTesteeInfoByMobile" resultMap="BaseResultMap">
    SELECT * from project_testee_info where 1 = 1 and contant = #{mobile} and status = '0' limit 1
  </select>

  <!--受试者性别统计-->
  <select id="getProjectGenderData" resultType="java.util.Map">
    SELECT
      project_testee_info.gender,
      COUNT( project_testee_info.gender ) count
    FROM
        project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE 1 = 1 AND project_visit_user.review_status = '013002'
    <if test="orgIds != null and orgIds != ''">
      AND project_visit_user.owner_org_id IN (${orgIds})
    </if>
    AND project_visit_user.project_id = #{projectId}
    GROUP BY
    project_testee_info.gender
  </select>

  <!--受试者年龄分布统计-->
  <select id="getProjectAgeDistrbuteData" resultType="java.util.Map">
    SELECT
    NAME,
    COUNT(NAME) AS count
    FROM
    (
    SELECT
      CASE
      WHEN age > 60 THEN '老年'
      WHEN age BETWEEN 45 AND 59 THEN '中年'
      WHEN age BETWEEN 18 AND 44 THEN '青年'
      WHEN age BETWEEN 12 AND 17 THEN '青少年'
      WHEN age BETWEEN 0 AND 11 THEN '儿童'
      END AS NAME
    FROM
    (
      select
        project_testee_info.birthday,
        TIMESTAMPDIFF(YEAR,birthday,CURDATE()) AS age
        <!--YEAR(CURDATE())-YEAR(birthday)-(RIGHT(CURDATE(),5)<RIGHT(birthday,5)) AS age-->
      FROM
      project_testee_info INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      WHERE project_testee_info.birthday IS NOT NULL AND project_visit_user.review_status = '013002'
      <if test="orgIds != null and orgIds != ''">
        AND project_visit_user.owner_org_id IN (${orgIds})
      </if>
      AND project_visit_user.project_id = #{projectId}
    )t
    )t GROUP BY NAME ORDER BY NAME


  </select>


  <!--查询受试者总量-->
  <select id="getProjectTesteeCount" resultType="java.lang.Long">
    SELECT
    IFNULL(count(*), 0) count
    FROM
    (
      SELECT
      project_visit_user.project_id, project_visit_user.owner_org_id,project_visit_user.testee_id
      FROM project_testee_info INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      WHERE project_visit_user.project_id = #{projectId} AND project_visit_user.review_status = '013002'
      AND project_testee_info.create_time BETWEEN date_add(curdate(), interval - day(curdate()) + 1 day) AND NOW()
      <if test="orgIds != null and orgIds != ''">
        AND project_visit_user.owner_org_id IN (${orgIds})
      </if>
      <if test="customOrgId != null and customOrgId != ''">
        AND project_visit_user.owner_org_id = #{customOrgId}
      </if>
      GROUP BY project_id, testee_id
    ) t
    WHERE t.project_id = #{projectId}
    GROUP BY t.project_id
  </select>

  <!--受试者访视统计-->
  <select id="list" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo">
    SELECT
      pt.real_name as realName,
      pt.code,
      pt.id,
      pt.owner_org_id as ownerOrgId ,
      soi.org_name AS ownerOrgName,
      su.real_name as ownerDoctorName,
      pvc.visit_name AS visitName,
      pvc.id AS visitId,
      pvc.project_id AS projectId,
      pvtr.follow_up_next_time as planViewDate
    FROM
    project_testee_info pt
    INNER JOIN system_org_info soi on soi.org_id=pt.owner_org_id
    INNER JOIN project_visit_user pvu ON pvu.testee_id = pt.id and pvu.review_status='013002'
    INNER JOIN project_visit_config pvc ON pvc.project_id = pvu.project_id and pvc.status=0
    left join system_user_info su on su.id= pt.owner_doctor_id
    left join project_visit_testee_record pvtr on pvtr.visit_id= pvc.id and pvtr.testee_id=pt.id  and pvtr.project_id=pvu.project_id
    WHERE  pvu.project_id = #{projectId} AND pt.owner_org_id IN

    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    <if test="code != null and code != ''">
      AND instr(pt.code,#{code})>0
    </if>
    <if test="realName != null and realName != ''">
      AND instr(pt.real_name,#{realName})>0
    </if>
    <if test="ownerOrgId != null and ownerOrgId != ''">
      and pt.owner_org_id=#{ownerOrgId}
    </if>
    <if test="ownerDoctor != null and ownerDoctor != ''">
      AND pt.owner_doctor_id= #{ownerDoctor}
    </if>
    <if test="visitName != null and visitName != ''">
      AND instr(pvc.visit_name,#{visitName})>0
    </if>
    <if test="startDate != null and startDate != ''">
      AND pvtr.follow_up_next_time BETWEEN #{startDate} and #{endDate}
    </if>
    GROUP BY
    pvc.id,
    pvu.testee_id
    order by pt.create_time desc,pvc.sort
  </select>

  <!--根据testeeCode查询受试者基本信息 返回受试者审核状态、绑定状态、绑定结果、绑定来源-->
  <select id="getTesteeBaseInfoByTesteeCode" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeVo">
    SELECT project_testee_info.id,
           project_testee_info.code,
           project_testee_info.real_name realName,
           project_testee_info.gender,
           project_testee_info.contant,
           project_testee_info.height,
           project_testee_info.weight,
           project_testee_info.expand,
           project_visit_user.owner_org_id   ownerOrgId,
           project_visit_user.owner_org_name ownerOrgName,
           project_visit_user.owner_doctor_id   ownerDoctorId,
           project_visit_user.status,
           project_visit_user.self_record selfRecord,
           project_visit_user.review_status reviewStatus,
           project_visit_user.bind_resource bindResource,
           project_visit_user.bind_result bindResult
    FROM project_testee_info
           INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId} AND project_testee_info.code = #{testeeCode} LIMIT 1
  </select>

  <select id="getOrgIdsByTesteeIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from project_testee_info where id in
    <foreach collection="testeeIds" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    group by project_visit_user.owner_org_id
  </select>

  <select id="getProjectTesteeListByIds" resultMap="BaseResultMap">
    select  a.id , a.real_name, b.testee_code as code
    from project_testee_info a join project_visit_user b on a.id=b.testee_id where
    b.project_id = #{projectId} and a.id in
    <foreach collection="testeeIds" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    <if test="projectOrgId != null">
      AND b.owner_org_id=#{projectOrgId}
    </if>
    ORDER BY CONVERT(b.testee_code USING gbk) ASC
  </select>

  <select id="getProjectTesteeBaseInfoForVersion2" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeVo">
    SELECT
       project_testee_info.id,
       project_testee_info.real_name realName,
       project_testee_info.gender,
       project_testee_info.contant,
       project_testee_info.height,
       project_testee_info.weight,
       project_testee_info.expand,
       project_visit_user.testee_code testeeCode,
       project_visit_user.visit_card_no visitCardNo,
       project_visit_user.informed_date informedDate,
       project_visit_user.owner_org_id   ownerOrgId,
       project_visit_user.owner_org_name ownerOrgName,
       project_visit_user.owner_doctor_id  ownerDoctorId,
       project_visit_user.research_status researchStatus,
       project_visit_user.self_record selfRecord,
       project_visit_user.review_status reviewStatus,
       project_visit_user.bind_resource bindResource,
       project_visit_user.bind_result bindResult
    FROM project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    WHERE project_visit_user.project_id = #{projectId} AND project_visit_user.testee_id = #{testeeId}
      AND project_visit_user.status = '0'
  </select>

  <!--通过用户id查询受试者基本信息-->
  <select id="selectProjectTesteeInfoByUserId" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeBaseVo">
    select <include refid="Base_Column_List" /> from project_testee_info where user_id = #{userId} and tenant_id = #{tenantId} and platform_id = #{platformId}
  </select>

  <!--查询患者加入项目的研究中心列表-->
  <select id="getProjectTesteeBaseInfoByUserId" resultType="com.haoys.user.domain.vo.project.ProjectTesteeOrgVo">
    SELECT
      project_testee_info.id,
      project_testee_info.real_name realName,
      project_testee_info.acronym,
      project_visit_user.testee_code testeeCode,
      project_org_info.id projectOrgId,
      system_org_info.org_name projectOrgName
    FROM project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    INNER JOIN project_org_info on project_visit_user.owner_org_id = project_org_info.id
    INNER JOIN system_org_info on system_org_info.org_id = project_org_info.org_id
    WHERE project_visit_user.project_id = #{projectId} AND project_testee_info.user_id = #{userId}
  </select>

  <select id="getProjectTesteeProcessCount" resultType="com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo">
    select count(*) submitCount from (
      select
          t1.project_id,t1.visit_id, t2.totalCount, t1.submitCount
        from
        ( select
          project_id, visit_id, testee_id,SUM(IF(`status`='0',1,0)) submitCount
        from project_testee_process where project_id = #{projectId} and visit_id = #{visitId} and testee_id = #{testeeId} and `status`='0' group by project_id, visit_id
      ) t1
    inner join
    (
      select project_id, visit_id, count(visit_id) totalCount from flow_form_set where project_id = #{projectId} and visit_id = #{visitId} and `status` = '0'
    ) t2 on t1.visit_id = t2.visit_id and t2.totalCount = t1.submitCount) t
  </select>

  <select id="getProjectTesteeFormAuditCount" resultType="com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo">
    select count(*) checkCount from (
      select
        t1.project_id,t1.visit_id, t2.totalCount, t1.checkCount
    from
    ( select
      project_id, visit_id, testee_id,SUM(IF(`audit_status`='1',1,0)) checkCount
      from project_form_audit where project_id = #{projectId} and visit_id = #{visitId} and testee_id = #{testeeId} and `status`='0' group by project_id, visit_id
    ) t1
    inner join
    (
      select project_id, visit_id, count(visit_id) totalCount from flow_form_set where project_id = #{projectId} and visit_id = #{visitId} and `status` = '0'
    ) t2 on t1.visit_id = t2.visit_id and t2.totalCount = t1.checkCount) t
  </select>

  <select id="getProjectTesteeInputCount" resultType="int">
    select
      count(project_visit_user.testee_id) count
    from project_visit_user where project_visit_user.project_id = #{projectId} and status = '0' and project_visit_user.create_user_id = #{createUserId}
  </select>

  <select id="getProjectTesteeProcessValue" resultType="com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo">
    select
      t1.project_id,t1.visit_id, t2.totalCount, t1.submitCount
    from
    ( select
        project_id, visit_id, testee_id,SUM(IF(`status`='0',1,0)) submitCount
      from project_testee_process where project_id = #{projectId} and visit_id = #{visitId} and testee_id = #{testeeId} and `status`='0' group by project_id, visit_id
    ) t1
    inner join
    (
      select project_id, visit_id, count(visit_id) totalCount from flow_form_set where project_id = #{projectId} and visit_id = #{visitId} and `status` = '0'
    ) t2 on t1.visit_id = t2.visit_id
  </select>

  <select id="getProjectTesteeFormAuditValue" resultType="com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo">
    select t1.project_id,t1.visit_id, t2.totalCount, t1.checkCount from
    ( select
        project_id, visit_id, testee_id,SUM(IF(`audit_status`='1',1,0)) checkCount
        from project_form_audit where project_id = #{projectId} and visit_id = #{visitId} and testee_id = #{testeeId} and `status`='0' group by project_id, visit_id
    ) t1
    inner join
    (
      select project_id, visit_id, count(visit_id) totalCount from flow_form_set where project_id = #{projectId} and visit_id = #{visitId} and `status` = '0'
    ) t2 on t1.visit_id = t2.visit_id
  </select>

  <select id="getProjectTesteeUserListForBoRui" resultType="com.haoys.user.domain.expand.ProjectVisitUserExpand">
    select
      project_visit_user.testee_id,project_visit_user.testee_code,project_testee_info.acronym,
      project_researchers_info.area,project_researchers_info.group_info,project_researchers_info.hospital,
      project_researchers_info.dept,project_researchers_info.`name` realName,project_researchers_info.project_volunteer,
      project_researchers_info.create_user projectResearchUserId
    from
      project_visit_user inner join project_testee_info on project_testee_info.id = project_visit_user.testee_id and project_testee_info.`status` = '0'
    left join project_researchers_info on project_researchers_info.create_user COLLATE utf8mb4_0900_ai_ci = project_visit_user.create_user_id COLLATE utf8mb4_0900_ai_ci
    where project_visit_user.project_id = #{projectId}
    <if test="testeeCode != null and testeeCode != ''">
      and project_visit_user.testee_code = #{testeeCode}
    </if>
    <if test="realName != null and realName != ''">
      and project_researchers_info.name LIKE CONCAT('%',#{realName},'%')
    </if>
  </select>

  <select id="getProjectUncheckTesteeUserList" resultType="com.haoys.user.model.ProjectVisitUser">
    select
      project_visit_user.project_id,project_visit_user.testee_id, project_visit_user.testee_code,project_visit_user.research_status,
      project_testee_process.visit_id,project_testee_process.form_id, project_testee_process.`status`,project_testee_process.create_time,
      project_form_audit.audit_status,project_form_audit.audit_time,project_form_audit.audit_user_id
    from project_visit_user
    left join project_testee_process on project_visit_user.project_id = project_testee_process.project_id
    and project_visit_user.testee_id = project_testee_process.testee_id
    left join project_form_audit on project_testee_process.project_id = project_form_audit.project_id
    and project_form_audit.visit_id = project_testee_process.visit_id
    and project_form_audit.form_id = project_testee_process.form_id
    and project_testee_process.testee_id = project_form_audit.testee_id
    where project_visit_user.project_id = #{projectId} and project_form_audit.audit_status IN ('-1', '0')
    <if test="testeeCode != null and testeeCode != ''">
      and project_visit_user.testee_code = #{testeeCode}
    </if>
    group by project_visit_user.project_id,project_visit_user.testee_id
  </select>

  <select id="getProjectResearchOwnerTesteeList" resultType="com.haoys.user.model.ProjectVisitUser">
    select project_visit_user.project_id,project_visit_user.testee_id, project_visit_user.testee_code,project_visit_user.research_status from project_visit_user
    where project_visit_user.project_id = #{projectId} and project_visit_user.create_user_id = #{createUserId} and project_visit_user.status = '0'
  </select>

  <!-- 获取项目参与者列表（包含参与者姓名）- 用于访视统计 -->
  <select id="getProjectTesteeUserListWithName" resultType="java.util.Map">
    SELECT
        pvu.project_id,
        pvu.testee_id,
        pvu.testee_code,
        pti.real_name as testee_name,
        pvu.owner_org_id,
        pvu.owner_org_name,
        pvu.owner_doctor_id,
        pvu.status,
        pvu.research_status,
        pvu.create_time,
        pvu.create_user_id,
        pvu.tenant_id,
        pvu.platform_id
    FROM project_visit_user pvu
    LEFT JOIN project_testee_info pti ON pvu.testee_id = pti.id
    WHERE pvu.project_id = #{projectId}
    AND pvu.status = '0'
    <if test="testeeCode != null and testeeCode != ''">
        AND pvu.testee_code = #{testeeCode}
    </if>
    ORDER BY pvu.testee_code
  </select>

</mapper>