<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeVisitCountMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeVisitCount">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="plan_id" jdbcType="BIGINT" property="planId" />
        <result column="visit_id" jdbcType="BIGINT" property="visitId" />
        <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
        <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
        <result column="testee_name" jdbcType="VARCHAR" property="testeeName" />
        <result column="owner_org_id" jdbcType="VARCHAR" property="ownerOrgId" />
        <result column="complate_status" jdbcType="VARCHAR" property="complateStatus" />
        <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
        <result column="visit_name" jdbcType="VARCHAR" property="visitName" />
        <result column="total_forms" jdbcType="INTEGER" property="totalForms" />
        <result column="submitted_forms" jdbcType="INTEGER" property="submittedForms" />
        <result column="approved_forms" jdbcType="INTEGER" property="approvedForms" />
        <result column="rejected_forms" jdbcType="INTEGER" property="rejectedForms" />
        <result column="pending_forms" jdbcType="INTEGER" property="pendingForms" />
        <result column="submit_status" jdbcType="VARCHAR" property="submitStatus" />
        <result column="review_status" jdbcType="VARCHAR" property="reviewStatus" />
        <result column="last_visit_time" jdbcType="TIMESTAMP" property="lastVisitTime" />
        <result column="last_submit_time" jdbcType="TIMESTAMP" property="lastSubmitTime" />
        <result column="last_review_time" jdbcType="TIMESTAMP" property="lastReviewTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_id, plan_id, visit_id, testee_id, testee_code, testee_name, owner_org_id,
        complate_status, audit_status, visit_name, total_forms, submitted_forms, approved_forms,
        rejected_forms, pending_forms, submit_status, review_status, last_visit_time,
        last_submit_time, last_review_time, create_user_id, create_time, tenant_id, platform_id
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        where id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeVisitCount">
        insert into project_testee_visit_count (id, project_id, plan_id, 
          visit_id, testee_id, testee_code, 
          testee_name, owner_org_id, complate_status, 
          audit_status, visit_name, total_forms, 
          submitted_forms, approved_forms, rejected_forms, 
          pending_forms, submit_status, review_status, 
          last_visit_time, last_submit_time, last_review_time, 
          create_user_id, create_time, tenant_id, 
          platform_id)
        values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
          #{visitId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, #{testeeCode,jdbcType=VARCHAR}, 
          #{testeeName,jdbcType=VARCHAR}, #{ownerOrgId,jdbcType=VARCHAR}, #{complateStatus,jdbcType=VARCHAR}, 
          #{auditStatus,jdbcType=VARCHAR}, #{visitName,jdbcType=VARCHAR}, #{totalForms,jdbcType=INTEGER}, 
          #{submittedForms,jdbcType=INTEGER}, #{approvedForms,jdbcType=INTEGER}, #{rejectedForms,jdbcType=INTEGER}, 
          #{pendingForms,jdbcType=INTEGER}, #{submitStatus,jdbcType=VARCHAR}, #{reviewStatus,jdbcType=VARCHAR}, 
          #{lastVisitTime,jdbcType=TIMESTAMP}, #{lastSubmitTime,jdbcType=TIMESTAMP}, #{lastReviewTime,jdbcType=TIMESTAMP}, 
          #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
          #{platformId,jdbcType=VARCHAR})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeVisitCount">
        insert into project_testee_visit_count
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="planId != null">
                plan_id,
            </if>
            <if test="visitId != null">
                visit_id,
            </if>
            <if test="testeeId != null">
                testee_id,
            </if>
            <if test="testeeCode != null">
                testee_code,
            </if>
            <if test="testeeName != null">
                testee_name,
            </if>
            <if test="ownerOrgId != null">
                owner_org_id,
            </if>
            <if test="complateStatus != null">
                complate_status,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="visitName != null">
                visit_name,
            </if>
            <if test="totalForms != null">
                total_forms,
            </if>
            <if test="submittedForms != null">
                submitted_forms,
            </if>
            <if test="approvedForms != null">
                approved_forms,
            </if>
            <if test="rejectedForms != null">
                rejected_forms,
            </if>
            <if test="pendingForms != null">
                pending_forms,
            </if>
            <if test="submitStatus != null">
                submit_status,
            </if>
            <if test="reviewStatus != null">
                review_status,
            </if>
            <if test="lastVisitTime != null">
                last_visit_time,
            </if>
            <if test="lastSubmitTime != null">
                last_submit_time,
            </if>
            <if test="lastReviewTime != null">
                last_review_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="platformId != null">
                platform_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="planId != null">
                #{planId,jdbcType=BIGINT},
            </if>
            <if test="visitId != null">
                #{visitId,jdbcType=BIGINT},
            </if>
            <if test="testeeId != null">
                #{testeeId,jdbcType=BIGINT},
            </if>
            <if test="testeeCode != null">
                #{testeeCode,jdbcType=VARCHAR},
            </if>
            <if test="testeeName != null">
                #{testeeName,jdbcType=VARCHAR},
            </if>
            <if test="ownerOrgId != null">
                #{ownerOrgId,jdbcType=VARCHAR},
            </if>
            <if test="complateStatus != null">
                #{complateStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="visitName != null">
                #{visitName,jdbcType=VARCHAR},
            </if>
            <if test="totalForms != null">
                #{totalForms,jdbcType=INTEGER},
            </if>
            <if test="submittedForms != null">
                #{submittedForms,jdbcType=INTEGER},
            </if>
            <if test="approvedForms != null">
                #{approvedForms,jdbcType=INTEGER},
            </if>
            <if test="rejectedForms != null">
                #{rejectedForms,jdbcType=INTEGER},
            </if>
            <if test="pendingForms != null">
                #{pendingForms,jdbcType=INTEGER},
            </if>
            <if test="submitStatus != null">
                #{submitStatus,jdbcType=VARCHAR},
            </if>
            <if test="reviewStatus != null">
                #{reviewStatus,jdbcType=VARCHAR},
            </if>
            <if test="lastVisitTime != null">
                #{lastVisitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastSubmitTime != null">
                #{lastSubmitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastReviewTime != null">
                #{lastReviewTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                #{platformId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 选择性更新记录（根据主键） -->
    <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeVisitCount">
        update project_testee_visit_count
        <set>
            <if test="projectId != null">
                project_id = #{projectId,jdbcType=BIGINT},
            </if>
            <if test="planId != null">
                plan_id = #{planId,jdbcType=BIGINT},
            </if>
            <if test="visitId != null">
                visit_id = #{visitId,jdbcType=BIGINT},
            </if>
            <if test="testeeId != null">
                testee_id = #{testeeId,jdbcType=BIGINT},
            </if>
            <if test="testeeCode != null">
                testee_code = #{testeeCode,jdbcType=VARCHAR},
            </if>
            <if test="testeeName != null">
                testee_name = #{testeeName,jdbcType=VARCHAR},
            </if>
            <if test="ownerOrgId != null">
                owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
            </if>
            <if test="complateStatus != null">
                complate_status = #{complateStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="visitName != null">
                visit_name = #{visitName,jdbcType=VARCHAR},
            </if>
            <if test="totalForms != null">
                total_forms = #{totalForms,jdbcType=INTEGER},
            </if>
            <if test="submittedForms != null">
                submitted_forms = #{submittedForms,jdbcType=INTEGER},
            </if>
            <if test="approvedForms != null">
                approved_forms = #{approvedForms,jdbcType=INTEGER},
            </if>
            <if test="rejectedForms != null">
                rejected_forms = #{rejectedForms,jdbcType=INTEGER},
            </if>
            <if test="pendingForms != null">
                pending_forms = #{pendingForms,jdbcType=INTEGER},
            </if>
            <if test="submitStatus != null">
                submit_status = #{submitStatus,jdbcType=VARCHAR},
            </if>
            <if test="reviewStatus != null">
                review_status = #{reviewStatus,jdbcType=VARCHAR},
            </if>
            <if test="lastVisitTime != null">
                last_visit_time = #{lastVisitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastSubmitTime != null">
                last_submit_time = #{lastSubmitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastReviewTime != null">
                last_review_time = #{lastReviewTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                platform_id = #{platformId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主键更新记录 -->
    <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeVisitCount">
        update project_testee_visit_count
        set project_id = #{projectId,jdbcType=BIGINT},
          plan_id = #{planId,jdbcType=BIGINT},
          visit_id = #{visitId,jdbcType=BIGINT},
          testee_id = #{testeeId,jdbcType=BIGINT},
          testee_code = #{testeeCode,jdbcType=VARCHAR},
          testee_name = #{testeeName,jdbcType=VARCHAR},
          owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
          complate_status = #{complateStatus,jdbcType=VARCHAR},
          audit_status = #{auditStatus,jdbcType=VARCHAR},
          visit_name = #{visitName,jdbcType=VARCHAR},
          total_forms = #{totalForms,jdbcType=INTEGER},
          submitted_forms = #{submittedForms,jdbcType=INTEGER},
          approved_forms = #{approvedForms,jdbcType=INTEGER},
          rejected_forms = #{rejectedForms,jdbcType=INTEGER},
          pending_forms = #{pendingForms,jdbcType=INTEGER},
          submit_status = #{submitStatus,jdbcType=VARCHAR},
          review_status = #{reviewStatus,jdbcType=VARCHAR},
          last_visit_time = #{lastVisitTime,jdbcType=TIMESTAMP},
          last_submit_time = #{lastSubmitTime,jdbcType=TIMESTAMP},
          last_review_time = #{lastReviewTime,jdbcType=TIMESTAMP},
          create_user_id = #{createUserId,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          tenant_id = #{tenantId,jdbcType=VARCHAR},
          platform_id = #{platformId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主键删除记录 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from project_testee_visit_count
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据项目ID删除记录 -->
    <delete id="deleteByProjectId" parameterType="java.lang.Long">
        delete from project_testee_visit_count
        where project_id = #{projectId,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into project_testee_visit_count (
            id, project_id, plan_id, visit_id, testee_id, testee_code, testee_name,
            owner_org_id, complate_status, audit_status, visit_name, total_forms,
            submitted_forms, approved_forms, rejected_forms, pending_forms,
            submit_status, review_status, last_visit_time, last_submit_time,
            last_review_time, create_user_id, create_time, tenant_id, platform_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=BIGINT}, #{item.projectId,jdbcType=BIGINT}, #{item.planId,jdbcType=BIGINT},
                #{item.visitId,jdbcType=BIGINT}, #{item.testeeId,jdbcType=BIGINT},
                #{item.testeeCode,jdbcType=VARCHAR}, #{item.testeeName,jdbcType=VARCHAR},
                #{item.ownerOrgId,jdbcType=VARCHAR}, #{item.complateStatus,jdbcType=VARCHAR},
                #{item.auditStatus,jdbcType=VARCHAR}, #{item.visitName,jdbcType=VARCHAR},
                #{item.totalForms,jdbcType=INTEGER}, #{item.submittedForms,jdbcType=INTEGER},
                #{item.approvedForms,jdbcType=INTEGER}, #{item.rejectedForms,jdbcType=INTEGER},
                #{item.pendingForms,jdbcType=INTEGER}, #{item.submitStatus,jdbcType=VARCHAR},
                #{item.reviewStatus,jdbcType=VARCHAR}, #{item.lastVisitTime,jdbcType=TIMESTAMP},
                #{item.lastSubmitTime,jdbcType=TIMESTAMP}, #{item.lastReviewTime,jdbcType=TIMESTAMP},
                #{item.createUserId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
                #{item.tenantId,jdbcType=VARCHAR}, #{item.platformId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 多条件查询 -->
    <select id="listByMultipleConditions" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        <where>
            <if test="conditions.projectId != null">
                and project_id = #{conditions.projectId,jdbcType=BIGINT}
            </if>
            <if test="conditions.testeeCode != null and conditions.testeeCode != ''">
                and testee_code = #{conditions.testeeCode,jdbcType=VARCHAR}
            </if>
            <if test="conditions.submitStatus != null and conditions.submitStatus != ''">
                and submit_status = #{conditions.submitStatus,jdbcType=VARCHAR}
            </if>
            <if test="conditions.reviewStatus != null and conditions.reviewStatus != ''">
                and review_status = #{conditions.reviewStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 复杂查询 -->
    <select id="complexQuery" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        <where>
            <if test="queryParams.projectId != null">
                and project_id = #{queryParams.projectId,jdbcType=BIGINT}
            </if>
            <if test="queryParams.testeeCode != null and queryParams.testeeCode != ''">
                and testee_code = #{queryParams.testeeCode,jdbcType=VARCHAR}
            </if>
            <!-- 查询未审核的记录：未提交或者已提交但未完成审核 -->
            <if test="queryParams.unreviewedOnly != null and queryParams.unreviewedOnly == true">
                and (submit_status = 'NOT_SUBMITTED' or (submit_status = 'SUBMITTED' and review_status = 'NOT_REVIEWED'))
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 分页复杂查询 -->
    <select id="complexPageQuery" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        <where>
            <if test="queryParams.projectId != null">
                and project_id = #{queryParams.projectId,jdbcType=BIGINT}
            </if>
            <if test="queryParams.testeeCode != null and queryParams.testeeCode != ''">
                and testee_code like concat('%', #{queryParams.testeeCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="queryParams.submitStatus != null and queryParams.submitStatus != ''">
                and submit_status = #{queryParams.submitStatus,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.reviewStatus != null and queryParams.reviewStatus != ''">
                and review_status = #{queryParams.reviewStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 根据条件查询参与者访视统计列表 -->
    <select id="queryTesteesByConditions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        <where>
            <if test="projectId != null">
                and project_id = #{projectId,jdbcType=BIGINT}
            </if>
            <if test="testeeCode != null and testeeCode != ''">
                and testee_code = #{testeeCode,jdbcType=VARCHAR}
            </if>
            <if test="submitStatus != null and submitStatus != ''">
                and submit_status = #{submitStatus,jdbcType=VARCHAR}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and review_status = #{reviewStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 查询未审核的参与者列表 -->
    <select id="queryUnreviewedTestees" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        <where>
            and project_id = #{projectId,jdbcType=BIGINT}
            <if test="testeeCode != null and testeeCode != ''">
                and testee_code = #{testeeCode,jdbcType=VARCHAR}
            </if>
            <!-- 只查询整体访视未提交或者访视已经提交同时未完成审核的访视记录 -->
            and (submit_status = 'NOT_SUBMITTED' or (submit_status = 'SUBMITTED' and review_status = 'NOT_REVIEWED'))
        </where>
        order by create_time desc
    </select>

    <!-- 根据项目ID和参与者ID和访视ID查询记录 -->
    <select id="selectByProjectAndTesteeAndVisit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from project_testee_visit_count
        where project_id = #{projectId,jdbcType=BIGINT}
          and testee_id = #{testeeId,jdbcType=BIGINT}
          and visit_id = #{visitId,jdbcType=BIGINT}
    </select>

    <!-- 统计记录数 -->
    <select id="countByConditions" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(*)
        from project_testee_visit_count
        <where>
            <if test="conditions.projectId != null">
                and project_id = #{conditions.projectId,jdbcType=BIGINT}
            </if>
            <if test="conditions.testeeCode != null and conditions.testeeCode != ''">
                and testee_code = #{conditions.testeeCode,jdbcType=VARCHAR}
            </if>
            <if test="conditions.submitStatus != null and conditions.submitStatus != ''">
                and submit_status = #{conditions.submitStatus,jdbcType=VARCHAR}
            </if>
            <if test="conditions.reviewStatus != null and conditions.reviewStatus != ''">
                and review_status = #{conditions.reviewStatus,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 获取医学审核管理列表 - 高性能查询 -->
    <select id="getMedicalReviewList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_testee_visit_count
        WHERE project_id = #{projectId,jdbcType=BIGINT}
        <if test="testeeCode != null and testeeCode != ''">
            AND testee_code = #{testeeCode,jdbcType=VARCHAR}
        </if>
        <if test="submitStatus != null and submitStatus != ''">
            AND submit_status = #{submitStatus,jdbcType=VARCHAR}
        </if>
        <if test="reviewStatus != null and reviewStatus != ''">
            AND review_status = #{reviewStatus,jdbcType=VARCHAR}
        </if>
        <if test="unreviewedOnly != null and unreviewedOnly == true">
            AND NOT (submit_status = 'NOT_SUBMITTED' OR review_status = 'APPROVED')
        </if>
        ORDER BY testee_code ASC
    </select>

</mapper>
