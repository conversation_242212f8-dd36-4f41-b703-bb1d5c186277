package com.haoys.user.service;

import com.haoys.user.service.impl.ProjectTesteeSignServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 参与者编号生成服务测试
 * 
 * 主要测试并发安全性和编号唯一性
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProjectTesteeSignServiceTest {

    @Autowired
    private ProjectTesteeSignServiceImpl projectTesteeSignService;

    /**
     * 测试参与者编号生成的并发安全性
     * 
     * 验证在高并发情况下不会产生重复编号
     */
    @Test
    public void testGenerateTesteeCodeConcurrency() throws InterruptedException {
        Long projectId = 1L;
        int threadCount = 10;
        int operationsPerThread = 5;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<String> generatedCodes = new HashSet<>();
        
        // 并发执行编号生成
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String code = projectTesteeSignService.getNextTesteeCode(projectId);
                        synchronized (generatedCodes) {
                            generatedCodes.add(code);
                        }
                        
                        // 验证编号格式
                        assertTrue("编号格式应为4位数字", code.matches("\\d{4}"));
                        assertEquals("编号长度应为4位", 4, code.length());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        assertTrue("测试超时", latch.await(30, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 验证结果
        int expectedCount = threadCount * operationsPerThread;
        assertEquals("应该生成" + expectedCount + "个唯一编号", 
            expectedCount, generatedCodes.size());
        
        System.out.println("并发测试完成，生成了 " + generatedCodes.size() + " 个唯一编号");
        System.out.println("生成的编号: " + generatedCodes);
    }

    /**
     * 测试编号格式的正确性
     */
    @Test
    public void testTesteeCodeFormat() {
        Long projectId = 1L;
        
        String code1 = projectTesteeSignService.getNextTesteeCode(projectId);
        String code2 = projectTesteeSignService.getNextTesteeCode(projectId);
        
        // 验证格式
        assertTrue("编号应为4位数字", code1.matches("\\d{4}"));
        assertTrue("编号应为4位数字", code2.matches("\\d{4}"));
        
        // 验证长度
        assertEquals("编号长度应为4", 4, code1.length());
        assertEquals("编号长度应为4", 4, code2.length());
        
        // 验证连续性
        int num1 = Integer.parseInt(code1);
        int num2 = Integer.parseInt(code2);
        assertEquals("编号应该连续", 1, num2 - num1);
        
        System.out.println("编号格式测试通过: " + code1 + " -> " + code2);
    }

    /**
     * 测试异常情况处理
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullProjectId() {
        projectTesteeSignService.getNextTesteeCode(null);
    }

    /**
     * 测试编号的连续性
     */
    @Test
    public void testCodeContinuity() {
        Long projectId = 2L;
        
        // 生成多个编号
        String[] codes = new String[5];
        for (int i = 0; i < codes.length; i++) {
            codes[i] = projectTesteeSignService.getNextTesteeCode(projectId);
        }
        
        // 验证连续性
        for (int i = 1; i < codes.length; i++) {
            int prev = Integer.parseInt(codes[i-1]);
            int curr = Integer.parseInt(codes[i]);
            assertEquals("编号应该连续", 1, curr - prev);
        }
        
        System.out.println("编号连续性测试通过: " + String.join(" -> ", codes));
    }
}
