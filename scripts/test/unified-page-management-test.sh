#!/bin/bash

# 统一页面管理系统测试脚本
# 测试各种管理功能的登录入口和页面跳转

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 基础配置
BASE_URL="http://localhost:8000/api/unified-page-management"
TEST_ACCESS_TOKEN="test_access_token_12345"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    统一页面管理系统功能测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$test_command"; then
        if [ -n "$expected_pattern" ]; then
            if echo "$test_command" | grep -q "$expected_pattern"; then
                echo -e "${GREEN}✓ 通过${NC}"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "${RED}✗ 失败 - 未找到预期模式: $expected_pattern${NC}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "${GREEN}✓ 通过${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi
    else
        echo -e "${RED}✗ 失败${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# 检查项目编译状态
echo -e "${BLUE}1. 项目编译验证${NC}"
run_test "项目编译检查" "mvn clean compile -DskipTests -q"

# 检查Controller文件是否存在
echo -e "${BLUE}2. Controller文件验证${NC}"
run_test "UnifiedPageManagementController文件存在" "test -f edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查关键代码结构
echo -e "${BLUE}3. 代码结构验证${NC}"
run_test "PlatformOssConfig注入检查" "grep -q 'PlatformOssConfig platformOssConfig' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "SecureTokenUtil注入检查" "grep -q 'SecureTokenUtil secureTokenUtil' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "访问日志管理接口检查" "grep -q 'access-log-login' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "Redis管理接口检查" "grep -q 'redis-management-login' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "集群监控接口检查" "grep -q 'cluster-monitor-login' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "定时任务管理接口检查" "grep -q 'quartz-management-login' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查动态URL构建方法
echo -e "${BLUE}4. 动态URL构建验证${NC}"
run_test "buildManagementUrl方法检查" "grep -q 'buildManagementUrl' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "platform.oss.viewUrl使用检查" "grep -q 'platformOssConfig.getOss().getViewUrl()' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查AccessToken验证
echo -e "${BLUE}5. 安全验证机制检查${NC}"
run_test "validateAccessToken方法检查" "grep -q 'validateAccessToken' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "secureTokenUtil.isValidAccessToken使用检查" "grep -q 'secureTokenUtil.isValidAccessToken' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查API注解
echo -e "${BLUE}6. API文档注解验证${NC}"
run_test "Controller注解检查" "grep -q '@Controller' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "RequestMapping注解检查" "grep -q '@RequestMapping(\"/unified-page-management\")' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "ApiOperation注解检查" "grep -q '@ApiOperation' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查日志记录
echo -e "${BLUE}7. 日志记录验证${NC}"
run_test "日志记录检查" "grep -q 'log.info' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

run_test "错误日志检查" "grep -q 'log.warn\\|log.error' edc-research-center/edc-research-api/src/main/java/com/haoys/user/web/controller/system/UnifiedPageManagementController.java"

# 检查配置文件
echo -e "${BLUE}8. 配置文件验证${NC}"
run_test "platform.oss.viewUrl配置检查" "grep -q 'viewUrl:' edc-research-center/edc-research-api/src/main/resources/application-release.yml"

# 模拟HTTP请求测试（需要应用运行）
echo -e "${BLUE}9. HTTP接口测试（需要应用运行）${NC}"
echo -e "${YELLOW}注意：以下测试需要应用正在运行${NC}"

# 检查应用是否运行
if curl -s -f "http://localhost:8000/api/health" > /dev/null 2>&1; then
    echo -e "${GREEN}应用正在运行，执行HTTP测试${NC}"
    
    run_test "访问日志管理登录页面" "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/access-log-login' | grep -q '302\\|200'"
    
    run_test "Redis管理登录页面" "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/redis-management-login' | grep -q '302\\|200'"
    
    run_test "集群监控登录页面" "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/cluster-monitor-login' | grep -q '302\\|200'"
    
    run_test "定时任务管理登录页面" "curl -s -o /dev/null -w '%{http_code}' '$BASE_URL/quartz-management-login' | grep -q '302\\|200'"
    
else
    echo -e "${YELLOW}应用未运行，跳过HTTP测试${NC}"
    echo -e "${YELLOW}要运行HTTP测试，请先启动应用：mvn spring-boot:run${NC}"
fi

# 检查文档
echo -e "${BLUE}10. 文档验证${NC}"
run_test "使用指南文档存在" "test -f docs/unified-page-management-guide.md"

run_test "文档内容完整性检查" "grep -q '统一页面管理系统' docs/unified-page-management-guide.md"

# 输出测试结果
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}           测试结果汇总${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "总测试数: ${TOTAL_TESTS}"
echo -e "${GREEN}通过: ${PASSED_TESTS}${NC}"
echo -e "${RED}失败: ${FAILED_TESTS}${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！统一页面管理系统实现成功！${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关问题${NC}"
    exit 1
fi
